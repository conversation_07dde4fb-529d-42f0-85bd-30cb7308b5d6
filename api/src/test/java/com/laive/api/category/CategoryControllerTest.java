package com.laive.api.category;

import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.laive.api.base.BaseControllerTestCase;
import com.laive.core.domain.Category;
import com.laive.core.domain.VodPrice;
import com.laive.core.service.CategoryManager;
import com.laive.core.service.VodPriceManager;
import com.laive.core.test.TestData;
import com.laive.core.util.JsonUtils;
import com.laive.core.vo.CategoryListVO;

public class CategoryControllerTest extends BaseControllerTestCase {

  @Autowired
  private CategoryController c;

  @Autowired
  private CategoryManager categoryManager;

  @Autowired
  private VodPriceManager vodPriceManager;

  @Test
  public void testList() throws Exception {
    CategoryListVO categoryListVO = new CategoryListVO();
    categoryListVO.setPage(1);
    categoryListVO.setPageSize(10);
    categoryListVO.setPlatformId(TestData.PLATFORM_ID);
    categoryListVO.setCategoryId(TestData.CATEGORY_ID);
    categoryListVO = c.list(categoryListVO);
    log.debug(JsonUtils.toString(categoryListVO));
  }

  @Test
  public void testChildrenWhere() {
    Category category = categoryManager.get("2");
    log.debug(JsonUtils.toString(category));
  }

  @Test
  @Ignore
  public void testPriceHierarch() {
    VodPrice vodPrice = vodPriceManager.get("2c99b3e5530285aa0153028e0fb80001");
    for(VodPrice vp : vodPrice.getSplittedVodPricesOfSVOD()) {
      log.debug(vp.getCategoryName());
    }

    Category category = categoryManager.get("3");
    for (Category pCategory :category.getParentCategories(true))
      log.debug(pCategory.getName());

  }

}
