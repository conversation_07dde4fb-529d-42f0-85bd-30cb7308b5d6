package com.laive.api.product;

import com.laive.api.base.BaseControllerTestCase;
import com.laive.core.test.TestData;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class ProductCommentControllerTest extends BaseControllerTestCase {

  @Autowired
  ProductCommentController c;

  @Test
  public void testCommentWrite() throws Exception {
    setUploginContext("<EMAIL>");
    CommentRequestVO requestVO = new CommentRequestVO();
    requestVO.setProductId(TestData.PRODUCT_VOD_ID);
    requestVO.setTitle("title");
    requestVO.setStarRating(1.3d);
    c.commentWrite(requestVO);
    flushAndClear();
  }
}