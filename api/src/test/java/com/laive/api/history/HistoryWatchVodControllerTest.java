package com.laive.api.history;

import com.laive.api.base.BaseControllerTestCase;
import com.laive.core.test.TestData;
import com.laive.core.util.JsonUtils;
import com.laive.core.vo.WatchHistoryListVO;
import org.apache.cxf.transport.http.HTTPSession;
import org.junit.Before;
import org.junit.Test;

import javax.inject.Inject;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;


public class HistoryWatchVodControllerTest extends BaseControllerTestCase {

  @Inject
  HistoryWatchVodController c;

  @Before
  public void setUp() {
    setUploginContext("<EMAIL>");
  }

  @Test
  public void testWatchVodList() {
    WatchHistoryListVO listVO = new WatchHistoryListVO();
    listVO = c.watchAllHistory(listVO);
    log.debug(JsonUtils.toString(listVO));
  }

  @Test
  public void testWatchVodCreate() {
    String vodId = TestData.VOD_ID;
    WatchVodRequestVO requestVO = new WatchVodRequestVO();
    requestVO.setEventType(WatchVodRequestVO.EVENTTYPE_S);
    requestVO.setProductId(vodId);
//    c.watchVodCreate(requestVO);
    flushAndClear();
  }

  @Test
  public void testWatchVodDelete() {
    c.watchVodDelete(new String[] { TestData.PRODUCT_VOD_ID }, new String[]{});
    flushAndClear();
  }


}
