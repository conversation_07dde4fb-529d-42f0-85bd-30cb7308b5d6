<head>
    <title>interest</title>
</head>
<body>
<!-- ========== list ============= -->
<form id="list" >
  <fieldset>
    <legend>/interest/list</legend>
    <p><label for="pageSize">pageSize:</label><input type="text" id="pageSize" name="pageSize" value="10" /></p>
    <p><label for="page">page:</label><input type="text" id="page" name="page" value="1" /></p>
    <p><input type="submit" value="list"/></p>
    <p>request:<div id="list_request"></div></p>
    <p>response:<div id="list_response"></div></p>
  </fieldset>
</form>
<script type="text/javascript">
$(document).ready(function() {
  $('#list').submit(function() {
    $('#list_request').html($(this).serialize());
    $.getJSON('/interest/list',
        $(this).serialize())
        .done(function(data) {
          $('#list_response').text($.toJSON(data));
        });
    return false;
  });
});
</script>

<!-- ========== add ============= -->
<form id="add" >
  <fieldset>
    <legend>/interest/add</legend>
    <p><label for="categoryId">categoryId:</label><input type="text" id="categoryId" name="categoryId" value="" /></p>
    <p><input type="submit" value="add"/></p>
    <p>request:<div id="add_request"></div></p>
    <p>response:<div id="add_response"></div></p>
  </fieldset>
</form>
<script type="text/javascript">
$(document).ready(function() {
  $('#add').submit(function() {
    $('#add_request').html($(this).serialize());
    $.post('/interest/add',
        $(this).serialize())
        .done(function(data) {
          $('#add_response').text($.toJSON(data));
        });
    return false;
  });
});
</script>

<!-- ========== delete ============= -->
<form id="delete" >
  <fieldset>
    <legend>/interest/delete</legend>
    <p><label for="categoryId">categoryId:</label><input type="text" id="categoryId" name="categoryId" value="" /></p>
    <p><input type="submit" value="delete"/></p>
    <p>request:<div id="delete_request"></div></p>
    <p>response:<div id="delete_response"></div></p>
  </fieldset>
</form>
<script type="text/javascript">
$(document).ready(function() {
  $('#delete').submit(function() {
    $('#delete_request').html($(this).serialize());
    $.post('/interest/delete',
        $(this).serialize())
        .done(function(data) {
          $('#delete_response').text($.toJSON(data));
        });
    return false;
  });
});
</script>

</body>