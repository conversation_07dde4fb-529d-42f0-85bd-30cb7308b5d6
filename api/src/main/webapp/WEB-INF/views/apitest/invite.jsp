<head>
    <title>invite</title>
</head>
<body>
<!-- ========== send_key ============= -->
<form id="send_key" >
  <fieldset>
    <legend>/invite/send_key</legend>
    <p><label for="name">name:</label><input type="text" id="name" name="name" value="" /></p>
    <p><label for="email">email:</label><input type="text" id="email" name="email" value="" /></p>
    <p><label for="deviceId">deviceId:</label><input type="text" id="deviceId" name="deviceId" value="" /></p>
    <p><input type="submit" value="send_key"/></p>
    <p>request:<div id="send_key_request"></div></p>
    <p>response:<div id="send_key_response"></div></p>
  </fieldset>
</form>
<script type="text/javascript">
$(document).ready(function() {
  $('#send_key').submit(function() {
    $('#send_key_request').html($(this).serialize());
    $.post('/invite/send_key',
        $(this).serialize())
        .done(function(data) {
          $('#send_key_response').text($.toJSON(data));
        });
    return false;
  });
});
</script>

<!-- ========== resend_key ============= -->
<form id="resend_key" >
  <fieldset>
    <legend>/invite/resend_key</legend>
    <p><label for="email">email:</label><input type="text" id="email" name="email" value="" /></p>
    <p><label for="deviceId">deviceId:</label><input type="text" id="deviceId" name="deviceId" value="" /></p>
    <p><label for="email">email:</label><input type="text" id="email" name="email" value="" /></p>
    <p><label for="deviceId">deviceId:</label><input type="text" id="deviceId" name="deviceId" value="" /></p>
    <p><input type="submit" value="resend_key"/></p>
    <p>request:<div id="resend_key_request"></div></p>
    <p>response:<div id="resend_key_response"></div></p>
  </fieldset>
</form>
<script type="text/javascript">
$(document).ready(function() {
  $('#resend_key').submit(function() {
    $('#resend_key_request').html($(this).serialize());
    $.post('/invite/resend_key',
        $(this).serialize())
        .done(function(data) {
          $('#resend_key_response').text($.toJSON(data));
        });
    return false;
  });
});
</script>

