<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8"%>
<%@ include file="/common/common.jsp"%>
<!DOCTYPE html>
<html lang="${sessionScope['org.apache.struts2.action.LOCALE'] }">
<head>
<title>apitest::<decorator:title default="" /></title>
<meta charset="UTF-8" />
<script type="text/javascript" src="<c:url value="/resources/js/jquery.min.js" />"></script>
<script type="text/javascript" src="<c:url value="/resources/js/jquery.json.min.js"/>"></script>
<script type="text/javascript" src="<c:url value="/resources/js/jquery.util.js"/>"></script>
<script type="text/javascript" src="<c:url value="/resources/js/jquery.form.min.js"/>"></script>
<script type="text/javascript" src="<c:url value="/resources/js/mqttws31.js"/>"></script>
<script type="text/javascript">
  function callApi(formId, url, method, jqForm, e) {
    var options = {
      url : url,
      type : method,
      dataType : 'json',
      clearForm : false,
      beforeSubmit : function(formData, jqForm, options) {
        var $a = $('<a />');
        var request = $.param(formData);
        $('#' + formId + ' #' + formId + '_request').html(
            $a.attr('href', url + '?' + request).attr('target', '_blank').text(request));
      },
      success : function(result) {
        console.log(result);
        $('#' + formId + ' #' + formId + '_response').html($.toJSON(result));
      },
      error : function(error) {
        $('#' + formId + ' #' + formId + '_response').html(error.status + ' ' + error.statusText);
      }

    };
    jqForm.ajaxSubmit(options);
    e.preventDefault();
  }
</script>
<decorator:head />
</head>

<security:authorize access="hasAnyRole('ROLE_USER')">
  <security:authentication property="principal.id" var="sessionUserId" scope="request" />
  <security:authentication property="principal.email" var="sessionEmail" scope="request" />
  <security:authentication property="principal.timezone" var="sessionTimezone" scope="request" />
</security:authorize>
<body>
  <ui:constants className="com.laive.api.common.ApiUriConstants" scope="application" />
  <ul>
    <li><a href="<c:url value="/" />">Go to Index</a></li>
  </ul>
  <decorator:body />
</body>
</html>
