package com.laive.api.account;

import lombok.Getter;
import lombok.Setter;

import com.laive.core.base.RequestVO;
import com.laive.core.domain.User;

@Getter
@Setter
public class AccountLoginRequestVO extends RequestVO {

  private static final long serialVersionUID = 7474930394289941486L;

  private String accessToken;

  private String email;

  private String password;

  private String authInstitute;

  private String deviceId;

  private String deviceType;

  private String deviceModel;

  private String accessNetworkType;

  private String timezone;

  // facebooklogin
  private String platformId;

  public User toUser() {
    User user = new User();
    user.setEmail(this.getEmail());
    return user;
  }

}
