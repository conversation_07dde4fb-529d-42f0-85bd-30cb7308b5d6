package com.laive.api.schedule;

import java.util.Date;
import java.util.List;

import lombok.extern.apachecommons.CommonsLog;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.laive.core.domain.Schedule;
import com.laive.core.service.ScheduleManager;
import com.laive.core.util.DateUtil;
import com.laive.core.util.ServicePropertiesUtil;

@Component
@Slf4j
public final class ScheduleTestDataUtil {

    @Autowired
    private ScheduleManager scheduleManager;

    @Autowired
    private ServicePropertiesUtil servicePropertiesUtil;

    public void execute() {
        if (servicePropertiesUtil.isCreateScheduleTestDataEnabled())
            createScheduleTestData();
    }

    public void createScheduleTestData() {

        String fromDt = DateUtil.getDate("yyyyMMdd", new Date());
        String toDt = fromDt;
        Date fromDate = DateUtil.convertStringToDate("yyyyMMdd", fromDt);
        Date toDate = DateUtil.convertStringToDate("yyyyMMdd", toDt);
        toDate = DateUtil.addSeconds(DateUtil.addDays(toDate, 1), -1);

        scheduleManager.deleteAllSchedules(fromDate, toDate);
        Date standardDt = DateUtil.convertStringToDate("yyyyMMdd", "20130101");
        List<Schedule> list = scheduleManager.getSchedulesByDate(standardDt);

        long addDayStart = DateUtil.getDateDiffDay(fromDate, standardDt);
        long addDayEnd = DateUtil.getDateDiffDay(toDate, standardDt);
        for (int ii = (int)addDayStart; ii <= (int)addDayEnd; ii++) {
            for (Schedule schedule : list) {

                Schedule newSchedule = new Schedule();
                newSchedule.setStartDt(DateUtil.addDays(schedule.getStartDt(), ii));
                newSchedule.setEndDt(DateUtil.addDays(schedule.getEndDt(), ii));
                newSchedule.setLibrary(schedule.getLibrary());
                newSchedule.setDuration(schedule.getDuration());
                scheduleManager.merge(newSchedule);
            }
        }

    }
}
