package com.laive.api.box;

import com.laive.core.annotation.ApiKeyRestricted;
import com.laive.core.base.BaseController;
import com.laive.core.base.ResponseVO;
import com.laive.core.domain.Box;
import com.laive.core.service.BoxManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/box_external")
public class BoxExternalController extends BaseController {

  @Autowired
  private BoxManager boxManager;

  @ApiKeyRestricted
  @RequestMapping(value = "/register")
  public ResponseVO register(@RequestBody BoxExternalRegisterRequestVO requestVO) {

    for (BoxExternalRegisterRequestVO.BoxRegisterVO registerVO : requestVO.getList()) {
      Box box = boxManager.create(registerVO.getMacAddress(), registerVO.getSerial(), registerVO.getModel(),
              registerVO.getStatus(), null);
      box.setEnabled(registerVO.isEnabled());
      boxManager.merge(box);
    }
    return ResponseVO.ok();
  }

  @ApiKeyRestricted
  @RequestMapping(value = "/activate")
  public ResponseVO activate(@RequestBody BoxExternalActivationRequestVO requestVO) {

    for (BoxExternalActivationRequestVO.BoxActivationVO vo : requestVO.getList()) {
      Box box = boxManager.find(vo.getMacAddress());
      if (box == null)
        continue;

      box.setEnabled(vo.isEnabled());
      boxManager.merge(box);
    }
    return ResponseVO.ok();
  }

}
