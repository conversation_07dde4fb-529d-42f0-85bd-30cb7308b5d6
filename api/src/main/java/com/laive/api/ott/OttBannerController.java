package com.laive.api.ott;

import java.util.Collections;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.laive.api.common.ApiUriConstants;
import com.laive.core.base.BaseController;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.domain.ott.OttBanner;
import com.laive.core.service.OttBannerManager;

@Slf4j
@RestController
public class OttBannerController extends BaseController {

  @Autowired
  private OttBannerManager ottBannerManager;

  @RequestMapping(value = ApiUriConstants.OTT_BANNER_TOP_LIST)
  public OttBannerImageListResVO list(@RequestParam String platformId) {
    return getOttBannerList(platformId, OttBanner.OttBannerType.TOP);
  }

  @RequestMapping(value = ApiUriConstants.OTT_BANNER_RECOMMEND_LIST)
  public OttBannerImageListResVO recommendList(@RequestParam String platformId) {
    return getOttBannerList(platformId, OttBanner.OttBannerType.RECOMMEND);
  }

  @RequestMapping(value = ApiUriConstants.OTT_BANNER_AD_WIDE_LIST)
  public OttBannerImageListResVO adWideList(@RequestParam String platformId) {
    return getOttBannerList(platformId, OttBanner.OttBannerType.AD_WIDE);
  }

  @RequestMapping(value = ApiUriConstants.OTT_BANNER_HOTCLIP_LIST)
  public OttBannerImageListResVO hotclips(@RequestParam String platformId) {
    return getOttBannerList(platformId, OttBanner.OttBannerType.HOTCLIP);
  }

  @RequestMapping(value = ApiUriConstants.OTT_BANNER_YOUTUBE_LIST)
  public OttBannerImageListResVO youtubes(@RequestParam String platformId) {
    return getOttBannerList(platformId, OttBanner.OttBannerType.YOUTUBE);
  }

  private OttBannerImageListResVO getOttBannerList(String platformId, OttBanner.OttBannerType ottBannerType) {
    OttBanner activeBanner = ottBannerManager.getActiveOttBanner(ottBannerType, platformId);
    if (activeBanner == null || activeBanner.getOttBannerImages().size() == 0) {
      return OttBannerImageListResVO.builder().returnCode(ReturnCodes.OK).list(Collections.emptyList()).build();
    } else {
      return OttBannerImageListResVO.builder().returnCode(ReturnCodes.OK).rollingDurationSeconds(activeBanner.getRollingDurationSeconds())
          .list(activeBanner.getOttBannerImages()).build();
    }
  }

}
