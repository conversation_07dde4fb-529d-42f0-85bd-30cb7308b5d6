package com.laive.api.channel;

import com.laive.core.config.RedisCacheConfig;
import com.laive.core.service.ChannelManager;
import com.laive.core.service.ChannelPackageManager;
import com.laive.core.vo.ChannelListVO;
import com.laive.core.domain.Channel;
import com.laive.core.domain.ChannelPackage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChannelService {

  private final ChannelManager channelManager;
  private final ChannelPackageManager channelPackageManager;

  @Cacheable(value = RedisCacheConfig.CACHE_CHANNELS, key="#listVO.platformId.concat('-').concat(#listVO.pageSize)" +
          ".concat('-').concat(#listVO.page).concat('-').concat(#listVO.type)")
  public ChannelListVO getCachedChannelListVO(ChannelListVO listVO) {
    return channelManager.getActivatedChannelListVO(listVO);
  }

  public ChannelListVO getChannelListVO(ChannelListVO listVO) {
    return channelManager.getActivatedChannelListVO(listVO);
  }

  /**
   * Cached method for getting channel list data with detailed logging
   * Cache key includes platformId, pageSize, page, type, and countryCode for comprehensive caching
   */
  @Transactional(readOnly = true)
  @Cacheable(value = RedisCacheConfig.CACHE_CHANNELS, key="'channels-'.concat(#listVO.platformId).concat('-')"
          + ".concat(#listVO.pageSize != null ? #listVO.pageSize.toString() : 'null').concat('-')"
          + ".concat(#listVO.page != null ? #listVO.page.toString() : 'null').concat('-')"
          + ".concat(#listVO.type != null ? #listVO.type.toString() : 'null').concat('-')"
          + ".concat(#listVO.countryCode != null ? #listVO.countryCode : 'null')")
  public ChannelListVO getCachedChannelListData(ChannelListVO listVO) {
    log.info("[CACHE MISS] Loading channel list data from database for platformId: {}, pageSize: {}, page: {}, type: {}, countryCode: {}",
             listVO.getPlatformId(), listVO.getPageSize(), listVO.getPage(), listVO.getType(), listVO.getCountryCode());

    ChannelListVO result = channelManager.getActivatedChannelListVO(listVO);

    // Force initialization of lazy collections for channels
    if (result.getList() != null) {
      for (Channel channel : result.getList()) {
        if (channel.getIptvList() != null) {
          channel.getIptvList().size(); // Force lazy loading of iptvList
        }
        if (channel.getChannelGenres() != null) {
          channel.getChannelGenres().size(); // Force lazy loading of channelGenres
        }
        if (channel.getCountries() != null) {
          channel.getCountries().size(); // Force lazy loading of countries
        }
      }
    }

    log.info("[CACHE MISS] Loaded {} channels for platformId: {}, total count: {}",
             result.getList() != null ? result.getList().size() : 0, listVO.getPlatformId(), result.getTotalCount());

    return result;
  }

  /**
   * Non-cached method for getting channel list data with detailed logging
   */
  @Transactional(readOnly = true)
  public ChannelListVO getChannelListData(ChannelListVO listVO) {
    log.info("[NO CACHE] Loading channel list data from database for platformId: {}, pageSize: {}, page: {}, type: {}, countryCode: {}",
             listVO.getPlatformId(), listVO.getPageSize(), listVO.getPage(), listVO.getType(), listVO.getCountryCode());

    ChannelListVO result = channelManager.getActivatedChannelListVO(listVO);

    // Force initialization of lazy collections for channels
    if (result.getList() != null) {
      for (Channel channel : result.getList()) {
        if (channel.getIptvList() != null) {
          channel.getIptvList().size(); // Force lazy loading of iptvList
        }
        if (channel.getChannelGenres() != null) {
          channel.getChannelGenres().size(); // Force lazy loading of channelGenres
        }
        if (channel.getCountries() != null) {
          channel.getCountries().size(); // Force lazy loading of countries
        }
      }
    }

    log.info("[NO CACHE] Loaded {} channels for platformId: {}, total count: {}",
             result.getList() != null ? result.getList().size() : 0, listVO.getPlatformId(), result.getTotalCount());

    return result;
  }

  /**
   * Cached method for getting channel packages by platform
   * Cache key: platformId-type (simple key for testing)
   */
  @Transactional(readOnly = true)
  @Cacheable(value = RedisCacheConfig.CACHE_CHANNELS_BY_PACKAGE, key="'packages-'.concat(#platformId).concat('-').concat(#type != null ? #type.toString() : 'null')")
  public ChannelPackageData getCachedChannelPackageData(String platformId, ChannelListVO.ChannelSearchType type) {
    log.info("[CACHE MISS] Loading channel package data from database for platformId: {}, type: {}", platformId, type);

    List<ChannelPackage> channelPackages = channelPackageManager.getAllListHavingChildChannel(platformId);
    List<Channel> orphanChannels = channelManager.getOrphanChannels(platformId, type);

    // Force initialization of lazy collections before caching
    for (ChannelPackage pkg : channelPackages) {
      // Force load ChannelPackage lazy collections
      if (pkg.getChannelPackagePrices() != null) {
        pkg.getChannelPackagePrices().size(); // Force lazy loading of channelPackagePrices
      }

      if (pkg.getChannels() != null) {
        pkg.getChannels().size(); // Force lazy loading of channels
        // Also force load nested lazy collections in channels
        for (Channel channel : pkg.getChannels()) {
          if (channel.getIptvList() != null) {
            channel.getIptvList().size(); // Force lazy loading of iptvList
          }
          if (channel.getChannelGenres() != null) {
            channel.getChannelGenres().size(); // Force lazy loading of channelGenres
          }
          if (channel.getCountries() != null) {
            channel.getCountries().size(); // Force lazy loading of countries
          }
        }
      }
    }

    // Force initialization of lazy collections for orphan channels
    for (Channel channel : orphanChannels) {
      if (channel.getIptvList() != null) {
        channel.getIptvList().size(); // Force lazy loading of iptvList
      }
      if (channel.getChannelGenres() != null) {
        channel.getChannelGenres().size(); // Force lazy loading of channelGenres
      }
      if (channel.getCountries() != null) {
        channel.getCountries().size(); // Force lazy loading of countries
      }
    }

    log.info("[CACHE MISS] Loaded {} channel packages and {} orphan channels for platformId: {}",
             channelPackages.size(), orphanChannels.size(), platformId);

    return new ChannelPackageData(channelPackages, orphanChannels);
  }

  /**
   * Non-cached method for getting channel packages by platform
   */
  @Transactional(readOnly = true)
  public ChannelPackageData getChannelPackageData(String platformId, ChannelListVO.ChannelSearchType type) {
    log.info("[NO CACHE] Loading channel package data from database for platformId: {}, type: {}", platformId, type);

    List<ChannelPackage> channelPackages = channelPackageManager.getAllListHavingChildChannel(platformId);
    List<Channel> orphanChannels = channelManager.getOrphanChannels(platformId, type);

    // Force initialization of lazy collections
    for (ChannelPackage pkg : channelPackages) {
      // Force load ChannelPackage lazy collections
      if (pkg.getChannelPackagePrices() != null) {
        pkg.getChannelPackagePrices().size(); // Force lazy loading of channelPackagePrices
      }

      if (pkg.getChannels() != null) {
        pkg.getChannels().size(); // Force lazy loading of channels
        // Also force load nested lazy collections in channels
        for (Channel channel : pkg.getChannels()) {
          if (channel.getIptvList() != null) {
            channel.getIptvList().size(); // Force lazy loading of iptvList
          }
          if (channel.getChannelGenres() != null) {
            channel.getChannelGenres().size(); // Force lazy loading of channelGenres
          }
          if (channel.getCountries() != null) {
            channel.getCountries().size(); // Force lazy loading of countries
          }
        }
      }
    }

    // Force initialization of lazy collections for orphan channels
    for (Channel channel : orphanChannels) {
      if (channel.getIptvList() != null) {
        channel.getIptvList().size(); // Force lazy loading of iptvList
      }
      if (channel.getChannelGenres() != null) {
        channel.getChannelGenres().size(); // Force lazy loading of channelGenres
      }
      if (channel.getCountries() != null) {
        channel.getCountries().size(); // Force lazy loading of countries
      }
    }

    log.info("[NO CACHE] Loaded {} channel packages and {} orphan channels for platformId: {}",
             channelPackages.size(), orphanChannels.size(), platformId);

    return new ChannelPackageData(channelPackages, orphanChannels);
  }

  /**
   * Simple data holder for channel package data
   * Implements Serializable for Redis caching
   */
  public static class ChannelPackageData implements Serializable {
    private static final long serialVersionUID = 1L;

    private final List<ChannelPackage> channelPackages;
    private final List<Channel> orphanChannels;

    public ChannelPackageData(List<ChannelPackage> channelPackages, List<Channel> orphanChannels) {
      this.channelPackages = channelPackages;
      this.orphanChannels = orphanChannels;
    }

    public List<ChannelPackage> getChannelPackages() {
      return channelPackages;
    }

    public List<Channel> getOrphanChannels() {
      return orphanChannels;
    }
  }
}
