package com.laive.api.channel;

import com.laive.api.common.ApiUriConstants;
import com.laive.core.base.BaseController;
import com.laive.core.base.ResponseVO;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.domain.Channel;
import com.laive.core.domain.ChannelPackage;
import com.laive.core.domain.User;
import com.laive.core.exception.BusinessException;
import com.laive.core.service.ChannelManager;
import com.laive.core.service.ChannelPackageManager;
import com.laive.core.service.ChannelPackageSubscriptionManager;
import com.laive.core.util.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class ChannelPackageController extends BaseController {

  @Autowired
  private ChannelPackageManager channelPackageManager;

  @Autowired
  private ChannelManager channelManager;

  @Autowired
  private ChannelPackageSubscriptionManager channelPackageSubscriptionManager;

  @RequestMapping(value = ApiUriConstants.CHANNEL_PACKAGE_CHECK_WATCHABLE_BY_TOKEN)
  public ResponseVO getListByGroup(@RequestParam String platformId, @RequestParam(value = "channel") String channelUrl) {

    User loginUser = SecurityUtils.getLoginUser();
    if (loginUser == null)
      throw new BusinessException(ReturnCodes.ERROR_USER_NOT_LOGGED_IN);

    Channel channel = channelManager.getChannelByChannelUrl(channelUrl);
    if (channel != null && !channel.isFree() && !loginUser.isFreeUser()) {
      ChannelPackage legacyChannelPackage = channelPackageManager.getByPlatformId(platformId);
      boolean watchable = false;
      if (channelPackageSubscriptionManager.existsActivePayment(loginUser.getId(), legacyChannelPackage.getId())) {
        watchable = true;
      } else {
        List<ChannelPackage> channelPackageList = channelPackageManager.getChannelPackageByChannel(channel.getId());
        for (ChannelPackage channelPackage : channelPackageList) {
          if (channelPackage == null
                  || channelPackageSubscriptionManager.existsActivePayment(loginUser.getId(), channelPackage.getId())) {
            watchable = true;
            break;
          }
        }

      }
      if (!watchable)
        throw new BusinessException(ReturnCodes.ERROR_ACTIVE_PAYMENT_NOT_EXIST);
    }

    return ResponseVO.ok();
  }

}
