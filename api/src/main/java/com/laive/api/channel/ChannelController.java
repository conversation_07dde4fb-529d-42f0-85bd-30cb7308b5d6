package com.laive.api.channel;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.laive.api.common.ApiUriConstants;
import com.laive.api.common.SimpleRateLimiterService;
import com.laive.api.common.RedisRateLimiterService;
import com.laive.api.common.RateLimitExceededException;
import com.laive.core.base.BaseController;
import com.laive.core.constants.CommonCodes;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.domain.Channel;
import com.laive.core.domain.ChannelPackage;
import com.laive.core.domain.User;
import com.laive.core.exception.BusinessException;
import com.laive.core.exception.RequiredDataNotFoundException;
import com.laive.core.service.*;
import com.laive.core.util.*;
import com.laive.core.vo.ChannelListVO;
import com.laive.core.vo.SignedUrlVO;
import com.laive.core.web.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class ChannelController extends BaseController {

    @Autowired
    private ChannelManager channelManager;

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Autowired
    private ServicePropertiesUtil servicePropertiesUtil;

    @Autowired
    private GeoipUtil geoipUtil;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private ChannelPackageManager channelPackageManager;

    @Autowired
    private ChannelPackageSubscriptionManager channelPackageSubscriptionManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private ConfigUtil configUtil;

    @Autowired
    private ChannelPackageShareManager channelPackageShareManager;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private GeoLocationProviderService geoLocationProviderService;

    @Autowired
    private ChangeChannelIP changeChannelIP;

    @Autowired
    private SimpleRateLimiterService rateLimiterService;

    @Autowired
    private RedisRateLimiterService redisRateLimiterService;

    @RequestMapping(value = ApiUriConstants.CHANNEL_LIST_BY_GROUP, method = RequestMethod.GET)
    public ChannelListByGroupResponseVO getListByGroup(ChannelListVO channelListVO) {
        ParameterUtil.checkParameterEmpty(channelListVO.getPlatformId());
        setDefaultParameter(channelListVO);
        List<ChannelPackage> channelPackages = channelPackageManager.getAllListHavingChildChannel(channelListVO.getPlatformId());
        filterCatchupChannelIfNessasary(channelListVO, channelPackages);
        List<Channel> channelsWithoutGroup = channelManager.getOrphanChannels(channelListVO.getPlatformId(), channelListVO.getType());

        ChannelPackage channelPackage = channelPackageManager.getByPlatformId(channelListVO.getPlatformId());
        boolean existActivePayment = false;
        User loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null && channelPackage != null) {
            boolean isFreeUser = userManager.isFreeUser(loginUser.getId());
            existActivePayment = isFreeUser || channelPackageSubscriptionManager.existsActivePayment(getCurrentUser().getId(), channelPackage.getId());
        }

        // CHANGE CDN BASED ON USER IP
        if (servicePropertiesUtil.getMiniCDNEnabled().equals(1)) {
            changeChannelIP.setChannelFromChannelPackage(request, channelPackages);
        }
        // CHANGE CDN BASED ON USER IP END

        ChannelListByGroupResponseVO responseVO = new ChannelListByGroupResponseVO();
        responseVO.setChannelPackageList(channelPackages);
        responseVO.setChannels(channelsWithoutGroup);
        if (channelPackage != null)
            responseVO.setChannelPackagePrices(channelPackage.getChannelPackagePrices());
        responseVO.setWatchable(BooleanUtils.toInteger10(existActivePayment));
        responseVO.setScheduleUpdateInterval(configUtil.getScheduleUpdateIntervalMinutes());
        responseVO.setReturnCode(ReturnCodes.OK);
        return responseVO;
    }

    private void setDefaultParameter(ChannelListVO channelListVO) {
        if (channelListVO.getType() == null) {
            channelListVO.setType(ChannelListVO.ChannelSearchType.CATCHUP);
        }
    }

    private void filterCatchupChannelIfNessasary(ChannelListVO channelListVO, List<ChannelPackage> channelPackages) {
        if (ChannelListVO.ChannelSearchType.CATCHUP.equals(channelListVO.getType())) {
            for (ChannelPackage channelPackage : channelPackages) {
                Set<Channel> channels = new HashSet<>(channelPackage.getChannels());
                if (CollectionUtils.isEmpty(channels))
                    continue;
                channelPackage.setChannels(channels.stream().filter(Channel::isCatchup).sorted().collect(Collectors.toList()));
            }
        }
    }
    @RequestMapping(value = ApiUriConstants.CHANNEL_LIST_BY_PACKAGE, method = RequestMethod.GET)
    public ChannelListByPackageResponseVO getListByPackage(ChannelListVO channelListVO, HttpServletRequest request) {
        ParameterUtil.checkParameterEmpty(channelListVO.getPlatformId());

        // Redis-based rate limiting check - 10 requests per 10 seconds per IP
        String clientIp = request.getRemoteAddr();
        if (!redisRateLimiterService.isAllowed(clientIp)) {
            int currentCount = redisRateLimiterService.getCurrentCount(clientIp);
            long remainingTTL = redisRateLimiterService.getRemainingTTL(clientIp);
            log.warn("[REDIS RATE LIMIT] Request blocked for IP: {}, current count: {}, TTL: {} seconds",
                     clientIp, currentCount, remainingTTL);

            throw new BusinessException("601", String.format("Too many requests. Limit: 10 requests per 10 seconds. Current: %d, Retry after: %d seconds",
                                                               currentCount, remainingTTL));
        }

        setDefaultParameter(channelListVO);

        // Cache implementation for testing
        List<ChannelPackage> channelPackages;
        List<Channel> channelsWithoutPackage;

        if (servicePropertiesUtil.isUseCacheForChannelsByPackage()) {
            log.warn("[CACHE ENABLED] Using cached channel package data for platformId: {}, type: {}",
                     channelListVO.getPlatformId(), channelListVO.getType());
            ChannelService.ChannelPackageData cachedData = channelService.getCachedChannelPackageData(
                channelListVO.getPlatformId(), channelListVO.getType());
            channelPackages = cachedData.getChannelPackages();
            channelsWithoutPackage = cachedData.getOrphanChannels();
            log.warn("[CACHE ENABLED] Retrieved {} channel packages and {} orphan channels from cache/service",
                     channelPackages.size(), channelsWithoutPackage.size());
        } else {
            log.warn("[CACHE DISABLED] Loading channel package data directly from database for platformId: {}, type: {}",
                     channelListVO.getPlatformId(), channelListVO.getType());
            ChannelService.ChannelPackageData data = channelService.getChannelPackageData(
                channelListVO.getPlatformId(), channelListVO.getType());
            channelPackages = data.getChannelPackages();
            channelsWithoutPackage = data.getOrphanChannels();
            log.warn("[CACHE DISABLED] Loaded {} channel packages and {} orphan channels directly",
                     channelPackages.size(), channelsWithoutPackage.size());
        }

        processChannelPackageWatchable(channelPackages, channelListVO.getPlatformId());
        filterCatchupChannelIfNessasary(channelListVO, channelPackages);

        // added by MJ
        User loginUser = SecurityUtils.getLoginUser();    // loginUser는 Null 가능.
        String currentDevice = null;
        if (loginUser != null) {
            currentDevice = loginUser.getCurrentDeviceType();   // currentDeviceType은 Null 가능.
        }
        int drmType = CommonCodes.getDrmTypeWithDeviceType(currentDevice);
        //channelPackages 내에 Channel IP 수정
        if (drmType == CommonCodes.DRM_HLS) {
            for (int i = 0; i < channelPackages.size(); i++) {
                Set<Channel> setChnlList = new HashSet<>(channelPackages.get(i).getChannels());
                List<Channel> pkgChnllist = new ArrayList<>();
                for (Channel pkgarry : setChnlList) {
                    pkgarry.setIp(pkgarry.getIp().replace(".mpd", ".m3u8"));
                    pkgChnllist.add(pkgarry);
                }
            }
        }

        //channelsWithoutPackage 내에 Channel IP 수정
        if (drmType == CommonCodes.DRM_HLS) {
            List<Channel> chnllist = new ArrayList<Channel>();
            for (Channel arry : channelsWithoutPackage) {
                arry.setIp(arry.getIp().replace(".mpd", ".m3u8"));
                chnllist.add(arry);
            }
//       channelsWithoutPackage=chnllist;
        }

        // end added by MJ

        // giitd의 요청으로 잠시 보류. 버그 이슈 리포트에 있음
        // setOnAirSchedule(channelPackages, channelsWithoutPackage,
        // channelListVO.getPlatformId());

        for (ChannelPackage channelPackage : channelPackages) {
            Set<Channel> channelSet = new HashSet<>(channelPackage.getChannels());
            List<Channel> channelList = new ArrayList<>();
            for (Channel channel : channelSet) {
                if (channel.isEnabled())
                    channelList.add(channel);
            }
            channelList.sort(Comparator.comparing(Channel::getChannelNumber, Comparator.nullsLast(Integer::compareTo)));
            channelPackage.setChannels(channelList);
        }

//        channelPackages.forEach(cp -> cp.setChannels(cp.getChannels().stream().filter(Channel::isEnabled).sorted().collect(Collectors.toList())));

        // Geo-Blocking feature start
        String countryCode = geoLocationProviderService.getCountryCodeUsingIpAddress(request.getRemoteAddr());
        log.warn("| IP Address: {}, Country code: {}", request.getRemoteAddr(), countryCode);

        if (countryCode != null) {
            channelPackages.forEach(cp -> cp.getChannels().removeIf(channel -> channel.isGeoBlocked() && channel.getCountries().stream().noneMatch(country -> country.getCca2().equalsIgnoreCase(countryCode))));
            channelsWithoutPackage.removeIf(channel -> channel.isGeoBlocked() && channel.getCountries().stream().noneMatch(country -> country.getCca2().equalsIgnoreCase(countryCode)));
        }
        // Geo-Blocking feature end

        // CHANGE CDN BASED ON USER IP
        if (servicePropertiesUtil.getMiniCDNEnabled().equals(1)) {
            changeChannelIP.setChannelFromChannelPackage(request, channelPackages);
        }
        // CHANGE CDN BASED ON USER IP END

        ChannelListByPackageResponseVO responseVO = new ChannelListByPackageResponseVO();
        responseVO.setChannelPackages(channelPackages);
        responseVO.setChannels(channelsWithoutPackage);
        responseVO.setReturnCode(ReturnCodes.OK);
        responseVO.setScheduleUpdateInterval(configUtil.getScheduleUpdateIntervalMinutes());

        // Log successful request with current Redis rate limit status
        int currentCount = redisRateLimiterService.getCurrentCount(clientIp);
        long remainingTTL = redisRateLimiterService.getRemainingTTL(clientIp);
        log.warn("[REDIS RATE LIMIT] Request successful for IP: {}, current count: {}/10, TTL: {} seconds",
                 clientIp, currentCount, remainingTTL);
// Final logging for debugging
        log.warn("[RESPONSE] Returning {} channel packages and {} orphan channels for platformId: {}, cacheEnabled: {}",
                channelPackages.size(), channelsWithoutPackage.size(),
                channelListVO.getPlatformId(), servicePropertiesUtil.isUseCacheForChannelsByPackage());

        return responseVO;
    }

    /**
     * Utility endpoint to clear corrupted Redis rate limit keys
     * Call this if you're getting Redis deserialization errors
     */
    @RequestMapping(value = "/clearCorruptedRateLimitKeys", method = RequestMethod.GET)
    public Map<String, String> clearCorruptedRateLimitKeys() {
        Map<String, String> response = new HashMap<>();
        try {
            redisRateLimiterService.clearCorruptedKeys();
            response.put("status", "success");
            response.put("message", "Corrupted rate limit keys cleared successfully");
            log.info("[ADMIN] Corrupted rate limit keys cleanup triggered manually");
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to clear corrupted keys: " + e.getMessage());
            log.error("[ADMIN] Failed to clear corrupted rate limit keys: {}", e.getMessage());
        }
        return response;
    }

    private void processChannelPackageWatchable(List<ChannelPackage> channelPackages, String platformId) {
        User loginUser = SecurityUtils.getLoginUser();
        if (CollectionUtils.isEmpty(channelPackages) || loginUser == null)
            return;

        List<String> subscribeChannelPackageIds = channelPackageSubscriptionManager.getActiveChannelPackageIdList(loginUser.getId());
        List<String> channelPackageShareIds = channelPackageShareManager.getSharedChannelPakcageIds(subscribeChannelPackageIds);
        subscribeChannelPackageIds.addAll(channelPackageShareIds);

        boolean isFreeUser = userManager.isFreeUser(loginUser.getId());

        for (ChannelPackage channelPackage : channelPackages) {
            boolean watchable = false;
            if (isFreeUser) {
                watchable = true;
            } else {
                if (CollectionUtils.isNotEmpty(subscribeChannelPackageIds) && subscribeChannelPackageIds.contains(channelPackage.getId())) {
                    watchable = true;
                }
            }
            channelPackage.setWatchable(BooleanUtils.toInteger10(watchable));
        }
    }
    //TODO: CACHE
    @RequestMapping(value = ApiUriConstants.CHANNEL_LIST, method = RequestMethod.GET)
    public ChannelListVO getList(ChannelListVO channelListVO, HttpServletRequest request) {
        ParameterUtil.checkParameterEmpty(channelListVO.getPlatformId());
        channelListVO.setTimezone(getTimezone(channelListVO.getTimezone()));
        channelListVO.setCountryCode(geoLocationProviderService.getCountryCodeUsingIpAddress(request.getRemoteAddr()));
        log.warn("| IP Address: {}, Country code: {}", request.getRemoteAddr(), channelListVO.getCountryCode());
        setDefaultParameter(channelListVO);
        if (servicePropertiesUtil.isUseCacheForChannels()) {
            channelListVO = channelService.getCachedChannelListVO(channelListVO);
        } else {
            channelListVO = channelService.getChannelListVO(channelListVO);
        }

        // CHANGE CDN BASED ON USER IP
        if (servicePropertiesUtil.getMiniCDNEnabled().equals(1)) {
            changeChannelIP.changeChannelCDN(request, channelListVO.getList());
        }
        // CHANGE CDN BASED ON USER IP END

        // added by MJ
        User loginUser = SecurityUtils.getLoginUser();    // loginUser는 Null 가능.
        String currentDevice = null;
        if (loginUser != null) {
            currentDevice = loginUser.getCurrentDeviceType();   // currentDeviceType은 Null 가능.
        }
        int drmType = CommonCodes.getDrmTypeWithDeviceType(currentDevice);
        if (drmType == CommonCodes.DRM_HLS) {
            List<Channel> chnllist = new ArrayList<Channel>();
            for (Channel arry : channelListVO.getList()) {
                arry.setIp(arry.getIp().replace(".mpd", ".m3u8"));
                chnllist.add(arry);
            }
            channelListVO.setList(chnllist);
        }
        // end added by MJ

        channelListVO.setScheduleUpdateInterval(configUtil.getScheduleUpdateIntervalMinutes());
        return channelListVO;
    }


    @RequestMapping(value = ApiUriConstants.CHANNEL_LIST_BY_GENRE, method = RequestMethod.GET)
    public ChannelListByGenreResponseVO getListByGenre(@RequestParam String platformId, @RequestParam Integer genreId, HttpServletRequest request) {
        List<Channel> channels = channelManager.getAllByPassChannelsByPlatformIdAndGenreId(platformId, genreId);

        // Geo-Blocking feature start
        String countryCode = geoLocationProviderService.getCountryCodeUsingIpAddress(request.getRemoteAddr());
        log.warn("| IP Address: {}, Country code: {}", request.getRemoteAddr(), countryCode);

        if (countryCode != null) {
            log.warn("| Before removing channel-list-by-genre with geo-blocked country, the total is: {}", channels.size());
            channels = channels.stream().filter(e -> !e.isGeoBlocked() || (e.isGeoBlocked() && e.getCountries().stream().anyMatch(c -> c.getCca2().equalsIgnoreCase(countryCode)))).collect(Collectors.toList());
            log.warn("| After removing channel-list-by-genre with geo-blocked country, the total is: {}", channels.size());
        }
        // Geo-Blocking feature end

        // 수정 필요.
        // added by MJ
        User loginUser = SecurityUtils.getLoginUser();    // loginUser는 Null 가능.
        String currentDevice = null;
        if (loginUser != null) {
            currentDevice = loginUser.getCurrentDeviceType();   // currentDeviceType은 Null 가능.
        }
        int drmType = CommonCodes.getDrmTypeWithDeviceType(currentDevice);
        if (drmType == CommonCodes.DRM_HLS) {
            List<Channel> chnllist = new ArrayList<Channel>();
            for (Channel arry : channels) {
                arry.setIp(arry.getIp().replace(".mpd", ".m3u8"));
                chnllist.add(arry);
            }
            channels = chnllist;
        }

        // CHANGE CDN BASED ON USER IP
        if (servicePropertiesUtil.getMiniCDNEnabled().equals(1)) {
            changeChannelIP.changeChannelCDN(request, channels);
        }
        // CHANGE CDN BASED ON USER IP END

        return ChannelListByGenreResponseVO.builder().channels(channels).scheduleUpdateInterval(configUtil.getScheduleUpdateIntervalMinutes()).build();

    }

    private String getTimezone(String paramTimezone) {

        String timezone = paramTimezone;
        if (StringUtils.isBlank(timezone)) {
            String remoteAddr = RequestUtil.getRemoteAddr(request, servicePropertiesUtil.isReverseProxyEnabled());
            timezone = geoipUtil.getTimezone(remoteAddr);

            if (StringUtils.isBlank(timezone))
                timezone = servicePropertiesUtil.getServiceTimezone();
        }
        return timezone;
    }

    @RequestMapping(value = ApiUriConstants.CHANNEL_GET_URL, method = RequestMethod.GET)
    public SignedUrlVO getUrl(String channelId) {
        SignedUrlVO signedUrlVO = getSignedUrlVO(channelId, servicePropertiesUtil.getChannelLiveUrl());
        return signedUrlVO;
    }

    @RequestMapping(value = ApiUriConstants.CHANNEL_GET_DRM_URL, method = RequestMethod.GET)
    public SignedUrlVO getDrmUrl(String channelId) {
        SignedUrlVO signedUrlVO = getSignedUrlVO(channelId, servicePropertiesUtil.getChannelVodDrmUrl());
        return signedUrlVO;
    }

    private SignedUrlVO getSignedUrlVO(String channelId, String url) {
        ParameterUtil.checkParameterEmpty(channelId);
        if (!channelManager.exists(channelId))
            throw new RequiredDataNotFoundException(String.format("channel(%s) not found..", channelId));

        String requestUrl = MessageFormat.format(url, channelId);
        log.debug("requestUrl:" + requestUrl);

        String result = httpClientUtil.get(requestUrl);
        log.debug("responseString:" + result);
        ObjectMapper mapper = new ObjectMapper();

        SignedUrlVO signedUrlVO;
        try {
            signedUrlVO = mapper.readValue(result, SignedUrlVO.class);
        } catch (Exception e) {
            throw new RuntimeException("json convert error....");
        }

        signedUrlVO.setReturnCode(ReturnCodes.OK);
        log.debug("{}", signedUrlVO);
        return signedUrlVO;
    }

    // added by rakie. 2021.06.23
    // purchasable channel package list
    @RequestMapping(value = ApiUriConstants.CHANNEL_LIST_BY_PACKAGE_PURCHASABLE, method = RequestMethod.GET)
    public ChannelListByPackageResponseVO getListByPackagePurchasable(ChannelListVO channelListVO) {
        ParameterUtil.checkParameterEmpty(channelListVO.getPlatformId());
        setDefaultParameter(channelListVO);
        List<ChannelPackage> channelPackages = channelPackageManager.getPurchasableListHavingChildChannel(channelListVO.getPlatformId());
        processChannelPackageWatchable(channelPackages, channelListVO.getPlatformId());
        filterCatchupChannelIfNessasary(channelListVO, channelPackages);
        List<Channel> channelsWithoutPackage = channelManager.getOrphanChannels(channelListVO.getPlatformId(), channelListVO.getType());

        // giitd의 요청으로 잠시 보류. 버그 이슈 리포트에 있음
        // setOnAirSchedule(channelPackages, channelsWithoutPackage,
        // channelListVO.getPlatformId());

        // CHANGE CDN BASED ON USER IP
        if (servicePropertiesUtil.getMiniCDNEnabled().equals(1)) {
            changeChannelIP.setChannelFromChannelPackage(request, channelPackages);
        }
        // CHANGE CDN BASED ON USER IP END

        ChannelListByPackageResponseVO responseVO = new ChannelListByPackageResponseVO();
        responseVO.setChannelPackages(channelPackages);
        responseVO.setChannels(channelsWithoutPackage);
        responseVO.setReturnCode(ReturnCodes.OK);
        responseVO.setScheduleUpdateInterval(configUtil.getScheduleUpdateIntervalMinutes());
        return responseVO;
    }
}
