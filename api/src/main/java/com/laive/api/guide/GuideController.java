package com.laive.api.guide;

import com.laive.api.common.ApiUriConstants;
import com.laive.core.base.BaseController;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.service.LibraryManager;
import com.laive.core.vo.MarketingLibraryListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class GuideController extends BaseController {

  @Autowired
  private LibraryManager libraryManager;

  @RequestMapping(value = ApiUriConstants.GUIDE_VIDEO_LIST, method = RequestMethod.GET)
  public MarketingLibraryListVO list(MarketingLibraryListVO listVO) {
    listVO = libraryManager.getMarketingLibraryListVO(listVO);
    listVO.setReturnCode(ReturnCodes.OK);
    return listVO;
  }


}
