/**
 * FeatureVO.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.laive.api.account_link.ws.service_point;

public class FeatureVO  extends com.laive.api.account_link.ws.service_point.BaseVO  implements java.io.Serializable {
    private java.lang.Short action;

    private java.util.Calendar billDate;

    private short dispAttribute;

    private com.laive.api.account_link.ws.service_point.FeatureAttributeVO[] featureAttributeList;

    private int featureCode;

    private java.lang.String featureDesc;

    private short featureStatus;

    private boolean isFeatureActiveEnable;

    private java.math.BigDecimal offerNum;

    public FeatureVO() {
    }

    public FeatureVO(
           java.lang.Short action,
           java.util.Calendar billDate,
           short dispAttribute,
           com.laive.api.account_link.ws.service_point.FeatureAttributeVO[] featureAttributeList,
           int featureCode,
           java.lang.String featureDesc,
           short featureStatus,
           boolean isFeatureActiveEnable,
           java.math.BigDecimal offerNum) {
        this.action = action;
        this.billDate = billDate;
        this.dispAttribute = dispAttribute;
        this.featureAttributeList = featureAttributeList;
        this.featureCode = featureCode;
        this.featureDesc = featureDesc;
        this.featureStatus = featureStatus;
        this.isFeatureActiveEnable = isFeatureActiveEnable;
        this.offerNum = offerNum;
    }


    /**
     * Gets the action value for this FeatureVO.
     * 
     * @return action
     */
    public java.lang.Short getAction() {
        return action;
    }


    /**
     * Sets the action value for this FeatureVO.
     * 
     * @param action
     */
    public void setAction(java.lang.Short action) {
        this.action = action;
    }


    /**
     * Gets the billDate value for this FeatureVO.
     * 
     * @return billDate
     */
    public java.util.Calendar getBillDate() {
        return billDate;
    }


    /**
     * Sets the billDate value for this FeatureVO.
     * 
     * @param billDate
     */
    public void setBillDate(java.util.Calendar billDate) {
        this.billDate = billDate;
    }


    /**
     * Gets the dispAttribute value for this FeatureVO.
     * 
     * @return dispAttribute
     */
    public short getDispAttribute() {
        return dispAttribute;
    }


    /**
     * Sets the dispAttribute value for this FeatureVO.
     * 
     * @param dispAttribute
     */
    public void setDispAttribute(short dispAttribute) {
        this.dispAttribute = dispAttribute;
    }


    /**
     * Gets the featureAttributeList value for this FeatureVO.
     * 
     * @return featureAttributeList
     */
    public com.laive.api.account_link.ws.service_point.FeatureAttributeVO[] getFeatureAttributeList() {
        return featureAttributeList;
    }


    /**
     * Sets the featureAttributeList value for this FeatureVO.
     * 
     * @param featureAttributeList
     */
    public void setFeatureAttributeList(com.laive.api.account_link.ws.service_point.FeatureAttributeVO[] featureAttributeList) {
        this.featureAttributeList = featureAttributeList;
    }


    /**
     * Gets the featureCode value for this FeatureVO.
     * 
     * @return featureCode
     */
    public int getFeatureCode() {
        return featureCode;
    }


    /**
     * Sets the featureCode value for this FeatureVO.
     * 
     * @param featureCode
     */
    public void setFeatureCode(int featureCode) {
        this.featureCode = featureCode;
    }


    /**
     * Gets the featureDesc value for this FeatureVO.
     * 
     * @return featureDesc
     */
    public java.lang.String getFeatureDesc() {
        return featureDesc;
    }


    /**
     * Sets the featureDesc value for this FeatureVO.
     * 
     * @param featureDesc
     */
    public void setFeatureDesc(java.lang.String featureDesc) {
        this.featureDesc = featureDesc;
    }


    /**
     * Gets the featureStatus value for this FeatureVO.
     * 
     * @return featureStatus
     */
    public short getFeatureStatus() {
        return featureStatus;
    }


    /**
     * Sets the featureStatus value for this FeatureVO.
     * 
     * @param featureStatus
     */
    public void setFeatureStatus(short featureStatus) {
        this.featureStatus = featureStatus;
    }


    /**
     * Gets the isFeatureActiveEnable value for this FeatureVO.
     * 
     * @return isFeatureActiveEnable
     */
    public boolean isIsFeatureActiveEnable() {
        return isFeatureActiveEnable;
    }


    /**
     * Sets the isFeatureActiveEnable value for this FeatureVO.
     * 
     * @param isFeatureActiveEnable
     */
    public void setIsFeatureActiveEnable(boolean isFeatureActiveEnable) {
        this.isFeatureActiveEnable = isFeatureActiveEnable;
    }


    /**
     * Gets the offerNum value for this FeatureVO.
     * 
     * @return offerNum
     */
    public java.math.BigDecimal getOfferNum() {
        return offerNum;
    }


    /**
     * Sets the offerNum value for this FeatureVO.
     * 
     * @param offerNum
     */
    public void setOfferNum(java.math.BigDecimal offerNum) {
        this.offerNum = offerNum;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof FeatureVO)) return false;
        FeatureVO other = (FeatureVO) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.action==null && other.getAction()==null) || 
             (this.action!=null &&
              this.action.equals(other.getAction()))) &&
            ((this.billDate==null && other.getBillDate()==null) || 
             (this.billDate!=null &&
              this.billDate.equals(other.getBillDate()))) &&
            this.dispAttribute == other.getDispAttribute() &&
            ((this.featureAttributeList==null && other.getFeatureAttributeList()==null) || 
             (this.featureAttributeList!=null &&
              java.util.Arrays.equals(this.featureAttributeList, other.getFeatureAttributeList()))) &&
            this.featureCode == other.getFeatureCode() &&
            ((this.featureDesc==null && other.getFeatureDesc()==null) || 
             (this.featureDesc!=null &&
              this.featureDesc.equals(other.getFeatureDesc()))) &&
            this.featureStatus == other.getFeatureStatus() &&
            this.isFeatureActiveEnable == other.isIsFeatureActiveEnable() &&
            ((this.offerNum==null && other.getOfferNum()==null) || 
             (this.offerNum!=null &&
              this.offerNum.equals(other.getOfferNum())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getAction() != null) {
            _hashCode += getAction().hashCode();
        }
        if (getBillDate() != null) {
            _hashCode += getBillDate().hashCode();
        }
        _hashCode += getDispAttribute();
        if (getFeatureAttributeList() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getFeatureAttributeList());
                 i++) {
                java.lang.Object obj = java.lang.reflect.Array.get(getFeatureAttributeList(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        _hashCode += getFeatureCode();
        if (getFeatureDesc() != null) {
            _hashCode += getFeatureDesc().hashCode();
        }
        _hashCode += getFeatureStatus();
        _hashCode += (isIsFeatureActiveEnable() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getOfferNum() != null) {
            _hashCode += getOfferNum().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FeatureVO.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://amdocs/amsp/valueobject/servicepoint", "FeatureVO"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("action");
        elemField.setXmlName(new javax.xml.namespace.QName("", "action"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("billDate");
        elemField.setXmlName(new javax.xml.namespace.QName("", "billDate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dispAttribute");
        elemField.setXmlName(new javax.xml.namespace.QName("", "dispAttribute"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("featureAttributeList");
        elemField.setXmlName(new javax.xml.namespace.QName("", "featureAttributeList"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://amdocs/amsp/valueobject/servicepoint", "FeatureAttributeVO"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("featureCode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "featureCode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("featureDesc");
        elemField.setXmlName(new javax.xml.namespace.QName("", "featureDesc"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("featureStatus");
        elemField.setXmlName(new javax.xml.namespace.QName("", "featureStatus"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isFeatureActiveEnable");
        elemField.setXmlName(new javax.xml.namespace.QName("", "isFeatureActiveEnable"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("offerNum");
        elemField.setXmlName(new javax.xml.namespace.QName("", "offerNum"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "decimal"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
