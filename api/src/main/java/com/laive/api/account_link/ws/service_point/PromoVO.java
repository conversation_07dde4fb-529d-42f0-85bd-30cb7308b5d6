/**
 * PromoVO.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.laive.api.account_link.ws.service_point;

public class PromoVO  extends com.laive.api.account_link.ws.service_point.BaseVO  implements java.io.Serializable {
    private short action;

    private short actvPromoStepNum;

    private short actvPromoStepType;

    private boolean applyPromoOnActvSvc;

    private short defaultOccurence;

    private long etfId;

    private boolean hasDependency;

    private boolean isNewSvc;

    private java.lang.String originalPromoId;

    private java.lang.String prevPromoId;

    private int priority;

    private java.lang.String promoDesc;

    private short promoFirstStep;

    private java.lang.String promoId;

    private java.util.Calendar promoStartDate;

    private com.laive.api.account_link.ws.service_point.PromoStepDetailsVO[] promoStepDetails;

    private short promoType;

    private java.lang.String proratingPromoID;

    private boolean remvFrmStrtDate;

    private boolean userOverride;

    private boolean wasRemvFrmStrtDate;

    public PromoVO() {
    }

    public PromoVO(
           short action,
           short actvPromoStepNum,
           short actvPromoStepType,
           boolean applyPromoOnActvSvc,
           short defaultOccurence,
           long etfId,
           boolean hasDependency,
           boolean isNewSvc,
           java.lang.String originalPromoId,
           java.lang.String prevPromoId,
           int priority,
           java.lang.String promoDesc,
           short promoFirstStep,
           java.lang.String promoId,
           java.util.Calendar promoStartDate,
           com.laive.api.account_link.ws.service_point.PromoStepDetailsVO[] promoStepDetails,
           short promoType,
           java.lang.String proratingPromoID,
           boolean remvFrmStrtDate,
           boolean userOverride,
           boolean wasRemvFrmStrtDate) {
        this.action = action;
        this.actvPromoStepNum = actvPromoStepNum;
        this.actvPromoStepType = actvPromoStepType;
        this.applyPromoOnActvSvc = applyPromoOnActvSvc;
        this.defaultOccurence = defaultOccurence;
        this.etfId = etfId;
        this.hasDependency = hasDependency;
        this.isNewSvc = isNewSvc;
        this.originalPromoId = originalPromoId;
        this.prevPromoId = prevPromoId;
        this.priority = priority;
        this.promoDesc = promoDesc;
        this.promoFirstStep = promoFirstStep;
        this.promoId = promoId;
        this.promoStartDate = promoStartDate;
        this.promoStepDetails = promoStepDetails;
        this.promoType = promoType;
        this.proratingPromoID = proratingPromoID;
        this.remvFrmStrtDate = remvFrmStrtDate;
        this.userOverride = userOverride;
        this.wasRemvFrmStrtDate = wasRemvFrmStrtDate;
    }


    /**
     * Gets the action value for this PromoVO.
     * 
     * @return action
     */
    public short getAction() {
        return action;
    }


    /**
     * Sets the action value for this PromoVO.
     * 
     * @param action
     */
    public void setAction(short action) {
        this.action = action;
    }


    /**
     * Gets the actvPromoStepNum value for this PromoVO.
     * 
     * @return actvPromoStepNum
     */
    public short getActvPromoStepNum() {
        return actvPromoStepNum;
    }


    /**
     * Sets the actvPromoStepNum value for this PromoVO.
     * 
     * @param actvPromoStepNum
     */
    public void setActvPromoStepNum(short actvPromoStepNum) {
        this.actvPromoStepNum = actvPromoStepNum;
    }


    /**
     * Gets the actvPromoStepType value for this PromoVO.
     * 
     * @return actvPromoStepType
     */
    public short getActvPromoStepType() {
        return actvPromoStepType;
    }


    /**
     * Sets the actvPromoStepType value for this PromoVO.
     * 
     * @param actvPromoStepType
     */
    public void setActvPromoStepType(short actvPromoStepType) {
        this.actvPromoStepType = actvPromoStepType;
    }


    /**
     * Gets the applyPromoOnActvSvc value for this PromoVO.
     * 
     * @return applyPromoOnActvSvc
     */
    public boolean isApplyPromoOnActvSvc() {
        return applyPromoOnActvSvc;
    }


    /**
     * Sets the applyPromoOnActvSvc value for this PromoVO.
     * 
     * @param applyPromoOnActvSvc
     */
    public void setApplyPromoOnActvSvc(boolean applyPromoOnActvSvc) {
        this.applyPromoOnActvSvc = applyPromoOnActvSvc;
    }


    /**
     * Gets the defaultOccurence value for this PromoVO.
     * 
     * @return defaultOccurence
     */
    public short getDefaultOccurence() {
        return defaultOccurence;
    }


    /**
     * Sets the defaultOccurence value for this PromoVO.
     * 
     * @param defaultOccurence
     */
    public void setDefaultOccurence(short defaultOccurence) {
        this.defaultOccurence = defaultOccurence;
    }


    /**
     * Gets the etfId value for this PromoVO.
     * 
     * @return etfId
     */
    public long getEtfId() {
        return etfId;
    }


    /**
     * Sets the etfId value for this PromoVO.
     * 
     * @param etfId
     */
    public void setEtfId(long etfId) {
        this.etfId = etfId;
    }


    /**
     * Gets the hasDependency value for this PromoVO.
     * 
     * @return hasDependency
     */
    public boolean isHasDependency() {
        return hasDependency;
    }


    /**
     * Sets the hasDependency value for this PromoVO.
     * 
     * @param hasDependency
     */
    public void setHasDependency(boolean hasDependency) {
        this.hasDependency = hasDependency;
    }


    /**
     * Gets the isNewSvc value for this PromoVO.
     * 
     * @return isNewSvc
     */
    public boolean isIsNewSvc() {
        return isNewSvc;
    }


    /**
     * Sets the isNewSvc value for this PromoVO.
     * 
     * @param isNewSvc
     */
    public void setIsNewSvc(boolean isNewSvc) {
        this.isNewSvc = isNewSvc;
    }


    /**
     * Gets the originalPromoId value for this PromoVO.
     * 
     * @return originalPromoId
     */
    public java.lang.String getOriginalPromoId() {
        return originalPromoId;
    }


    /**
     * Sets the originalPromoId value for this PromoVO.
     * 
     * @param originalPromoId
     */
    public void setOriginalPromoId(java.lang.String originalPromoId) {
        this.originalPromoId = originalPromoId;
    }


    /**
     * Gets the prevPromoId value for this PromoVO.
     * 
     * @return prevPromoId
     */
    public java.lang.String getPrevPromoId() {
        return prevPromoId;
    }


    /**
     * Sets the prevPromoId value for this PromoVO.
     * 
     * @param prevPromoId
     */
    public void setPrevPromoId(java.lang.String prevPromoId) {
        this.prevPromoId = prevPromoId;
    }


    /**
     * Gets the priority value for this PromoVO.
     * 
     * @return priority
     */
    public int getPriority() {
        return priority;
    }


    /**
     * Sets the priority value for this PromoVO.
     * 
     * @param priority
     */
    public void setPriority(int priority) {
        this.priority = priority;
    }


    /**
     * Gets the promoDesc value for this PromoVO.
     * 
     * @return promoDesc
     */
    public java.lang.String getPromoDesc() {
        return promoDesc;
    }


    /**
     * Sets the promoDesc value for this PromoVO.
     * 
     * @param promoDesc
     */
    public void setPromoDesc(java.lang.String promoDesc) {
        this.promoDesc = promoDesc;
    }


    /**
     * Gets the promoFirstStep value for this PromoVO.
     * 
     * @return promoFirstStep
     */
    public short getPromoFirstStep() {
        return promoFirstStep;
    }


    /**
     * Sets the promoFirstStep value for this PromoVO.
     * 
     * @param promoFirstStep
     */
    public void setPromoFirstStep(short promoFirstStep) {
        this.promoFirstStep = promoFirstStep;
    }


    /**
     * Gets the promoId value for this PromoVO.
     * 
     * @return promoId
     */
    public java.lang.String getPromoId() {
        return promoId;
    }


    /**
     * Sets the promoId value for this PromoVO.
     * 
     * @param promoId
     */
    public void setPromoId(java.lang.String promoId) {
        this.promoId = promoId;
    }


    /**
     * Gets the promoStartDate value for this PromoVO.
     * 
     * @return promoStartDate
     */
    public java.util.Calendar getPromoStartDate() {
        return promoStartDate;
    }


    /**
     * Sets the promoStartDate value for this PromoVO.
     * 
     * @param promoStartDate
     */
    public void setPromoStartDate(java.util.Calendar promoStartDate) {
        this.promoStartDate = promoStartDate;
    }


    /**
     * Gets the promoStepDetails value for this PromoVO.
     * 
     * @return promoStepDetails
     */
    public com.laive.api.account_link.ws.service_point.PromoStepDetailsVO[] getPromoStepDetails() {
        return promoStepDetails;
    }


    /**
     * Sets the promoStepDetails value for this PromoVO.
     * 
     * @param promoStepDetails
     */
    public void setPromoStepDetails(com.laive.api.account_link.ws.service_point.PromoStepDetailsVO[] promoStepDetails) {
        this.promoStepDetails = promoStepDetails;
    }


    /**
     * Gets the promoType value for this PromoVO.
     * 
     * @return promoType
     */
    public short getPromoType() {
        return promoType;
    }


    /**
     * Sets the promoType value for this PromoVO.
     * 
     * @param promoType
     */
    public void setPromoType(short promoType) {
        this.promoType = promoType;
    }


    /**
     * Gets the proratingPromoID value for this PromoVO.
     * 
     * @return proratingPromoID
     */
    public java.lang.String getProratingPromoID() {
        return proratingPromoID;
    }


    /**
     * Sets the proratingPromoID value for this PromoVO.
     * 
     * @param proratingPromoID
     */
    public void setProratingPromoID(java.lang.String proratingPromoID) {
        this.proratingPromoID = proratingPromoID;
    }


    /**
     * Gets the remvFrmStrtDate value for this PromoVO.
     * 
     * @return remvFrmStrtDate
     */
    public boolean isRemvFrmStrtDate() {
        return remvFrmStrtDate;
    }


    /**
     * Sets the remvFrmStrtDate value for this PromoVO.
     * 
     * @param remvFrmStrtDate
     */
    public void setRemvFrmStrtDate(boolean remvFrmStrtDate) {
        this.remvFrmStrtDate = remvFrmStrtDate;
    }


    /**
     * Gets the userOverride value for this PromoVO.
     * 
     * @return userOverride
     */
    public boolean isUserOverride() {
        return userOverride;
    }


    /**
     * Sets the userOverride value for this PromoVO.
     * 
     * @param userOverride
     */
    public void setUserOverride(boolean userOverride) {
        this.userOverride = userOverride;
    }


    /**
     * Gets the wasRemvFrmStrtDate value for this PromoVO.
     * 
     * @return wasRemvFrmStrtDate
     */
    public boolean isWasRemvFrmStrtDate() {
        return wasRemvFrmStrtDate;
    }


    /**
     * Sets the wasRemvFrmStrtDate value for this PromoVO.
     * 
     * @param wasRemvFrmStrtDate
     */
    public void setWasRemvFrmStrtDate(boolean wasRemvFrmStrtDate) {
        this.wasRemvFrmStrtDate = wasRemvFrmStrtDate;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof PromoVO)) return false;
        PromoVO other = (PromoVO) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            this.action == other.getAction() &&
            this.actvPromoStepNum == other.getActvPromoStepNum() &&
            this.actvPromoStepType == other.getActvPromoStepType() &&
            this.applyPromoOnActvSvc == other.isApplyPromoOnActvSvc() &&
            this.defaultOccurence == other.getDefaultOccurence() &&
            this.etfId == other.getEtfId() &&
            this.hasDependency == other.isHasDependency() &&
            this.isNewSvc == other.isIsNewSvc() &&
            ((this.originalPromoId==null && other.getOriginalPromoId()==null) || 
             (this.originalPromoId!=null &&
              this.originalPromoId.equals(other.getOriginalPromoId()))) &&
            ((this.prevPromoId==null && other.getPrevPromoId()==null) || 
             (this.prevPromoId!=null &&
              this.prevPromoId.equals(other.getPrevPromoId()))) &&
            this.priority == other.getPriority() &&
            ((this.promoDesc==null && other.getPromoDesc()==null) || 
             (this.promoDesc!=null &&
              this.promoDesc.equals(other.getPromoDesc()))) &&
            this.promoFirstStep == other.getPromoFirstStep() &&
            ((this.promoId==null && other.getPromoId()==null) || 
             (this.promoId!=null &&
              this.promoId.equals(other.getPromoId()))) &&
            ((this.promoStartDate==null && other.getPromoStartDate()==null) || 
             (this.promoStartDate!=null &&
              this.promoStartDate.equals(other.getPromoStartDate()))) &&
            ((this.promoStepDetails==null && other.getPromoStepDetails()==null) || 
             (this.promoStepDetails!=null &&
              java.util.Arrays.equals(this.promoStepDetails, other.getPromoStepDetails()))) &&
            this.promoType == other.getPromoType() &&
            ((this.proratingPromoID==null && other.getProratingPromoID()==null) || 
             (this.proratingPromoID!=null &&
              this.proratingPromoID.equals(other.getProratingPromoID()))) &&
            this.remvFrmStrtDate == other.isRemvFrmStrtDate() &&
            this.userOverride == other.isUserOverride() &&
            this.wasRemvFrmStrtDate == other.isWasRemvFrmStrtDate();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        _hashCode += getAction();
        _hashCode += getActvPromoStepNum();
        _hashCode += getActvPromoStepType();
        _hashCode += (isApplyPromoOnActvSvc() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += getDefaultOccurence();
        _hashCode += new Long(getEtfId()).hashCode();
        _hashCode += (isHasDependency() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isIsNewSvc() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getOriginalPromoId() != null) {
            _hashCode += getOriginalPromoId().hashCode();
        }
        if (getPrevPromoId() != null) {
            _hashCode += getPrevPromoId().hashCode();
        }
        _hashCode += getPriority();
        if (getPromoDesc() != null) {
            _hashCode += getPromoDesc().hashCode();
        }
        _hashCode += getPromoFirstStep();
        if (getPromoId() != null) {
            _hashCode += getPromoId().hashCode();
        }
        if (getPromoStartDate() != null) {
            _hashCode += getPromoStartDate().hashCode();
        }
        if (getPromoStepDetails() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getPromoStepDetails());
                 i++) {
                java.lang.Object obj = java.lang.reflect.Array.get(getPromoStepDetails(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        _hashCode += getPromoType();
        if (getProratingPromoID() != null) {
            _hashCode += getProratingPromoID().hashCode();
        }
        _hashCode += (isRemvFrmStrtDate() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isUserOverride() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isWasRemvFrmStrtDate() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(PromoVO.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://amdocs/amsp/valueobject/servicepoint", "PromoVO"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("action");
        elemField.setXmlName(new javax.xml.namespace.QName("", "action"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("actvPromoStepNum");
        elemField.setXmlName(new javax.xml.namespace.QName("", "actvPromoStepNum"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("actvPromoStepType");
        elemField.setXmlName(new javax.xml.namespace.QName("", "actvPromoStepType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("applyPromoOnActvSvc");
        elemField.setXmlName(new javax.xml.namespace.QName("", "applyPromoOnActvSvc"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("defaultOccurence");
        elemField.setXmlName(new javax.xml.namespace.QName("", "defaultOccurence"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("etfId");
        elemField.setXmlName(new javax.xml.namespace.QName("", "etfId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "long"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("hasDependency");
        elemField.setXmlName(new javax.xml.namespace.QName("", "hasDependency"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isNewSvc");
        elemField.setXmlName(new javax.xml.namespace.QName("", "isNewSvc"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("originalPromoId");
        elemField.setXmlName(new javax.xml.namespace.QName("", "originalPromoId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("prevPromoId");
        elemField.setXmlName(new javax.xml.namespace.QName("", "prevPromoId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("priority");
        elemField.setXmlName(new javax.xml.namespace.QName("", "priority"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("promoDesc");
        elemField.setXmlName(new javax.xml.namespace.QName("", "promoDesc"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("promoFirstStep");
        elemField.setXmlName(new javax.xml.namespace.QName("", "promoFirstStep"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("promoId");
        elemField.setXmlName(new javax.xml.namespace.QName("", "promoId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("promoStartDate");
        elemField.setXmlName(new javax.xml.namespace.QName("", "promoStartDate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("promoStepDetails");
        elemField.setXmlName(new javax.xml.namespace.QName("", "promoStepDetails"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://amdocs/amsp/valueobject/servicepoint", "PromoStepDetailsVO"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("promoType");
        elemField.setXmlName(new javax.xml.namespace.QName("", "promoType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("proratingPromoID");
        elemField.setXmlName(new javax.xml.namespace.QName("", "proratingPromoID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("remvFrmStrtDate");
        elemField.setXmlName(new javax.xml.namespace.QName("", "remvFrmStrtDate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("userOverride");
        elemField.setXmlName(new javax.xml.namespace.QName("", "userOverride"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("wasRemvFrmStrtDate");
        elemField.setXmlName(new javax.xml.namespace.QName("", "wasRemvFrmStrtDate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
