<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://*************:9003/amspop/services/Customer" xmlns:apachesoap="http://xml.apache.org/xml-soap" xmlns:impl="http://*************:9003/amspop/services/Customer" xmlns:intf="http://*************:9003/amspop/services/Customer" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns1="http://amdocs/amsp/valueobject/customer" xmlns:tns2="http://valueobject.amsp.amdocs" xmlns:tns3="http://exception.amsp.amdocs" xmlns:tns4="http://amdocs/amsp/valueobject/addressservice" xmlns:tns5="http://amdocs/amsp/valueobject/accountview" xmlns:tns6="http://amdocs/amsp/valueobject/accountparam" xmlns:tns7="http://amdocs/core" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
<!--WSDL created by Apache Axis version: 1.4
Built on Apr 22, 2006 (06:55:48 PDT)-->
 <wsdl:types>
  <schema targetNamespace="http://valueobject.amsp.amdocs" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://amdocs/amsp/valueobject/customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountparam"/>
   <import namespace="http://amdocs/amsp/valueobject/addressservice"/>
   <import namespace="http://*************:9003/amspop/services/Customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountview"/>
   <import namespace="http://amdocs/core"/>
   <import namespace="http://exception.amsp.amdocs"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType abstract="true" name="BaseVO">
    <sequence/>
   </complexType>
  </schema>
  <schema targetNamespace="http://amdocs/amsp/valueobject/customer" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://amdocs/amsp/valueobject/accountparam"/>
   <import namespace="http://valueobject.amsp.amdocs"/>
   <import namespace="http://amdocs/amsp/valueobject/addressservice"/>
   <import namespace="http://*************:9003/amspop/services/Customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountview"/>
   <import namespace="http://amdocs/core"/>
   <import namespace="http://exception.amsp.amdocs"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType name="ProspectResultVO">
    <complexContent>
     <extension base="tns2:BaseVO">
      <sequence>
       <element name="custAcct" nillable="true" type="xsd:string"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
   <complexType name="AccountExpandedNamesVO">
    <complexContent>
     <extension base="tns2:BaseVO">
      <sequence>
       <element name="label" nillable="true" type="xsd:string"/>
       <element name="nameComponent" type="xsd:int"/>
       <element name="value" nillable="true" type="xsd:string"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
   <complexType name="PayCardDetailsVO">
    <complexContent>
     <extension base="tns2:BaseVO">
      <sequence>
       <element name="cardBillAddress" nillable="true" type="xsd:string"/>
       <element name="cardBillPostCode" nillable="true" type="xsd:string"/>
       <element name="cardHolderName" nillable="true" type="xsd:string"/>
       <element name="cardName" nillable="true" type="xsd:string"/>
       <element name="cardSequenceNumber" type="xsd:short"/>
       <element name="cardSwipeData" nillable="true" type="xsd:string"/>
       <element name="creditCardExpiration" nillable="true" type="xsd:string"/>
       <element name="creditCardNumber" nillable="true" type="xsd:string"/>
       <element name="creditCardType" nillable="true" type="xsd:string"/>
       <element name="epiCvn" nillable="true" type="xsd:int"/>
       <element name="epiHostRespCode" nillable="true" type="xsd:string"/>
       <element name="epiHostRespMessage" nillable="true" type="xsd:string"/>
       <element name="epiMerchantId" nillable="true" type="xsd:string"/>
       <element name="epiPointOfOrgin" nillable="true" type="xsd:string"/>
       <element name="epiPriorAuthorizationNumber" nillable="true" type="xsd:string"/>
       <element name="epiRecordId" nillable="true" type="xsd:string"/>
       <element name="epiRefNumber" nillable="true" type="xsd:string"/>
       <element name="epiRetrievalRefNumber" nillable="true" type="xsd:string"/>
       <element name="epiTerminalId" nillable="true" type="xsd:string"/>
       <element name="epiTranDate" nillable="true" type="xsd:string"/>
       <element name="isEpiNewDrawer" type="xsd:boolean"/>
       <element name="isPrimary" type="xsd:boolean"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
   <complexType name="CustServiceGroupDetailsVO">
    <complexContent>
     <extension base="tns2:BaseVO">
      <sequence>
       <element name="serviceGroup" type="xsd:int"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
   <complexType name="CustomerDetailsVO">
    <complexContent>
     <extension base="tns2:BaseVO">
      <sequence>
       <element name="EMailAddress" nillable="true" type="xsd:string"/>
       <element name="accountCategory" type="xsd:short"/>
       <element name="alias" nillable="true" type="xsd:string"/>
       <element name="billDate" nillable="true" type="xsd:dateTime"/>
       <element name="billForm" nillable="true" type="xsd:string"/>
       <element name="billingAddress" nillable="true" type="xsd:string"/>
       <element name="billingAddressFieldsArr" nillable="true" type="impl:ArrayOf_tns4_AddressFieldsDetailVO"/>
       <element name="billingAddressId" type="xsd:long"/>
       <element name="billingAddressUpdated" type="xsd:boolean"/>
       <element name="blankOutPersonalId1" type="xsd:short"/>
       <element name="blankOutPersonalId2" type="xsd:short"/>
       <element name="blankOutRphoneFlag" type="xsd:short"/>
       <element name="broadcastStatusFlag" type="xsd:boolean"/>
       <element name="businessDetails" nillable="true" type="tns5:BusinessDetailsVO"/>
       <element name="campaignCode" nillable="true" type="xsd:string"/>
       <element name="campaignDesc" nillable="true" type="xsd:string"/>
       <element name="cancelReason" nillable="true" type="xsd:string"/>
       <element name="cancelRsnCode" nillable="true" type="xsd:string"/>
       <element name="cancelRsnDesc" nillable="true" type="xsd:string"/>
       <element name="costCenter" type="xsd:int"/>
       <element name="custAcct" nillable="true" type="xsd:string"/>
       <element name="custClassCode" nillable="true" type="xsd:string"/>
       <element name="custClassDesc" nillable="true" type="xsd:string"/>
       <element name="custDataCode" nillable="true" type="xsd:string"/>
       <element name="custDataDesc" nillable="true" type="xsd:string"/>
       <element name="custHomePhoneAcct" nillable="true" type="xsd:string"/>
       <element name="custPin" nillable="true" type="xsd:string"/>
       <element name="custPredicted" nillable="true" type="xsd:dateTime"/>
       <element name="cvtr" nillable="true" type="xsd:string"/>
       <element name="cycle" type="xsd:int"/>
       <element name="dateOfBirth" nillable="true" type="xsd:dateTime"/>
       <element name="dealer" nillable="true" type="xsd:string"/>
       <element name="desigMaintId" type="xsd:int"/>
       <element name="disconnectDate" nillable="true" type="xsd:dateTime"/>
       <element name="disconnectReason" nillable="true" type="xsd:string"/>
       <element name="disconnectRsnCode" nillable="true" type="xsd:string"/>
       <element name="disconnectRsnDesc" nillable="true" type="xsd:string"/>
       <element name="discountCode" nillable="true" type="xsd:string"/>
       <element name="discountOffcycleDate" nillable="true" type="xsd:dateTime"/>
       <element name="driverLicense" nillable="true" type="xsd:string"/>
       <element name="driverLicense2" nillable="true" type="xsd:string"/>
       <element name="echeckFlag" type="xsd:int"/>
       <element name="echeckStopCycleDate" nillable="true" type="xsd:dateTime"/>
       <element name="eftsFlg" nillable="true" type="xsd:string"/>
       <element name="enterpriseId" nillable="true" type="xsd:string"/>
       <element name="eppFlg" type="xsd:int"/>
       <element name="eppStopCycleDate" nillable="true" type="xsd:dateTime"/>
       <element name="expandedName" nillable="true" type="impl:ArrayOf_tns1_AccountExpandedNamesVO"/>
       <element name="firstName" nillable="true" type="xsd:string"/>
       <element name="governmentUnifiedInvoice" nillable="true" type="xsd:string"/>
       <element name="hasPin" type="xsd:boolean"/>
       <element name="hasText" type="xsd:boolean"/>
       <element name="holdStatus" type="xsd:int"/>
       <element name="homePhone" nillable="true" type="xsd:string"/>
       <element name="hsds" type="xsd:int"/>
       <element name="infoCode" nillable="true" type="xsd:string"/>
       <element name="infoDesc" nillable="true" type="xsd:string"/>
       <element name="initialInstallDate" nillable="true" type="xsd:dateTime"/>
       <element name="invoiceFee" nillable="true" type="xsd:string"/>
       <element name="lastInstallDate" nillable="true" type="xsd:dateTime"/>
       <element name="lastName" nillable="true" type="xsd:string"/>
       <element name="lateAPCModeChgDate" nillable="true" type="xsd:dateTime"/>
       <element name="maiden" nillable="true" type="xsd:string"/>
       <element name="mduExempt" type="xsd:int"/>
       <element name="middleInitial" nillable="true" type="xsd:string"/>
       <element name="mobilePhone" nillable="true" type="xsd:string"/>
       <element name="monthlySavings" nillable="true" type="tns7:Money"/>
       <element name="monthlyTax" nillable="true" type="tns7:Money"/>
       <element name="nameField5" nillable="true" type="xsd:string"/>
       <element name="newCycle" type="xsd:int"/>
       <element name="offerNum" nillable="true" type="xsd:decimal"/>
       <element name="otherMailAdr" type="xsd:int"/>
       <element name="otherPhone" nillable="true" type="xsd:string"/>
       <element name="otherPhoneDesc" nillable="true" type="xsd:string"/>
       <element name="outletCount" type="xsd:int"/>
       <element name="payCardDetails" nillable="true" type="impl:ArrayOf_tns1_PayCardDetailsVO"/>
       <element name="pendExcosOdFlg" type="xsd:int"/>
       <element name="pendExcosOrdCount" type="xsd:int"/>
       <element name="personalId" nillable="true" type="xsd:string"/>
       <element name="personalId2" nillable="true" type="xsd:string"/>
       <element name="preferredContact" nillable="true" type="xsd:string"/>
       <element name="productLanguageCode" nillable="true" type="xsd:string"/>
       <element name="productLanguageDesc" nillable="true" type="xsd:string"/>
       <element name="rate" nillable="true" type="tns7:Money"/>
       <element name="rcvPromoCallsFlag" type="xsd:boolean"/>
       <element name="rcvPromoMailFlag" type="xsd:boolean"/>
       <element name="remainderScheme" nillable="true" type="xsd:string"/>
       <element name="salesChannel" nillable="true" type="xsd:string"/>
       <element name="salesReasonCode" nillable="true" type="xsd:string"/>
       <element name="salesReasonDesc" nillable="true" type="xsd:string"/>
       <element name="salesRepId" nillable="true" type="xsd:string"/>
       <element name="salesRepInitials" nillable="true" type="xsd:string"/>
       <element name="salesRepLastName" nillable="true" type="xsd:string"/>
       <element name="salesRepresentative" nillable="true" type="xsd:string"/>
       <element name="sellNameFlg" type="xsd:boolean"/>
       <element name="sendOtherMailTo" type="xsd:boolean"/>
       <element name="serviceGroupDetails" nillable="true" type="impl:ArrayOf_tns1_CustServiceGroupDetailsVO"/>
       <element name="shippingAddress" nillable="true" type="xsd:string"/>
       <element name="shippingAddressFieldsArr" nillable="true" type="impl:ArrayOf_tns4_AddressFieldsDetailVO"/>
       <element name="shippingAddressId" type="xsd:long"/>
       <element name="shippingAddressUpdated" type="xsd:boolean"/>
       <element name="siteId" nillable="true" type="xsd:string"/>
       <element name="splitCustNum" type="xsd:short"/>
       <element name="splitCustomer" type="xsd:boolean"/>
       <element name="statisticsCode" nillable="true" type="xsd:string"/>
       <element name="statisticsDesc" nillable="true" type="xsd:string"/>
       <element name="status" nillable="true" type="xsd:string"/>
       <element name="statusCode" nillable="true" type="xsd:string"/>
       <element name="statusDesc" nillable="true" type="xsd:string"/>
       <element name="stmtScheme" nillable="true" type="xsd:string"/>
       <element name="title" nillable="true" type="xsd:string"/>
       <element name="tvTypeCode" nillable="true" type="xsd:string"/>
       <element name="tvTypeDesc" nillable="true" type="xsd:string"/>
       <element name="typeCode" nillable="true" type="xsd:string"/>
       <element name="typeDescription" nillable="true" type="xsd:string"/>
       <element name="vip" type="xsd:boolean"/>
       <element name="vipNum" type="xsd:int"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
   <complexType name="SplitCustomerDetailsVO">
    <complexContent>
     <extension base="tns2:BaseVO">
      <sequence>
       <element name="custAccount" nillable="true" type="xsd:string"/>
       <element name="custLName" nillable="true" type="xsd:string"/>
       <element name="enterpriseId" nillable="true" type="xsd:string"/>
       <element name="splitCustomer" type="xsd:boolean"/>
       <element name="stat" nillable="true" type="xsd:string"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
  </schema>
  <schema targetNamespace="http://exception.amsp.amdocs" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://amdocs/amsp/valueobject/customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountparam"/>
   <import namespace="http://valueobject.amsp.amdocs"/>
   <import namespace="http://amdocs/amsp/valueobject/addressservice"/>
   <import namespace="http://*************:9003/amspop/services/Customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountview"/>
   <import namespace="http://amdocs/core"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType name="AmspBaseException">
    <sequence/>
   </complexType>
   <complexType name="AmspServiceException">
    <complexContent>
     <extension base="tns3:AmspBaseException">
      <sequence>
       <element name="localizedMessage" nillable="true" type="xsd:string"/>
       <element name="msgId" nillable="true" type="xsd:string"/>
       <element name="subMsgIds" nillable="true" type="impl:ArrayOf_xsd_string"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
  </schema>
  <schema targetNamespace="http://*************:9003/amspop/services/Customer" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://amdocs/amsp/valueobject/customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountparam"/>
   <import namespace="http://valueobject.amsp.amdocs"/>
   <import namespace="http://amdocs/amsp/valueobject/addressservice"/>
   <import namespace="http://amdocs/amsp/valueobject/accountview"/>
   <import namespace="http://amdocs/core"/>
   <import namespace="http://exception.amsp.amdocs"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType name="ArrayOf_xsd_string">
    <complexContent>
     <restriction base="soapenc:Array">
      <attribute ref="soapenc:arrayType" wsdl:arrayType="xsd:string[]"/>
     </restriction>
    </complexContent>
   </complexType>
   <complexType name="ArrayOf_tns4_AddressFieldsDetailVO">
    <complexContent>
     <restriction base="soapenc:Array">
      <attribute ref="soapenc:arrayType" wsdl:arrayType="tns4:AddressFieldsDetailVO[]"/>
     </restriction>
    </complexContent>
   </complexType>
   <complexType name="ArrayOf_tns1_AccountExpandedNamesVO">
    <complexContent>
     <restriction base="soapenc:Array">
      <attribute ref="soapenc:arrayType" wsdl:arrayType="tns1:AccountExpandedNamesVO[]"/>
     </restriction>
    </complexContent>
   </complexType>
   <complexType name="ArrayOf_tns1_PayCardDetailsVO">
    <complexContent>
     <restriction base="soapenc:Array">
      <attribute ref="soapenc:arrayType" wsdl:arrayType="tns1:PayCardDetailsVO[]"/>
     </restriction>
    </complexContent>
   </complexType>
   <complexType name="ArrayOf_tns1_CustServiceGroupDetailsVO">
    <complexContent>
     <restriction base="soapenc:Array">
      <attribute ref="soapenc:arrayType" wsdl:arrayType="tns1:CustServiceGroupDetailsVO[]"/>
     </restriction>
    </complexContent>
   </complexType>
   <complexType name="ArrayOf_tns1_SplitCustomerDetailsVO">
    <complexContent>
     <restriction base="soapenc:Array">
      <attribute ref="soapenc:arrayType" wsdl:arrayType="tns1:SplitCustomerDetailsVO[]"/>
     </restriction>
    </complexContent>
   </complexType>
  </schema>
  <schema targetNamespace="http://amdocs/amsp/valueobject/addressservice" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://amdocs/amsp/valueobject/customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountparam"/>
   <import namespace="http://valueobject.amsp.amdocs"/>
   <import namespace="http://*************:9003/amspop/services/Customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountview"/>
   <import namespace="http://amdocs/core"/>
   <import namespace="http://exception.amsp.amdocs"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType name="AddressFieldsDetailVO">
    <complexContent>
     <extension base="tns2:BaseVO">
      <sequence>
       <element name="fieldLabel" nillable="true" type="xsd:string"/>
       <element name="lineNumber" type="xsd:int"/>
       <element name="mask" nillable="true" type="xsd:string"/>
       <element name="maxLength" type="xsd:int"/>
       <element name="nlsNum" type="xsd:long"/>
       <element name="required" type="xsd:boolean"/>
       <element name="seqNumber" type="xsd:int"/>
       <element name="truncate" type="xsd:boolean"/>
       <element name="value" nillable="true" type="xsd:string"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
  </schema>
  <schema targetNamespace="http://amdocs/amsp/valueobject/accountparam" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://amdocs/amsp/valueobject/customer"/>
   <import namespace="http://valueobject.amsp.amdocs"/>
   <import namespace="http://amdocs/amsp/valueobject/addressservice"/>
   <import namespace="http://*************:9003/amspop/services/Customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountview"/>
   <import namespace="http://amdocs/core"/>
   <import namespace="http://exception.amsp.amdocs"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType name="DesignatedMaintainerVO">
    <complexContent>
     <extension base="tns2:BaseVO">
      <sequence>
       <element name="addressId" type="xsd:long"/>
       <element name="attention" nillable="true" type="xsd:string"/>
       <element name="companyName" nillable="true" type="xsd:string"/>
       <element name="desigMaintAddress" nillable="true" type="xsd:string"/>
       <element name="desigMaintId" type="xsd:long"/>
       <element name="extension" nillable="true" type="xsd:string"/>
       <element name="extnPhDesc" nillable="true" type="xsd:string"/>
       <element name="maintainerAddressFieldsArr" nillable="true" type="impl:ArrayOf_tns4_AddressFieldsDetailVO"/>
       <element name="phoneNum" nillable="true" type="xsd:string"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
  </schema>
  <schema targetNamespace="http://amdocs/amsp/valueobject/accountview" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://amdocs/amsp/valueobject/customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountparam"/>
   <import namespace="http://valueobject.amsp.amdocs"/>
   <import namespace="http://amdocs/amsp/valueobject/addressservice"/>
   <import namespace="http://*************:9003/amspop/services/Customer"/>
   <import namespace="http://amdocs/core"/>
   <import namespace="http://exception.amsp.amdocs"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType name="BusinessDetailsVO">
    <complexContent>
     <extension base="tns2:BaseVO">
      <sequence>
       <element name="businessName" nillable="true" type="xsd:string"/>
       <element name="designatorMaintainer" nillable="true" type="tns6:DesignatedMaintainerVO"/>
       <element name="invoiceNumber" nillable="true" type="xsd:string"/>
       <element name="otherVendorLines" type="xsd:short"/>
       <element name="purchaseOrder" nillable="true" type="xsd:string"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
  </schema>
  <schema targetNamespace="http://amdocs/core" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://amdocs/amsp/valueobject/customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountparam"/>
   <import namespace="http://valueobject.amsp.amdocs"/>
   <import namespace="http://amdocs/amsp/valueobject/addressservice"/>
   <import namespace="http://*************:9003/amspop/services/Customer"/>
   <import namespace="http://amdocs/amsp/valueobject/accountview"/>
   <import namespace="http://exception.amsp.amdocs"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType name="Money">
    <sequence>
     <element name="currencyCode" nillable="true" type="xsd:string"/>
     <element name="decValue" nillable="true" type="xsd:decimal"/>
    </sequence>
   </complexType>
  </schema>
 </wsdl:types>

     <wsdl:message name="checkCustomerPersonalIdLoginRequest">

          <wsdl:part name="personalId" type="xsd:string"/>

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="userId" type="xsd:string"/>

          <wsdl:part name="password" type="xsd:string"/>

          <wsdl:part name="clientLocale" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPersonalIdLoginResponse">

          <wsdl:part name="checkCustomerPersonalIdLoginReturn" type="tns1:ProspectResultVO"/>

     </wsdl:message>

     <wsdl:message name="AmspServiceException">

          <wsdl:part name="fault" type="tns3:AmspServiceException"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPersonalIdRequest">

          <wsdl:part name="personalId" type="xsd:string"/>

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPersonalIdResponse">

          <wsdl:part name="checkCustomerPersonalIdReturn" type="tns1:ProspectResultVO"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPersonalIdNewTransRequest">

          <wsdl:part name="personalId" type="xsd:string"/>

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="transactionAttribute" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPersonalIdNewTransResponse">

          <wsdl:part name="checkCustomerPersonalIdNewTransReturn" type="tns1:ProspectResultVO"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPhoneLoginRequest">

          <wsdl:part name="homePhone" type="xsd:string"/>

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="siteId" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="userId" type="xsd:string"/>

          <wsdl:part name="password" type="xsd:string"/>

          <wsdl:part name="clientLocale" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPhoneLoginResponse">

          <wsdl:part name="checkCustomerPhoneLoginReturn" type="tns1:ProspectResultVO"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPhoneRequest">

          <wsdl:part name="homePhone" type="xsd:string"/>

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="siteId" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPhoneResponse">

          <wsdl:part name="checkCustomerPhoneReturn" type="tns1:ProspectResultVO"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPhoneNewTransRequest">

          <wsdl:part name="homePhone" type="xsd:string"/>

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="siteId" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="transactionAttribute" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="checkCustomerPhoneNewTransResponse">

          <wsdl:part name="checkCustomerPhoneNewTransReturn" type="tns1:ProspectResultVO"/>

     </wsdl:message>

     <wsdl:message name="getCustomerDetailLoginRequest">

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="skipPayCard" type="xsd:boolean"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="userId" type="xsd:string"/>

          <wsdl:part name="password" type="xsd:string"/>

          <wsdl:part name="clientLocale" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="getCustomerDetailLoginResponse">

          <wsdl:part name="getCustomerDetailLoginReturn" type="tns1:CustomerDetailsVO"/>

     </wsdl:message>

     <wsdl:message name="getCustomerDetailRequest">

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="skipPayCard" type="xsd:boolean"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="getCustomerDetailResponse">

          <wsdl:part name="getCustomerDetailReturn" type="tns1:CustomerDetailsVO"/>

     </wsdl:message>

     <wsdl:message name="getCustomerDetailNewTransRequest">

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="skipPayCard" type="xsd:boolean"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="transactionAttribute" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="getCustomerDetailNewTransResponse">

          <wsdl:part name="getCustomerDetailNewTransReturn" type="tns1:CustomerDetailsVO"/>

     </wsdl:message>

     <wsdl:message name="getExpandedNamesLoginRequest">

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="userId" type="xsd:string"/>

          <wsdl:part name="password" type="xsd:string"/>

          <wsdl:part name="clientLocale" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="getExpandedNamesLoginResponse">

          <wsdl:part name="getExpandedNamesLoginReturn" type="impl:ArrayOf_tns1_AccountExpandedNamesVO"/>

     </wsdl:message>

     <wsdl:message name="getExpandedNamesNewTransRequest">

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="transactionAttribute" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="getExpandedNamesNewTransResponse">

          <wsdl:part name="getExpandedNamesNewTransReturn" type="impl:ArrayOf_tns1_AccountExpandedNamesVO"/>

     </wsdl:message>

     <wsdl:message name="getSplitCustomersLoginRequest">

          <wsdl:part name="siteId" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="userId" type="xsd:string"/>

          <wsdl:part name="password" type="xsd:string"/>

          <wsdl:part name="clientLocale" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="getSplitCustomersLoginResponse">

          <wsdl:part name="getSplitCustomersLoginReturn" type="impl:ArrayOf_tns1_SplitCustomerDetailsVO"/>

     </wsdl:message>

     <wsdl:message name="getSplitCustomersRequest">

          <wsdl:part name="siteId" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="getSplitCustomersResponse">

          <wsdl:part name="getSplitCustomersReturn" type="impl:ArrayOf_tns1_SplitCustomerDetailsVO"/>

     </wsdl:message>

     <wsdl:message name="getSplitCustomersNewTransRequest">

          <wsdl:part name="siteId" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="transactionAttribute" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="getSplitCustomersNewTransResponse">

          <wsdl:part name="getSplitCustomersNewTransReturn" type="impl:ArrayOf_tns1_SplitCustomerDetailsVO"/>

     </wsdl:message>

     <wsdl:message name="saveProspectLoginRequest">

          <wsdl:part name="custDetail" type="tns1:CustomerDetailsVO"/>

          <wsdl:part name="siteID" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="transactionAttribute" type="xsd:string"/>

          <wsdl:part name="userId" type="xsd:string"/>

          <wsdl:part name="password" type="xsd:string"/>

          <wsdl:part name="clientLocale" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="saveProspectLoginResponse">

          <wsdl:part name="saveProspectLoginReturn" type="tns1:ProspectResultVO"/>

     </wsdl:message>

     <wsdl:message name="saveProspectNewTransRequest">

          <wsdl:part name="custDetail" type="tns1:CustomerDetailsVO"/>

          <wsdl:part name="siteID" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="transactionAttribute" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="saveProspectNewTransResponse">

          <wsdl:part name="saveProspectNewTransReturn" type="tns1:ProspectResultVO"/>

     </wsdl:message>

     <wsdl:message name="getExpandedNamesRequest">

          <wsdl:part name="custAcct" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="getExpandedNamesResponse">

          <wsdl:part name="getExpandedNamesReturn" type="impl:ArrayOf_tns1_AccountExpandedNamesVO"/>

     </wsdl:message>

     <wsdl:message name="saveProspectRequest">

          <wsdl:part name="custDetail" type="tns1:CustomerDetailsVO"/>

          <wsdl:part name="siteID" type="xsd:string"/>

          <wsdl:part name="corp" type="xsd:string"/>

          <wsdl:part name="ticket" type="xsd:string"/>

     </wsdl:message>

     <wsdl:message name="saveProspectResponse">

          <wsdl:part name="saveProspectReturn" type="tns1:ProspectResultVO"/>

     </wsdl:message>

     <wsdl:portType name="CustomerFacade">

          <wsdl:operation name="checkCustomerPersonalIdLogin" parameterOrder="personalId custAcct corp userId password clientLocale">

               <wsdl:input message="impl:checkCustomerPersonalIdLoginRequest" name="checkCustomerPersonalIdLoginRequest"/>

               <wsdl:output message="impl:checkCustomerPersonalIdLoginResponse" name="checkCustomerPersonalIdLoginResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="checkCustomerPersonalId" parameterOrder="personalId custAcct corp ticket">

               <wsdl:input message="impl:checkCustomerPersonalIdRequest" name="checkCustomerPersonalIdRequest"/>

               <wsdl:output message="impl:checkCustomerPersonalIdResponse" name="checkCustomerPersonalIdResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="checkCustomerPersonalIdNewTrans" parameterOrder="personalId custAcct corp transactionAttribute ticket">

               <wsdl:input message="impl:checkCustomerPersonalIdNewTransRequest" name="checkCustomerPersonalIdNewTransRequest"/>

               <wsdl:output message="impl:checkCustomerPersonalIdNewTransResponse" name="checkCustomerPersonalIdNewTransResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="checkCustomerPhoneLogin" parameterOrder="homePhone custAcct siteId corp userId password clientLocale">

               <wsdl:input message="impl:checkCustomerPhoneLoginRequest" name="checkCustomerPhoneLoginRequest"/>

               <wsdl:output message="impl:checkCustomerPhoneLoginResponse" name="checkCustomerPhoneLoginResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="checkCustomerPhone" parameterOrder="homePhone custAcct siteId corp ticket">

               <wsdl:input message="impl:checkCustomerPhoneRequest" name="checkCustomerPhoneRequest"/>

               <wsdl:output message="impl:checkCustomerPhoneResponse" name="checkCustomerPhoneResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="checkCustomerPhoneNewTrans" parameterOrder="homePhone custAcct siteId corp transactionAttribute ticket">

               <wsdl:input message="impl:checkCustomerPhoneNewTransRequest" name="checkCustomerPhoneNewTransRequest"/>

               <wsdl:output message="impl:checkCustomerPhoneNewTransResponse" name="checkCustomerPhoneNewTransResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="getCustomerDetailLogin" parameterOrder="custAcct skipPayCard corp userId password clientLocale">

               <wsdl:input message="impl:getCustomerDetailLoginRequest" name="getCustomerDetailLoginRequest"/>

               <wsdl:output message="impl:getCustomerDetailLoginResponse" name="getCustomerDetailLoginResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="getCustomerDetail" parameterOrder="custAcct skipPayCard corp ticket">

               <wsdl:input message="impl:getCustomerDetailRequest" name="getCustomerDetailRequest"/>

               <wsdl:output message="impl:getCustomerDetailResponse" name="getCustomerDetailResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="getCustomerDetailNewTrans" parameterOrder="custAcct skipPayCard corp transactionAttribute ticket">

               <wsdl:input message="impl:getCustomerDetailNewTransRequest" name="getCustomerDetailNewTransRequest"/>

               <wsdl:output message="impl:getCustomerDetailNewTransResponse" name="getCustomerDetailNewTransResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="getExpandedNamesLogin" parameterOrder="custAcct corp userId password clientLocale">

               <wsdl:input message="impl:getExpandedNamesLoginRequest" name="getExpandedNamesLoginRequest"/>

               <wsdl:output message="impl:getExpandedNamesLoginResponse" name="getExpandedNamesLoginResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="getExpandedNamesNewTrans" parameterOrder="custAcct corp transactionAttribute ticket">

               <wsdl:input message="impl:getExpandedNamesNewTransRequest" name="getExpandedNamesNewTransRequest"/>

               <wsdl:output message="impl:getExpandedNamesNewTransResponse" name="getExpandedNamesNewTransResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="getSplitCustomersLogin" parameterOrder="siteId corp userId password clientLocale">

               <wsdl:input message="impl:getSplitCustomersLoginRequest" name="getSplitCustomersLoginRequest"/>

               <wsdl:output message="impl:getSplitCustomersLoginResponse" name="getSplitCustomersLoginResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="getSplitCustomers" parameterOrder="siteId corp ticket">

               <wsdl:input message="impl:getSplitCustomersRequest" name="getSplitCustomersRequest"/>

               <wsdl:output message="impl:getSplitCustomersResponse" name="getSplitCustomersResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="getSplitCustomersNewTrans" parameterOrder="siteId corp transactionAttribute ticket">

               <wsdl:input message="impl:getSplitCustomersNewTransRequest" name="getSplitCustomersNewTransRequest"/>

               <wsdl:output message="impl:getSplitCustomersNewTransResponse" name="getSplitCustomersNewTransResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="saveProspectLogin" parameterOrder="custDetail siteID corp transactionAttribute userId password clientLocale">

               <wsdl:input message="impl:saveProspectLoginRequest" name="saveProspectLoginRequest"/>

               <wsdl:output message="impl:saveProspectLoginResponse" name="saveProspectLoginResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="saveProspectNewTrans" parameterOrder="custDetail siteID corp transactionAttribute ticket">

               <wsdl:input message="impl:saveProspectNewTransRequest" name="saveProspectNewTransRequest"/>

               <wsdl:output message="impl:saveProspectNewTransResponse" name="saveProspectNewTransResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="getExpandedNames" parameterOrder="custAcct corp ticket">

               <wsdl:input message="impl:getExpandedNamesRequest" name="getExpandedNamesRequest"/>

               <wsdl:output message="impl:getExpandedNamesResponse" name="getExpandedNamesResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

          <wsdl:operation name="saveProspect" parameterOrder="custDetail siteID corp ticket">

               <wsdl:input message="impl:saveProspectRequest" name="saveProspectRequest"/>

               <wsdl:output message="impl:saveProspectResponse" name="saveProspectResponse"/>

               <wsdl:fault message="impl:AmspServiceException" name="AmspServiceException"/>

          </wsdl:operation>

     </wsdl:portType>

     <wsdl:binding name="CustomerSoapBinding" type="impl:CustomerFacade">

          <wsdlsoap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>

          <wsdl:operation name="checkCustomerPersonalIdLogin">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="checkCustomerPersonalIdLoginRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="checkCustomerPersonalIdLoginResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="checkCustomerPersonalId">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="checkCustomerPersonalIdRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="checkCustomerPersonalIdResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="checkCustomerPersonalIdNewTrans">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="checkCustomerPersonalIdNewTransRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="checkCustomerPersonalIdNewTransResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="checkCustomerPhoneLogin">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="checkCustomerPhoneLoginRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="checkCustomerPhoneLoginResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="checkCustomerPhone">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="checkCustomerPhoneRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="checkCustomerPhoneResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="checkCustomerPhoneNewTrans">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="checkCustomerPhoneNewTransRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="checkCustomerPhoneNewTransResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="getCustomerDetailLogin">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="getCustomerDetailLoginRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="getCustomerDetailLoginResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="getCustomerDetail">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="getCustomerDetailRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="getCustomerDetailResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="getCustomerDetailNewTrans">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="getCustomerDetailNewTransRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="getCustomerDetailNewTransResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="getExpandedNamesLogin">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="getExpandedNamesLoginRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="getExpandedNamesLoginResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="getExpandedNamesNewTrans">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="getExpandedNamesNewTransRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="getExpandedNamesNewTransResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="getSplitCustomersLogin">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="getSplitCustomersLoginRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="getSplitCustomersLoginResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="getSplitCustomers">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="getSplitCustomersRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="getSplitCustomersResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="getSplitCustomersNewTrans">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="getSplitCustomersNewTransRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="getSplitCustomersNewTransResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="saveProspectLogin">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="saveProspectLoginRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="saveProspectLoginResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="saveProspectNewTrans">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="saveProspectNewTransRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="saveProspectNewTransResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="getExpandedNames">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="getExpandedNamesRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="getExpandedNamesResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

          <wsdl:operation name="saveProspect">

               <wsdlsoap:operation soapAction=""/>

               <wsdl:input name="saveProspectRequest">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://services.amsp.amdocs" use="encoded"/>

               </wsdl:input>

               <wsdl:output name="saveProspectResponse">

                    <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:output>

               <wsdl:fault name="AmspServiceException">

                    <wsdlsoap:fault encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" name="AmspServiceException" namespace="http://*************:9003/amspop/services/Customer" use="encoded"/>

               </wsdl:fault>

          </wsdl:operation>

     </wsdl:binding>

     <wsdl:service name="CustomerFacadeService">

          <wsdl:port binding="impl:CustomerSoapBinding" name="Customer">

               <wsdlsoap:address location="http://*************:9003/amspop/services/Customer"/>

          </wsdl:port>

     </wsdl:service>

</wsdl:definitions>
