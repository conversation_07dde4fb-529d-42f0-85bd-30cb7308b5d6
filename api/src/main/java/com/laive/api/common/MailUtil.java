package com.laive.api.common;

import javax.mail.Address;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MailUtil {

  @Autowired
  private JavaMailSender javaMailSender;


  @Async
  public void sendMail(MimeMessage message) {
    javaMailSender.send(message);

    if (log.isDebugEnabled()) {
      try {
        Address[] addresses = message.getRecipients(Message.RecipientType.TO);
        for(Address address : addresses) {
          log.debug(String.format("mail sent to : %s", address.toString()));
        }
      } catch (MessagingException e) {
        // ignored
      }
    }
  }

}
