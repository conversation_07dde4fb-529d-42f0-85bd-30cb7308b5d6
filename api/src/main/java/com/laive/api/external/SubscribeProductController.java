package com.laive.api.external;

import com.laive.api.common.ApiUriConstants;
import com.laive.core.annotation.ApiKeyRestricted;
import com.laive.core.base.BaseController;
import com.laive.core.domain.ChannelPackage;
import com.laive.core.service.ChannelPackageManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
public class SubscribeProductController extends BaseController {


  private final ChannelPackageManager channelPackageManager;

  @RequestMapping(value = ApiUriConstants.SUBSCRIBE_PRODUCT_LIST)
  @ApiKeyRestricted
  public SubscribeProductListResponseVO subscribeProductList(@RequestParam String platformId) {

    List<ChannelPackage> channelPackages = channelPackageManager.getAllListHavingPrices(platformId);
    return SubscribeProductListResponseVO.builder().channelPackages(channelPackages).build();
  }

}
