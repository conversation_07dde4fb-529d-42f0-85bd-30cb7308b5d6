package com.laive.api.oauth;

import com.laive.core.base.BaseException;
import com.laive.core.constants.ReturnCodes;
public class OAuthExpiredTokenException extends BaseException {

  private static final long serialVersionUID = -8902369501586567276L;


  public OAuthExpiredTokenException(Throwable cause) {
    super(cause);
  }

  public OAuthExpiredTokenException() {

  }

  @Override
  public String getExceptionCode() {
    return ReturnCodes.ERROR_ACCESS_TOKEN_EXPIRED;
  }


}
