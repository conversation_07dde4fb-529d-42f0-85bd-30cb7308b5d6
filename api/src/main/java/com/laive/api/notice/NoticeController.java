package com.laive.api.notice;

import com.laive.api.common.ApiUriConstants;
import com.laive.core.base.BaseController;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.domain.Notice;
import com.laive.core.service.NoticeManager;
import com.laive.core.util.CollectionUtils;
import com.laive.core.util.DateUtil;
import com.laive.core.util.ParameterUtil;
import com.laive.core.vo.NoticeListVO;
import com.laive.core.web.util.TimezoneUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
public class NoticeController extends BaseController {

  @Autowired
  private NoticeManager noticeManager;

  @Autowired
  private TimezoneUtil timezoneUtil;

  @Autowired
  private HttpServletRequest request;

  @RequestMapping(value = ApiUriConstants.NOTICE_LIST)
  public NoticeListVO list(NoticeListVO listVO) {
    ParameterUtil.checkParameterEmpty(listVO.getSearch().getPlatformId());
    listVO = noticeManager.getListVO(listVO);

    String timezone = timezoneUtil.getTimezone(request, listVO.getTimezone());
    listVO = adjustTimezoneToNoticeDt(listVO, timezone);
    listVO.setReturnCode(ReturnCodes.OK);
    return listVO;
  }

  private NoticeListVO adjustTimezoneToNoticeDt(NoticeListVO listVO, String timezone) {
    List<Notice> list = listVO.getList();
    if (CollectionUtils.isEmpty(list))
      return listVO;

    for (Notice notice : list) {
      notice.setUpdateDtString(DateUtil.getDateString("yyyyMMdd", notice.getUpdatedDt(), timezone));
    }
    return listVO;
  }

  @RequestMapping(value = ApiUriConstants.NOTICE_DETAIL)
  public NoticeResponseVO list(String noticeId) {
    ParameterUtil.checkParameterEmpty(noticeId);
    Notice notice = noticeManager.get(noticeId);
    String timezone = timezoneUtil.getTimezone(request);
    notice.setUpdateDtString(DateUtil.getDateString("yyyyMMdd", notice.getUpdatedDt(), timezone));
    return NoticeResponseVO.getInstance(notice);
  }

}
