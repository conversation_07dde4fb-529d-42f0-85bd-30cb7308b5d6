package com.laive.api.watch;

import com.laive.api.common.ApiUriConstants;
import com.laive.core.base.BaseController;
import com.laive.core.base.ResponseVO;
import com.laive.core.config.RedisCacheConfig;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.exception.BusinessException;
import com.laive.core.service.WatchVodLogManager;
import com.laive.core.util.ConfigUtil;
import com.laive.core.util.ParameterUtil;
import com.laive.core.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
public class WatchUserController extends BaseController {

  @Autowired
  private WatchVodLogManager watchVodLogManager;

  @Autowired
  private WatchUserService service;

  @Autowired
  private CacheManager cacheManager;

  @Autowired
  private ConfigUtil configUtil;

  @RequestMapping(value = ApiUriConstants.WATCH_USER_CHECK_WATCHING, method = RequestMethod.GET)
  public ResponseVO checkWatching() {
    String userId = getCurrentUser().getId();
    boolean watching = watchVodLogManager.existWatchVodLogOnWatching(userId);
    if (watching)
      throw new BusinessException(ReturnCodes.ERROR_EXIST_WATCHING_VOD);
    return ResponseVO.ok();
  }

  @RequestMapping(value = ApiUriConstants.WATCH_USER_KICKOUT, method = RequestMethod.GET)
  public ResponseVO kickout(@RequestParam Long watchTimestamp) {
    String userId = getCurrentUser().getId();
    ParameterUtil.checkParameterEmpty(watchTimestamp);

    Cache cache = cacheManager.getCache(RedisCacheConfig.CACHE_WATCH_USER_TIMESTAMP);
    List<Long> list;
    if (cache.get(userId) == null) {
      list = new ArrayList<>();
    } else {
      list = (List<Long>) cache.get(userId).get();
    }
    list.add(watchTimestamp);

    List<Long> watchTimestampList = setCache(userId, cache, list);

    service.kickout(userId, watchTimestampList);
    return ResponseVO.ok();
  }

  private List<Long> setCache(String userId, Cache cache, List<Long> list) {
    Long concurrentWatchUserCount = configUtil.getConcurrentWatchUserCount();
    List<Long> watchTimestampList = list.stream().sorted(Comparator.reverseOrder()).limit(concurrentWatchUserCount).collect(Collectors.toList());
    cache.put(userId, watchTimestampList);
    return watchTimestampList;
  }

  @RequestMapping(value = ApiUriConstants.WATCH_USER_FINISH, method = RequestMethod.GET)
  public ResponseVO finish(Long watchTimestamp) {
    String userId = getCurrentUser().getId();
    ParameterUtil.checkParameterEmpty(watchTimestamp);

    Cache cache = cacheManager.getCache(RedisCacheConfig.CACHE_WATCH_USER_TIMESTAMP);
    if (cache.get(userId) == null) {
      return ResponseVO.ok();
    }

    List<Long> list = (List<Long>) cache.get(userId).get();
    list.removeIf(l -> Objects.equals(l, watchTimestamp));

    setCache(userId, cache, list);

    return ResponseVO.ok();
  }


  @RequestMapping(value = ApiUriConstants.WATCH_USER_SEND_PUSH )
  public ResponseVO sendPush(String topic, String message) {
    ParameterUtil.checkParameterEmpty(topic, message);
    service.sendPush(topic, message);
    return ResponseVO.ok();
  }

  @RequestMapping(value = "/watch_user/cache", method = RequestMethod.GET)
  public String cacheCheck(String userId, Long watchTimestamp) {
    Cache cache = cacheManager.getCache(RedisCacheConfig.CACHE_WATCH_USER_TIMESTAMP);
    List<Long> list;
    if (cache.get(userId) == null) {
      list = new ArrayList<>();
    } else {
      list = (List<Long>) cache.get(userId).get();
    }
    list.add(watchTimestamp);

    setCache(userId, cache, list);

    Cache.ValueWrapper element = cache.get(userId);
    List<Long> cacheList = (List<Long>) element.get();
    return String.format("%s--->%s", userId, StringUtils.join(cacheList, ","));
  }


}
