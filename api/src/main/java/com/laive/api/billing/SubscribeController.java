package com.laive.api.billing;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.laive.api.common.ApiUriConstants;
import com.laive.core.base.BaseController;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.service.CategorySubscriptionManager;
import com.laive.core.service.ChannelSubscriptionManager;
import com.laive.core.vo.ChannelSubscriptionListVO;
import com.laive.core.vo.SubscribeListVO;
import com.laive.core.web.util.TimezoneUtil;

@RestController
public class SubscribeController extends BaseController {

  @Autowired
  private CategorySubscriptionManager categorySubscriptionManager;

  @Autowired
  private ChannelSubscriptionManager channelSubscriptionManager;

  @Autowired
  private HttpServletRequest request;

  @Autowired
  private TimezoneUtil timezoneUtil;

  @RequestMapping(value = ApiUriConstants.BILLING_LIST_CATEGORY_SUBSCRIBE, method = RequestMethod.GET)
  public SubscribeListVO list(SubscribeListVO listVO) {
    listVO.setReturnCode(ReturnCodes.OK);
    listVO.setUserId(getCurrentUser().getId());
    String timezone = timezoneUtil.getTimezone(request, listVO.getTimezone());
    listVO.setTimezone(timezone);
    listVO.setUsePurchasedListRule(true);
    listVO.setExcludeDeletedPayments(true);
    return categorySubscriptionManager.getList(listVO);
  }

  @RequestMapping(value = ApiUriConstants.BILLING_LIST_CHANNEL_SUBSCRIBE, method = RequestMethod.GET)
  public ChannelSubscriptionListVO list(ChannelSubscriptionListVO listVO) {
    listVO.setReturnCode(ReturnCodes.OK);
    listVO.setUserId(getCurrentUser().getId());
    String timezone = timezoneUtil.getTimezone(request, listVO.getTimezone());
    listVO.setTimezone(timezone);
    listVO.setUsePurchasedListRule(true);
    listVO.setExcludeDeletedPayments(true);
    return channelSubscriptionManager.getList(listVO);
  }

}
