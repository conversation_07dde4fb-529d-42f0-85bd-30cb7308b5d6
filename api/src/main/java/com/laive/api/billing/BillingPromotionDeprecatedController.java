package com.laive.api.billing;

import com.laive.api.common.ApiUriConstants;
import com.laive.core.annotation.IpRestricted;
import com.laive.core.base.BaseController;
import com.laive.core.base.ResponseVO;
import com.laive.core.billing.BillingRequestVO;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.domain.BillingLog;
import com.laive.core.domain.BillingMethod;
import com.laive.core.domain.Category;
import com.laive.core.domain.CategoryPrice;
import com.laive.core.domain.CategorySubscription;
import com.laive.core.domain.ChannelPackage;
import com.laive.core.domain.ChannelPackagePrice;
import com.laive.core.domain.ChannelPackageSubscription;
import com.laive.core.domain.Platform;
import com.laive.core.domain.PurchaseType;
import com.laive.core.domain.User;
import com.laive.core.domain.VodPrice;
import com.laive.core.exception.BusinessException;
import com.laive.core.service.BillingLogManager;
import com.laive.core.service.CategoryManager;
import com.laive.core.service.CategorySubscriptionManager;
import com.laive.core.service.ChannelPackageManager;
import com.laive.core.service.ChannelPackageSubscriptionManager;
import com.laive.core.service.PlatformManager;
import com.laive.core.service.UserManager;
import com.laive.core.service.VodPriceManager;
import com.laive.core.util.CollectionUtils;
import com.laive.core.util.ParameterUtil;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.BillingResponseVO;
import com.laive.core.web.interceptor.ApiSecurityInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/deprecated")
public class BillingPromotionDeprecatedController extends BaseController {

  @Autowired
  private UserManager userManager;

  @Autowired
  private BillingController billingController;

  @Autowired
  private ChannelPackageManager channelPackageManager;

  @Autowired
  private PlatformManager platformManager;

  @Autowired
  private CategoryManager categoryManager;

  @Autowired
  private VodPriceManager vodPriceManager;

  @Autowired
  private CategorySubscriptionManager categorySubscriptionManager;

  @Autowired
  private ChannelPackageSubscriptionManager channelPackageSubscriptionManager;

  @Autowired
  private BillingLogManager billingLogManager;

  @RequestMapping(value = ApiUriConstants.BILLING_PROMOTION_CREATE, method = RequestMethod.POST)
  @IpRestricted(caller = ApiSecurityInterceptor.CALLER_PROMOTION)
  public BillingResponseVO create(BillingPromotionRequestVO requestVO) {
    ParameterUtil.checkParameterEmpty(requestVO.getPlatformName(), requestVO.getPurchaseType(), requestVO.getUserEmail(), requestVO.getDuration());

    Platform platform = platformManager.getByName(requestVO.getPlatformName());
    if (platform == null)
      throw new BusinessException(ReturnCodes.ERROR_ENTITY_NOT_EXIST, String.format("platform with name(%s) is not found.", requestVO.getPlatformName()));

    String productPriceId = getProductPriceIdByPurchaseType(platform.getId(), requestVO.getPurchaseType(), requestVO.getDuration(), requestVO.getTitle());
    if (StringUtils.isBlank(productPriceId))
      throw new BusinessException(ReturnCodes.ERROR_ENTITY_NOT_EXIST, "productPriceId is null");

    User user = userManager.getUserByEmail(requestVO.getUserEmail());
    if (user == null) {
      throw new BusinessException(ReturnCodes.ERROR_ENTITY_NOT_EXIST, String.format("user with email(%s) is not found.", requestVO.getUserEmail()));
    }

    BillingRequestVO billingRequestVO = new BillingRequestVO();
    billingRequestVO.setPurchaseType(requestVO.getPurchaseType());
    billingRequestVO.setProductPriceId(productPriceId);
    billingRequestVO.setUserId(user.getId());
    billingRequestVO.setPromotion(true);
    billingRequestVO.setEmail(user.getEmail());
    billingRequestVO.setStartTime(new Date().getTime());

    return billingController.create(billingRequestVO);
  }

  private String getProductPriceIdByPurchaseType(String platformId, PurchaseType purchaseType, Integer duration, String title) {
    String result = null;
    if (PurchaseType.CHANNEL_PACKAGE.equals(purchaseType)) {
      ChannelPackage channelPackage = channelPackageManager.getByPlatformId(platformId);
      if (channelPackage == null)
        throw new BusinessException(ReturnCodes.ERROR_ENTITY_NOT_EXIST, String.format("channelPackage with platform(%s) is not found.", platformId));

      ChannelPackagePrice channelPackagePrice = channelPackage.getChannelPackagePriceByDuration(duration);
      if (channelPackagePrice == null)
        throw new BusinessException(ReturnCodes.ERROR_ENTITY_NOT_EXIST, String.format("channelPackagePrice with duration(%d) is not found.", duration));

      result = channelPackagePrice.getId();

    } else if (PurchaseType.SUBSCRIBE.equals(purchaseType)) {

      Category category = categoryManager.getRootCategory(platformId);
      List<Category> saleableChildCategories = category.getSaleableChildCategories(true);
      if (CollectionUtils.isEmpty(saleableChildCategories))
        throw new BusinessException(ReturnCodes.ERROR_ENTITY_NOT_EXIST, String.format("saleableChildCategories with platform(%s) is not found.", platformId));

      CategoryPrice categoryPrice = findCategoryPriceByDuration(saleableChildCategories, duration);
      if (categoryPrice == null)
        throw new BusinessException(ReturnCodes.ERROR_ENTITY_NOT_EXIST, String.format("categoryPrice with duration(%d) is not found.", duration));

      result = categoryPrice.getId();

    } else if (PurchaseType.RENT.equals(purchaseType)) {

      VodPrice vodPrice = vodPriceManager.getByPlatformIdAndVodTitleAndPeriod(platformId, title, duration);
      if (vodPrice == null)
        throw new BusinessException(ReturnCodes.ERROR_ENTITY_NOT_EXIST, String.format("vodPrice with platform(%s), title(%s), period(%d) is not found.", platformId, title, duration));

      result = vodPrice.getId();
    }

    return result;
  }

  private CategoryPrice findCategoryPriceByDuration(List<Category> saleableChildCategories, Integer duration) {

    CategoryPrice categoryPrice = null;
    for (Category category : saleableChildCategories) {
      categoryPrice = category.getCategoryPriceByDuration(duration);
      if (categoryPrice != null)
        return categoryPrice;
    }
    return null;
  }

  @RequestMapping(value = ApiUriConstants.BILLING_ACTIVATE_PROMOTION, method = RequestMethod.POST)
  public BillingResponseVO activatePromotion(BillingPromotionMobileRequestVO requestVO) {
    ParameterUtil.checkParameterEmpty(requestVO.getPurchaseType(), requestVO.getMobile(), requestVO.getProductId());

    User user = userManager.getUserByMobile(requestVO.getMobile());
    if (user == null) {
      throw new BusinessException(ReturnCodes.ERROR_USER_NOT_FOUND, String.format("verified mobile(%s) is not found.", requestVO.getMobile()));
    }

    userManager.merge(user);

    BillingMethod billingMethod = requestVO.getPaymentType() == null ? BillingMethod.PROMOTION_MOBILE : requestVO
            .getPaymentType();
    String billingId = createSubscription(requestVO.getPurchaseType(), user, requestVO.getProductId(), billingMethod);

    createBillingLog(requestVO, billingId, user.getId(), billingMethod);

    BillingResponseVO responseVO = BillingResponseVO.createInstance(billingId);
    return responseVO;
  }

  private void createBillingLog(BillingPromotionMobileRequestVO requestVO, String billingId, String userId,
                                BillingMethod billingMethod) {
    BillingLog billingLog = new BillingLog();
    billingLog.setBillingMethod(billingMethod);
    billingLog.setUserId(userId);
    billingLog.setPurchaseType(requestVO.getPurchaseType());
    billingLog.setPaymentId(billingId);
    billingLog.setCreatedDt(new Date());
    billingLogManager.merge(billingLog);
  }

  private String createSubscription(PurchaseType purchaseType, User user, String productId, BillingMethod billingMethod) {
    String paymentId;
    if (purchaseType.equals(PurchaseType.SUBSCRIBE)) {
      paymentId = createCategorySubscription(user, productId, billingMethod);
    } else if (purchaseType.equals(PurchaseType.CHANNEL_PACKAGE)) {
      paymentId = createChannelPackageSubscriptionSubscription(user, productId, billingMethod);
    } else {
      throw new BusinessException(ReturnCodes.ERROR_INVALID_PURCHASE_TYPE, String.format("purchaseType(%s) is invalid.", purchaseType));
    }

    return paymentId;
  }


  private String createCategorySubscription(User user, String categoryId, BillingMethod billingMethod) {
    CategorySubscription categorySubscription;
    categorySubscription = categorySubscriptionManager.getRecentActiveCategorySubscription(categoryId, user.getId(), billingMethod);

    if (categorySubscription == null) {
      Category category = categoryManager.get(categoryId);
      categorySubscription = CategorySubscription.createCategorySubscriptionbyPromotionMobile(category, user, billingMethod);
      categorySubscription = categorySubscriptionManager.merge(categorySubscription);
    } else if(!categorySubscription.isInfinite()) {
      categorySubscription.setInfinite();
      categorySubscription = categorySubscriptionManager.merge(categorySubscription);
    } else {
      throw new BusinessException(ReturnCodes.ERROR_ENTITY_ALREADY_EXISTS,
          String.format("categorySubscription(id:%s, userId:%s, categoryId:%s) already exists", categorySubscription.getId(), user.getId(), categoryId));
    }

    return categorySubscription.getId();
  }

  private String createChannelPackageSubscriptionSubscription(User user, String channelPackageId, BillingMethod billingMethod) {
    ChannelPackageSubscription channelPackageSubscription;
    channelPackageSubscription = channelPackageSubscriptionManager.getRecentActiveSubscription(channelPackageId, user.getId(), billingMethod);

    if (channelPackageSubscription == null) {
      ChannelPackage channelPackage = channelPackageManager.get(channelPackageId);
      channelPackageSubscription =
          ChannelPackageSubscription.createChannelPackageSubscriptionByPromotionMobile(channelPackage, user, billingMethod);
      channelPackageSubscription = channelPackageSubscriptionManager.merge(channelPackageSubscription);
    } else if(channelPackageSubscription.isInfinite()) {
      channelPackageSubscription.setInfinite();
      channelPackageSubscription = channelPackageSubscriptionManager.merge(channelPackageSubscription);
    } else {
      log.warn(String.format("channelPackageSubscription(id:%s, userId:%s, categoryId:%s) already exists", channelPackageSubscription.getId(), user.getId(), channelPackageId));
    }
    return channelPackageSubscription.getId();
  }


  @RequestMapping(value = ApiUriConstants.BILLING_INACTIVATE_PROMOTION, method = RequestMethod.POST)
  public ResponseVO inactivatePromotion(BillingPromotionMobileRequestVO requestVO) {
    ParameterUtil.checkParameterEmpty(requestVO.getPurchaseType(), requestVO.getMobile(), requestVO.getProductId());

    User user = userManager.getUserByMobile(requestVO.getMobile());
    if (user == null) {
      throw new BusinessException(ReturnCodes.ERROR_USER_NOT_FOUND, String.format("verified mobile(%s) is not found.", requestVO.getMobile()));
    }
    BillingMethod billingMethod = requestVO.getPaymentType() == null ? BillingMethod.PROMOTION_MOBILE : requestVO
            .getPaymentType();
    expireSubscription(requestVO.getPurchaseType(), user, requestVO.getProductId(), billingMethod);

    return ResponseVO.ok();
  }

  private void expireSubscription(PurchaseType purchaseType, User user, String productId, BillingMethod billingMethod) {
    if (purchaseType.equals(PurchaseType.SUBSCRIBE)) {
      expireCategorySubscription(user, productId, billingMethod);
    } else if (purchaseType.equals(PurchaseType.CHANNEL_PACKAGE)) {
      expireChannelPackageSubscriptionSubscription(user, productId, billingMethod);
    } else {
      throw new BusinessException(ReturnCodes.ERROR_INVALID_PURCHASE_TYPE, String.format("purchaseType(%s) is invalid.", purchaseType));
    }
  }


  private void expireCategorySubscription(User user, String categoryId, BillingMethod billingMethod) {
    categorySubscriptionManager.expirePromotionMobile(user, categoryId, billingMethod);
  }

  private void expireChannelPackageSubscriptionSubscription(User user, String channelPackageId, BillingMethod billingMethod) {
    channelPackageSubscriptionManager.expirePromotionMobile(user, channelPackageId, billingMethod);
  }

  @RequestMapping(value = ApiUriConstants.BILLING_PRODUCT_LIST, method = RequestMethod.GET)
  public BillingProductListVO productListVO(BillingProductListVO listVO) {
    ParameterUtil.checkParameterEmpty(listVO.getPlatformName(), listVO.getPurchaseType());

    Platform platform = platformManager.getByName(listVO.getPlatformName());
    if (platform == null)
      throw new BusinessException(ReturnCodes.ERROR_ENTITY_NOT_EXIST, String.format("platform with name(%s) is not found.", listVO.getPlatformName()));

    String platformId = platform.getId();
    PurchaseType purchaseType = listVO.getPurchaseType();

    BillingProductListVO responseVO;
    if (purchaseType.equals(PurchaseType.SUBSCRIBE)) {
      List<Category> categories = categoryManager.getSvodCategoriesWithPrices(platformId);
      responseVO = BillingProductListVO.createByCategoryList(categories);
    } else if (purchaseType.equals(PurchaseType.CHANNEL_PACKAGE)) {
      List<ChannelPackage> channelPackages = channelPackageManager.getAllListHavingChildChannel(platformId);
      responseVO = BillingProductListVO.createByChannelPackageList(channelPackages);
    } else {
      throw new BusinessException(ReturnCodes.ERROR_INVALID_PURCHASE_TYPE, String.format("purchaseType(%s) is invalid.", purchaseType));
    }


    return responseVO;
  }


}
