package com.laive.api.billing;

import com.laive.api.billing.base.BillingBaseController;
import com.laive.api.common.ApiUriConstants;
import com.laive.api.watch.WatchUserService;
import com.laive.core.annotation.ApiKeyRestricted;
import com.laive.core.annotation.IpRestricted;
import com.laive.core.base.ResponseVO;
import com.laive.core.billing.BillingRequestVO;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.domain.PurchaseType;
import com.laive.core.domain.User;
import com.laive.core.util.ParameterUtil;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.BillingResponseVO;
import com.laive.core.web.interceptor.ApiSecurityInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class BillingController extends BillingBaseController {

    @Autowired
    protected WatchUserService watchUserService;

    @RequestMapping(value = ApiUriConstants.BILLING_CREATE, method = RequestMethod.POST)
    @ApiKeyRestricted
    public BillingResponseVO create(BillingRequestVO requestVO) {
        ParameterUtil.checkParameterEmpty(requestVO.getProductPriceId(), requestVO.getPurchaseType(), requestVO.getStartTime());
        User user = getBillingRequestUser(requestVO);
        requestVO.setUserId(user.getId());

        return processBilling(requestVO);
    }

    @RequestMapping(value = ApiUriConstants.BILLING_QUIT, method = RequestMethod.POST)
    @ApiKeyRestricted
    public ResponseVO quit(BillingRequestVO requestVO) {
        ParameterUtil.checkParameterEmpty(requestVO.getProductPriceId(), requestVO.getPurchaseType(), requestVO.getEndTime());

        User user = getBillingRequestUser(requestVO);
        requestVO.setUserId(user.getId());

        ResponseVO responseVO;
        if (PurchaseType.SUBSCRIBE.equals(requestVO.getPurchaseType())) {
            responseVO = billingService.quitCategorySubscription(requestVO, getBillingRequestUser(requestVO.getUserId()));
            if (responseVO.getReturnCode().equalsIgnoreCase(ReturnCodes.OK)) {
                watchUserService.logoutWithoutNotification(user.getId(), "Expire date updated!");
            }
        } else if (PurchaseType.CHANNEL_PACKAGE.equals(requestVO.getPurchaseType())) {
            responseVO = billingService.quitChannelPackageSubscription(requestVO, getBillingRequestUser(requestVO.getUserId()));
            if (responseVO.getReturnCode().equalsIgnoreCase(ReturnCodes.OK)) {
                watchUserService.logoutWithoutNotification(user.getId(), "Expire date updated!");
            }
        } else {
            responseVO = billingService.quitVodPayment(requestVO, getBillingRequestUser(requestVO.getUserId()));
        }
        return responseVO;
    }

    @RequestMapping(value = ApiUriConstants.BILLING_RESUME_SUBSCRIBE, method = RequestMethod.POST)
    @IpRestricted(caller = ApiSecurityInterceptor.CALLER_BILLING_SYSTEM)
    public ResponseVO resumeSubscribe(BillingRequestVO requestVO) {
        // TODO 임시
        if (StringUtils.isBlank(requestVO.getUserId()))
            requestVO.setUserId(getCurrentUser().getId());

        ParameterUtil.checkParameterEmpty(requestVO.getPaymentId(), requestVO.getPurchaseType(), requestVO.getUserId());
        ResponseVO responseVO = null;
        if (PurchaseType.SUBSCRIBE.equals(requestVO.getPurchaseType())) {
            responseVO = billingService.resumeCategorySubscription(requestVO, getBillingRequestUser(requestVO.getUserId()));
        } else if (PurchaseType.CHANNEL.equals(requestVO.getPurchaseType())) {
            responseVO = billingService.resumeChannelSubscription(requestVO, getBillingRequestUser(requestVO.getUserId()));
        } else if (PurchaseType.CHANNEL_PACKAGE.equals(requestVO.getPurchaseType())) {
            responseVO = billingService.resumeChannelPackageSubscription(requestVO, getBillingRequestUser(requestVO.getUserId()));
        } else {
            throw new RuntimeException("resume only for subscribe");
        }
        return responseVO;
    }


}
