package com.laive.core.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.security.crypto.codec.Base64;

@Slf4j
public class CryptoTest {

    @Test
    public void testEncrypt() throws Exception {
       log.debug(Crypto.getInstance().encrypt("111111"));
       log.debug(Crypto.getInstance().encrypt("laive4430"));
       log.debug(Crypto.getInstance().encrypt("<EMAIL>|a2lsbGVyMjMwIUAhQCM"));
       log.debug(Crypto.getInstance().encrypt("<EMAIL>"));

       String str = "killer230!@!@#";
       log.debug(new String(Base64.encode(str.getBytes("UTF-8")), "UTF-8"));
       log.debug(org.apache.commons.net.util.Base64.encodeBase64String(str.getBytes("UTF-8")));
       log.debug(new String(org.apache.commons.net.util.Base64.decodeBase64("a2lsbGVyMjMwIUAhQCM"), "UTF-8"));
    }

}
