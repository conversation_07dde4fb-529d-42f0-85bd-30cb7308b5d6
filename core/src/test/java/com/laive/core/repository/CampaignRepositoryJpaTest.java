package com.laive.core.repository;

import com.laive.core.base.BaseRepositoryTestCase;
import com.laive.core.domain.AdExposeLocation;
import com.laive.core.domain.Campaign;
import com.laive.core.domain.CampaignType;
import com.laive.core.vo.CampaignListVO;
import com.mysema.query.types.Order;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

public class CampaignRepositoryJpaTest extends BaseRepositoryTestCase {

  @Autowired
  private CampaignRepository repository;

  @Test
  public void testGetCampaigns() {
    CampaignListVO listVO = new CampaignListVO();
    listVO.setPage(1);
    listVO.setPageSize(5);
    listVO.setPlatformId("sk");
    listVO.setSortTargetColumn("rate");
    listVO.setAscendingOrder(Order.DESC);
    listVO = repository.getCampaigns(listVO);
    for (Campaign item : listVO.getList()) {
      log.debug(item.toString());
    }
  }

  @Test
  public void getCampaignsByCategoryIdList() {
    List<String> categoryIdList = Arrays.asList("2", "6");
    repository.getCampaignInventoryListByCategoryIdList(categoryIdList, CampaignType.PRE);
  }

  @Test
  public void testCampaignExposeCountMap() {
    repository.getCampaignExposeCountMap(Arrays.asList("1", "2"));
  }

  @Test
  public void testGetActiveAdImagesMapByLocation() {
    repository.getActiveAdImagesMapByLocation("sk", AdExposeLocation.BANNER_MAIN);
  }
}
