package com.laive.core.repository;

import com.laive.core.base.BaseRepositoryTestCase;
import com.laive.core.domain.Device;
import org.junit.Test;

import javax.inject.Inject;



public class DeviceRepositoryJpaTest extends BaseRepositoryTestCase {

  @Inject
  DeviceRepository repository;

  @Test
  public void testGet() throws Exception {
    String userId = "402880bd438bdf4c01438be65e9e0000";
    Integer slot = 1;
    Device device = repository.get(userId, slot);
    log.debug(device);
  }

  @Test
  public void testGetAllByPlatformId() throws Exception {
    log.debug(repository.getAllByPlatformId("sk"));
  }


}
