package com.laive.core.repository;

import com.laive.core.base.BaseRepositoryTestCase;
import com.laive.core.vo.UserBalanceLogListVO;
import com.laive.core.vo.UserBalanceLogSearchListVO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * Created by sylee on 2017. 7. 5..
 */
public class UserBalanceLogRepositoryJpaTest extends BaseRepositoryTestCase {

  @Autowired
  private UserBalanceLogRepository repository;

  @Test
  public void getListVO() throws Exception {
    UserBalanceLogListVO listVO = new UserBalanceLogListVO();
    listVO.setUserId("2");
    repository.getListVO(listVO);
  }

  @Test
  public void getSearchListVO() {
    UserBalanceLogSearchListVO listVO = new UserBalanceLogSearchListVO();
    listVO.setSearchDate(new Date());
    listVO.setSearchText("<EMAIL>");
    repository.getBalanceLogSearchListVO(listVO);
  }

}