package com.laive.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;

@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
@Import({CoreConfig.class, MailConfig.class})
public class TestConfig {

  @Bean
  public MockHttpServletRequest httpServletRequest() {
    return new MockHttpServletRequest();
  }

}
