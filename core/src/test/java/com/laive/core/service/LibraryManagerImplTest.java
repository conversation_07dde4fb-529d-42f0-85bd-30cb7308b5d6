package com.laive.core.service;

import com.laive.core.base.BaseManagerTestCase;
import com.laive.core.domain.Library;
import com.laive.core.test.TestData;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class LibraryManagerImplTest extends BaseManagerTestCase {

  @Autowired
  LibraryManager libraryManager;

  @Test
  public void testMerge() {
    Library library = libraryManager.get(TestData.LIBRARY_ID);
    library.setName("changed name");
    libraryManager.merge(library);
    flushAndClear();
  }

  @Test
  public void testGetNotActivatedLibraryByUploadedFileName() {
    log.debug(libraryManager.getNotActivatedLibraryByUploadedFileName("abc"));
  }

  @Test
  public void testDelete() throws Exception {
    libraryManager.delete(TestData.LIBRARY_ID);
    flushAndClear();
  }

  /**
   * test for (deleted object would be re-saved by cascade)
   */
  @Test
  @Ignore
  public void testSaveLibraryAndRecreateKeyword() {
    Library library = libraryManager.get(TestData.LIBRARY_ID);
    log.debug(library.getLibraryCrews());
            library.setLibraryCrewNameString("Steven Spielberg"); // OK
//    library.setLibraryCrewNameString("Kim Il Ho|Steven Spielberg"); // error
    //        library.setLibraryCrewIds(new String[]{"@Kim Il Ho"}); // error
    libraryManager.saveLibraryAndRecreateKeyword(library, "Asia/Seoul");
    entityManager.flush();
  }

}
