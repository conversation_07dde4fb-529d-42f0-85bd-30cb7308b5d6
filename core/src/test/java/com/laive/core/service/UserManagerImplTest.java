package com.laive.core.service;

import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.laive.core.base.BaseManagerTestCase;
import com.laive.core.domain.User;
import com.laive.core.test.TestData;
import com.laive.core.vo.FavoriteVodListVO;

public class UserManagerImplTest extends BaseManagerTestCase {

  @Autowired
  UserManager manager;

  @Test
  public void testCreateFavoriteProduct() throws Exception {
    String username = TestData.USERID;
    String productId = TestData.PRODUCT_VOD_ID;
    manager.createFavoriteProduct(username, productId);
    flushAndClear();
  }

  @Test
  public void testDeleteFavoriteVod() throws Exception {
    testCreateFavoriteProduct();
    String username = TestData.USERID;
    String vodId = TestData.PRODUCT_VOD_ID;
    manager.deleteFavoriteVod(username, vodId);
    flushAndClear();
  }

  @Test
  public void testCreateInterestCategory() throws Exception {
    String username = TestData.USERNAME;
    String categoryId = TestData.CATEGORY_ID;
    manager.createInterestCategory(username, categoryId);
    manager.createInterestCategory(username, "5");
    flushAndClear();
  }

  @Test
  public void testDeleteInterestCategory() throws Exception {

    testCreateInterestCategory();

    String username = TestData.USERNAME;
    String categoryId = TestData.CATEGORY_ID;
    manager.deleteInterestCategory(username, categoryId);
    flushAndClear();
  }

  @Test
  public void testGetFavoriteVodListVO() throws Exception {
    FavoriteVodListVO listVO = new FavoriteVodListVO();
    listVO.setUserId(TestData.USERID);
    listVO = manager.getFavoriteVodListVO(listVO);
    log.debug(listVO);
  }

  @Test
  public void testGet() {
    User user = manager.getUserByEmail("<EMAIL>");
    log.debug(user == null);

  }

  @Ignore
  @Test
  public void testRegisterSubId() {
    manager.registerSubId(TestData.USERID, "33002171", "01022223333");
    flushAndClear();
  }

}
