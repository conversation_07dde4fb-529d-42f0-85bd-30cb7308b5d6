package com.laive.core.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Transient;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import org.hibernate.annotations.GenericGenerator;

import com.laive.core.base.BaseTraceableEntity;
import com.laive.core.constants.ColumnSizeConstants;
import com.laive.core.util.DateUtil;
import com.mysema.query.annotations.QueryProjection;

@Entity
@Table(name = "mt_schedule_template")
@Data
@EqualsAndHashCode(callSuper = false, of = { "id" })
@ToString(exclude = { "scheduleTemplateLibraries" })
@NoArgsConstructor
public class ScheduleTemplate extends BaseTraceableEntity<String> {

    private static final long serialVersionUID = -8813816035544683108L;

    @Id
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    @GeneratedValue(generator = "system-uuid")
    @Column(length = ColumnSizeConstants.UUID)
    private String id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "platform_id")
    private Platform platform;

    @OneToMany(mappedBy = "scheduleTemplate", fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("id.sortSeq")
    private Set<ScheduleTemplateLibrary> scheduleTemplateLibraries = new HashSet<ScheduleTemplateLibrary>();

    @Column(length = 50, unique = true)
    private String name;

    @Transient
    private String totalTimeHours;

    @Transient
    private String totalTimeMinutes;

    @Transient
    private String totalTimeSeconds;

    @Transient
    private Integer totalDuration;

    @Transient
    private String totalDurationString;

    @Transient
    private List<ScheduleTemplateLibrary> scheduleTemplateLibrariesTemp = new ArrayList<ScheduleTemplateLibrary>();

    @QueryProjection
    public ScheduleTemplate(String id, String name, Date updatedDt) {
        this.id = id;
        this.name = name;
        this.updatedDt = updatedDt;
    }

    @QueryProjection
    public ScheduleTemplate(String id, String name, Date updatedDt, Integer totalDuration) {
      this.id = id;
      this.name = name;
      this.updatedDt = updatedDt;
      this.totalDuration = totalDuration;
      this.totalDurationString = DateUtil.convertSecondsToHHmmss(totalDuration == null ? 0 : totalDuration);
    }

}
