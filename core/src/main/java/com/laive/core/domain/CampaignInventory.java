package com.laive.core.domain;

import com.laive.core.base.BaseEntityId;
import com.laive.core.base.BaseTimestampEntity;
import com.laive.core.constants.ColumnSizeConstants;
import com.mysema.query.annotations.QueryDelegate;
import com.mysema.query.types.expr.BooleanExpression;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@Entity
@Table(name = "mt_campaign_inventory")
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false, of = {"id"})
@ToString(exclude = {"campaign", "inventory"})
public class CampaignInventory extends BaseTimestampEntity<CampaignInventory.Id> {

  private static final long serialVersionUID = -4163311843963031199L;

  @EmbeddedId
  private Id id = new Id();

  @ManyToOne(optional = false)
  @JoinColumn(name = "campaign_id", insertable = false, updatable = false)
  private Campaign campaign;

  @ManyToOne(optional = false)
  @JoinColumn(name = "inventory_id", insertable = false, updatable = false)
  private Inventory inventory;

  @Column(name = "exposure_count")
  private Long exposureCount;

  @Column(name = "exposure_rate")
  private Double exposureRate;

  public CampaignInventory(Campaign campaign, Inventory inventory) {
    this.campaign = campaign;
    this.inventory = inventory;
    this.id.campaignId = campaign.getId();
    this.id.inventoryId = inventory.getId();
  }

  @Transient
  public double getCalculatedExposureRate(Long count) {
    if (this.exposureCount == null) {
      return 0.00;
    }

    double rate = (double) count / (double) this.exposureCount * 100.0;
    return rate;
  }

  @Transient
  public void calcurateRate(Long count) {
    this.exposureRate = getCalculatedExposureRate(count);
  }


  @QueryDelegate(CampaignInventory.class)
  public static BooleanExpression exposable(QCampaignInventory campaignInventory) {
    return campaignInventory.exposureRate.isNull().or(campaignInventory.exposureRate.lt(100.00));
  }

  @Embeddable
  @Data
  @EqualsAndHashCode(callSuper = false, of = {"campaignId", "inventoryId"})
  @NoArgsConstructor
  @RequiredArgsConstructor
  public static class Id extends BaseEntityId {

    private static final long serialVersionUID = 942865131619725930L;

    @Column(name = "campaign_id", length = ColumnSizeConstants.UUID, nullable = false)
    @NonNull
    private String campaignId;

    @Column(name = "inventory_id", length = ColumnSizeConstants.UUID, nullable = false)
    @NonNull
    private String inventoryId;

  }
}
