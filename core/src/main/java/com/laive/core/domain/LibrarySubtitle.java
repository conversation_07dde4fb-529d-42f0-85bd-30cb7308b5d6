package com.laive.core.domain;

import com.laive.core.base.BaseEntity;
import com.laive.core.base.BaseEntityId;
import com.laive.core.constants.ColumnSizeConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@Entity
@Table(name = "mt_library_subtitle")
@Data
@EqualsAndHashCode(callSuper = false, of = { "id" })
@NoArgsConstructor
public class LibrarySubtitle extends BaseEntity<LibrarySubtitle.Id> {

  private static final long serialVersionUID = 4827956715905594481L;

  @EmbeddedId
  private Id id = new Id();

  @ManyToOne
  @JoinColumn(name = "library_id", insertable = false, updatable = false, nullable = false)
  private Library library;

  @Column(length = 5)
  private String extension;

  @Transient
  private MultipartFile subtitleFile;

  public LibrarySubtitle(Library library, String language) {
    this.library = library;
    this.id.libraryId = library.getId();
    this.id.language = language;
  }

  @Embeddable
  @Data
  @EqualsAndHashCode(callSuper = false, of = { "libraryId", "language" })
  @NoArgsConstructor
  @RequiredArgsConstructor
  public static class Id extends BaseEntityId {

    private static final long serialVersionUID = 3134805971460014056L;

    @Column(name = "library_id", length = ColumnSizeConstants.UUID)
    @NonNull
    private String libraryId;

    @Column(name = "language", length = ColumnSizeConstants.LANGUAGE)
    @NonNull
    private String language;

  }
}
