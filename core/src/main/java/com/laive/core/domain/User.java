package com.laive.core.domain;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laive.commons.core.util.delete.DateUtils;
import com.laive.core.base.BaseUser;
import com.laive.core.constants.ColumnSizeConstants;
import com.laive.core.constants.CommonCodes;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.exception.BusinessException;
import com.laive.core.util.BooleanUtils;
import com.laive.core.util.CollectionUtils;
import com.laive.core.util.DateUtil;
import com.laive.core.util.ImageUrlUtil;
import com.laive.core.util.StringUtils;
import com.mysema.query.annotations.QueryDelegate;
import com.mysema.query.types.expr.BooleanExpression;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.envers.AuditJoinTable;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;
import org.springframework.security.core.GrantedAuthority;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Entity
@Table(name = "mt_user")
@Getter
@Setter
@NoArgsConstructor
@ToString(exclude = {"userDevices", "mySchedules", "feedbacks", "userVouchers", "favoriteVodes", "boxes", "deviceIP"})
@Audited
@AuditOverride(forClass = BaseUser.class)
public class User extends BaseUser {

  private static final long serialVersionUID = -927746231276865509L;

  // added by rakie
  @Transient
  private String currentDeviceType;

  @Transient
  private String currentPlatformId;

  @Transient
  private String currentSessionId;  // 같은 user라 할지라도 각각 접속한 Device에 따라 다르다.

  @ManyToOne
  @JoinColumn(name = "agent_id")
  @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
  private Agent agent;

  @Column(length = ColumnSizeConstants.ADDRESS)
  private String address;

  @Column(length = ColumnSizeConstants.EMAIL)
  private String email;

  @Column(length = ColumnSizeConstants.DATE8)
  private String birth;

  @Column(length = ColumnSizeConstants.CODE1)
  private String sex;

  @Column(length = ColumnSizeConstants.PHONE)
  private String mobile;

  @Column(length = ColumnSizeConstants.CODE1)
  @JsonIgnore
  private String authInstitute;

  @Column(name = "name", length = 100)
  private String name;

  @Column(name = "nick_name", length = 100)
  private String nickName;

  @Column(name = "image", length = ColumnSizeConstants.FILE_NAME)
  private String image;

  @ManyToMany(fetch = FetchType.EAGER)
  @JoinTable(name = "mt_user_role", joinColumns = {@JoinColumn(name = "user_id")}, inverseJoinColumns = @JoinColumn(name = "role_id"))
  @JsonIgnore
  @AuditJoinTable
  @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
  private Set<Role> roles = new HashSet<>();

  @Column(name = "reset_password_enabled")
  @JsonIgnore
  private boolean resetPasswordEnabled;

  @Column(name = "reset_password_expire_dt")
  @JsonIgnore
  private Date resetPasswordExpireDt;

  @Column(name = "email_verify_request_dt")
  @JsonIgnore
  private Date emailVerifyRequestDt;

  @Column(name = "email_verify_dt")
  @JsonIgnore
  private Date emailVerifyDt;

  @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
  @JsonIgnore
  @NotAudited
  private Set<FavoriteVod> favoriteVodes = new HashSet<>();

  @ManyToMany(fetch = FetchType.EAGER)
  @JoinTable(name = "mt_interest_category", joinColumns = {@JoinColumn(name = "user_id")}, inverseJoinColumns = @JoinColumn(name = "category_id"))
  @JsonIgnore
  @NotAudited
  private Set<Category> interestCategories = new HashSet<>();

  @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
  @OrderBy("id.slot")
  @JsonIgnore
  @NotAudited
  private Set<UserDevice> userDevices = new HashSet<>();

  @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
  @JsonIgnore
  @NotAudited
  private Set<UserVoucher> userVouchers = new HashSet<>();

  @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
  @JoinTable(name = "mt_user_platform", joinColumns = @JoinColumn(name = "user_id"), inverseJoinColumns = @JoinColumn(name = "platform_id"))
  @JsonIgnore
  @NotAudited
  private Set<Platform> platforms = new HashSet<>();

  @Column(length = ColumnSizeConstants.TIMEZONE)
  @JsonIgnore
  private String timezone;

  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "mt_user_schedule", joinColumns = {@JoinColumn(name = "user_id")}, inverseJoinColumns = @JoinColumn(name = "schedule_id"))
  @JsonIgnore
  @NotAudited
  private Set<Schedule> mySchedules = new HashSet<>();

  @Column(name = "auth_token", length = 200)
  private String authToken;

  @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
  @JsonIgnore
  @NotAudited
  private Set<Feedback> feedbacks = new HashSet<>();

  @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
  @NotAudited
  private UserBalance userBalance;

  @Column(name = "is_free_user", nullable = false)
  @JsonIgnore
  private boolean freeUser;

  @JsonProperty
  @Column(name = "sub_id", length = ColumnSizeConstants.SUB_ID, unique = true)
  private String subId; // amdocs sub id

  @JsonProperty
  @Column(name = "tvs_sub_id", length = ColumnSizeConstants.SUB_ID, unique = true)
  private String tvsSubId; // tvs sub id

  @OneToMany(mappedBy = "user", cascade = {CascadeType.MERGE, CascadeType.PERSIST})
  @JsonIgnore
  @NotAudited
  private Set<Box> boxes = new HashSet<>();

  @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
  @NotAudited
  @JsonIgnore
  private Set<Card> cards = new HashSet<>();

  // for doku billing
  @Column(length = 20)
  private String city;

  @Column(length = 50)
  private String state;

  @Column(name = "zip_code", length = 20)
  private String zipCode;

  @Column(length = 20)
  private String country;

  @Column(length = ColumnSizeConstants.PHONE)
  private String tel;

  @ColumnDefault("0")
  @Column(name = "is_unlimited_device_register")
  private boolean unlimitedDeviceRegister;

  @JsonProperty("boxIds")
  @Transient
  private List<String> boxIds;

  @Transient
  @JsonIgnore
  private Integer csAssignedCount = 0;

  @Transient
  @JsonIgnore
  private Integer csSolvedCount = 0;

  @OneToOne(mappedBy = "user")
  @JsonBackReference
  @NotAudited
//  @Audited
  private DeviceIP deviceIP;

  @JsonProperty
  public List<Device> getDeviceList() {
    if (CollectionUtils.isEmpty(this.getUserDevices()))
      return Collections.emptyList();

    return this.getUserDevices().stream().map(UserDevice::getDevice).collect(Collectors.toList());
  }

  @Transient
  public Set<GrantedAuthority> getAuthorities() {
    Set<GrantedAuthority> authorities = new LinkedHashSet<>();
    authorities.addAll(roles);
    return authorities;
  }

  @Transient
  public String getRoleNames() {
    List<String> roleNames = new ArrayList<>();
    if (this.roles != null && roles.size() > 0) {
      for (Role role : roles) {
        roleNames.add(role.getName());
      }
    }
    return StringUtils.join(roleNames.toArray(), ",");
  }

  @Transient
  public boolean hasRole(String... roleNames) {
    if (roleNames == null || roleNames.length == 0)
      return false;

    for (Role role : roles) {
      for (String roleName : roleNames) {
        if (role.getName().equals(roleName))
          return true;
      }
    }
    return false;
  }

  public void addFavoriteVod(Vod vod) {
    FavoriteVod favoriteVod = new FavoriteVod(this, vod);
    this.getFavoriteVodes().add(favoriteVod);
  }

  public void removeFavoriteVod(Vod vod) {
    FavoriteVod favoriteVod = new FavoriteVod(this, vod);
    this.getFavoriteVodes().remove(favoriteVod);
  }

  public boolean hasDevice(Device requestDevice) {
    if (CollectionUtils.isEmpty(this.userDevices))
      return false;

    for (UserDevice userDevice : this.userDevices) {
      Device device = userDevice.getDevice();
      if (requestDevice.equals(device))
        return true;
    }
    return false;
  }

  public Set<Device> getDeviceSets(int afterDay) {
    if (CollectionUtils.isEmpty(this.userDevices))
      return Collections.emptySet();

    Set<Device> resultSet = new HashSet<Device>();
    for (UserDevice userDevice : this.userDevices) {
      Device device = userDevice.getDevice();
      if (device != null) {
        Date registerDt = userDevice.getRegisterDt();
        if (registerDt == null) {
          device.setRegisterPossible(1);
        } else {
          Date registerPossibleDt = DateUtil.addDays(registerDt, afterDay);
          device.setRegisterPossibleDate(DateUtil.getDateString("yyyyMMdd", registerPossibleDt, this.timezone));
          boolean registerPossible = DateUtils.getDateDiffDay(new Date(), registerDt) >= afterDay;
          device.setRegisterPossible(BooleanUtils.toInteger10(registerPossible));
          device.setRegisterDate(DateUtil.getDateString("yyyyMMdd", registerDt, this.timezone));
        }
        device.setSlot(userDevice.getSlot());
        resultSet.add(device);
      }
    }
    return resultSet;
  }

  public Integer getAvailableSlot(int maxCount) {

    if (CollectionUtils.isEmpty(this.userDevices))
      return 1;

    Set<Integer> usedSlots = new HashSet<>();
    for (UserDevice userDevice : this.userDevices) {
      if (userDevice.getDevice() != null)
        usedSlots.add(userDevice.getSlot());
    }

    for (int i = 1; i <= maxCount; i++) {
      if (!usedSlots.contains(i))
        return i;
    }
    return null;
  }

  @JsonProperty
  public String getImageUrl() {
    if (StringUtils.isBlank(this.image))
      return "";

    if (CommonCodes.AuthInstituteType.TVSTORM.equals(this.authInstitute) && !StringUtils.contains(this.image, "facebook")) {
      return String.format("%s/%s/%s", ImageUrlUtil.getBaseImageUrl(this.getClass()), this.id, this.image);
    } else {
      return this.image;
    }
  }

  @Transient
  public Set<Voucher> getMyUnusedVouchers() {
    if (CollectionUtils.isEmpty(this.userVouchers))
      return Collections.emptySet();

    Set<Voucher> myVouchers = new HashSet<Voucher>();
    for (UserVoucher userVoucher : this.userVouchers) {
      if (userVoucher.isUsable()) {
        myVouchers.add(userVoucher.getVoucher());
      }
    }
    return myVouchers;
  }

  public Set<UserDevice> getActiveUserDevices() {
    Set<UserDevice> result = new HashSet<>();
    if (CollectionUtils.isEmpty(this.userDevices))
      return result;
    for (UserDevice userDevice : this.userDevices) {
      if (userDevice.getDevice() != null && userDevice.getDevice().isActive()) {
        result.add(userDevice);
      }
    }
    return result;
  }

  @Transient
  public String getPlatformNames() {
    List<String> platformNames = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(this.platforms)) {
      for (Platform platform : this.platforms) {
        platformNames.add(platform.getName());
      }
    }
    return StringUtils.join(platformNames, ",");
  }

  @Transient
  public List<String> getPlatformIds() {
    if (CollectionUtils.isEmpty(this.platforms))
      return Collections.emptyList();

    return this.platforms.stream().map(Platform::getId).collect(Collectors.toList());
  }

  @Transient
  public void checkEmailVerfied() {
    if (!isEnabled() && emailVerifyDt == null) {
      long dateDiffHours = DateUtil.getDateDiffHours(new Date(), emailVerifyRequestDt);
      if (dateDiffHours > 24) {
        throw new BusinessException(ReturnCodes.ERROR_USER_EMAIL_VERIFIED_EXPIRED, String.format(
                "user(%s) not email verified expired.., dateDiffHours:%d", this.username, dateDiffHours));
      } else {
        throw new BusinessException(ReturnCodes.ERROR_USER_EMAIL_VERIFIED_YET, String.format("user(%s) not email verified yet..", this.username));
      }
    }
  }

  @QueryDelegate(User.class)
  public static BooleanExpression searchPredicates(QUser user, String searchWord) {
    return user.email.contains(searchWord).or(
            user.name.contains(searchWord).or(user.username.contains(searchWord)).or(user.mobile.contains(searchWord).or(user.id.contains(searchWord))));
  }

  @JsonProperty("enabled")
  @Transient
  public boolean isActivated() {
    return this.isEnabled();
  }

  @Transient
  public String getOwnedBoxSerials() {
    List<String> boxSerials = this.boxes.stream().map(Box::getSerial).collect(Collectors.toList());
    return StringUtils.join(boxSerials, ",");
  }

  @JsonProperty("ownedBoxLabels")
  @Transient
  public List<String> getOwnedBoxLabels() {
    return this.boxes.stream().map(box -> box.getId().concat("(serial:").concat(box.getSerial()).concat(")")).collect(Collectors.toList());
  }

  @Transient
  public void prePersist() {
    if (this.agent != null && StringUtils.isBlank(this.agent.getId()))
      setAgent(null);
  }

  @Transient
  public boolean isDokuBillingPossible() {
    return StringUtils.isNotBlank(name) && StringUtils.isNotBlank(address) && StringUtils.isNotBlank(email) && StringUtils.isNotBlank(mobile);
  }

  @Transient
  public boolean isDokuBillingTokenizationPossible() {
    return isDokuBillingPossible()
            && StringUtils.isNotBlank(city)
            && StringUtils.isNotBlank(state)
            && StringUtils.isNotBlank(country)
            && StringUtils.isNotBlank(zipCode);
  }

  @Transient
  public boolean isMyPlatform(String platformId) {
    return this.platforms.stream().anyMatch(p -> StringUtils.equals(platformId, p.getId()));
  }



}
