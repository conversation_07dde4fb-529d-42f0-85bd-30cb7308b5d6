package com.laive.core.domain;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Transient;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import org.springframework.security.core.GrantedAuthority;

import com.laive.core.base.BaseTraceableEntity;
import com.laive.core.util.StringUtils;

@Entity
@Table(name = "mt_role")
@Data
@EqualsAndHashCode(callSuper = false, of = { "id" })
@NoArgsConstructor
public class Role extends BaseTraceableEntity<Long> implements GrantedAuthority {

  private static final long serialVersionUID = 3690197650654049848L;

  @Id
  @GeneratedValue
  @Column(name = "role_id")
  private Long id;

  @Enumerated(EnumType.STRING)
  @Column(name = "service_site", length = 5)
  private ServiceSite serviceSite;

  @Column(length = 20)
  private String name;

  @Lob
  private String description;

  @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
  @OrderBy("sortSeq")
  private List<RoleMenu> roleMenus = new ArrayList<>();

  @Transient
  private List<RoleMenu> roleMenusTemp = new ArrayList<>();

  public Role(Long id) {
    this.id = id;
  }

  @Transient
  public String getAuthority() {
    return String.format("ROLE_%s", getName());
  }

  public int compareTo(Object o) {
    return (equals(o) ? 0 : -1);
  }

  @Transient
  public boolean hasPermission(String menuName, Permission permission) {
    Optional<RoleMenu> roleMenuOptional = this.roleMenus.stream().filter(rm -> StringUtils.equalsIgnoreCase(rm.getMenu().getName(), menuName))
        .findFirst();
    if (roleMenuOptional.isPresent()) {
      RoleMenu roleMenu = roleMenuOptional.get();
      return Permission.WRITE.equals(roleMenu.getPermission()) || roleMenu.getPermission().equals(permission);
    } else {
      return false;
    }
  }

  @Transient
  public long getReadMenuCount() {
    return this.roleMenus.stream().filter(rm -> Permission.READ.equals(rm.getPermission())).count();
  }

  @Transient
  public long getWriteMenuCount() {
    return this.roleMenus.stream().filter(rm -> Permission.WRITE.equals(rm.getPermission())).count();
  }

  @Transient
  public List<RoleMenu> getRoleMenusOrderByMenuSortSeq() {
    return this.roleMenus.stream().sorted((o1, o2) -> o1.getMenu().getSortSeq().compareTo(o2.getMenu().getSortSeq())).collect(Collectors.toList());
  }
}