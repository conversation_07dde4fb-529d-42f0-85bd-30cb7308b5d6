package com.laive.core.exception;

import com.laive.core.base.BaseException;
import com.laive.core.base.BaseReturnCodes;

public class ParameterException extends BaseException {

    private static final long serialVersionUID = -6976510954438859842L;

    public ParameterException() {
        super();
    }

    public ParameterException(String message) {
        super(message);
    }

    @Override
    public String getExceptionCode() {
        return BaseReturnCodes.ERROR_PARAMETER_EMPTY;
    }

}
