package com.laive.core.repository;

import java.util.List;

import com.laive.core.base.GenericRepository;
import com.laive.core.domain.Device;
import com.laive.core.push.PushTargetVO;


public interface DeviceRepository extends GenericRepository<Device, String> {

  Device get(String userId, Integer slot);

  List<Device> getListByUserId(String userId);

  Device getSingle(String deviceId);

  List<Device> getAllByPlatformId(String platformId);

  List<PushTargetVO> getAllActiveDeviceTokensByPlatformIdAndDeviceType(String platformId, String type);

  void processToInActive(List<String> invalidDeviceTokens);

  List<PushTargetVO> getAllActiveDeviceTokensByUserIdsAndDeviceType(List<String> notificationUserIds, String deviceType);

  List<Device> getDevicesByTokens(List<String> deviceTokens);
}
