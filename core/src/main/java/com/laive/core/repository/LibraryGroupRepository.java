package com.laive.core.repository;

import com.laive.core.base.GenericRepository;
import com.laive.core.domain.LibraryGroup;

import java.util.List;

public interface LibraryGroupRepository extends GenericRepository<LibraryGroup, String> {

  List<LibraryGroup> getLibraryGroupList();

  boolean exists(String name, String id);

  List<LibraryGroup> getLibraryGroupListByLibraryType(String libraryType);
}
