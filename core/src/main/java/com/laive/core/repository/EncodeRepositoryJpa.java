package com.laive.core.repository;

import org.springframework.stereotype.Repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.domain.Encode;
import com.laive.core.domain.QEncode;
import com.laive.core.vo.EncodeListVO;
import com.mysema.query.jpa.impl.JPAQuery;

import java.util.List;

@Repository("encodeRepository")
public class EncodeRepositoryJpa extends GenericRepositoryJpa<Encode, String> implements EncodeRepository {

  public EncodeRepositoryJpa() {
    super(Encode.class);
  }

  @Override
  public EncodeListVO getListVO(EncodeListVO listVO) {
    JPAQuery query = new JPAQuery(entityManager);
    QEncode encode = QEncode.encode;
    query.from(encode).orderBy(encode.createdDt.desc());

    listVO.setFullListSize(query.count());

    if(listVO.isPaginated()) {
      listVO.pagination(query);
    }

    listVO.setList(query.list(encode));
    return listVO;
  }

  @Override
  public List<Encode> getAllEnabled() {
    JPAQuery query = new JPAQuery(entityManager);
    QEncode encode = QEncode.encode;
    query.from(encode).orderBy(encode.bitrate.desc());
    query.where(encode.valid.isTrue());
    return query.list(encode);
  }
}
