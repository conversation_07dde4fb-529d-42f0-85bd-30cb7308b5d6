package com.laive.core.repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.domain.ChannelScheduleMapper;
import com.laive.core.domain.QChannel;
import com.laive.core.domain.QChannelScheduleMapper;
import com.mysema.query.jpa.impl.JPAQuery;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("channelScheduleMapperRepository")
public class ChannelScheduleMapperRepositoryJpa extends GenericRepositoryJpa<ChannelScheduleMapper, ChannelScheduleMapper.Id>
    implements ChannelScheduleMapperRepository {

  public ChannelScheduleMapperRepositoryJpa() {
    super(ChannelScheduleMapper.class);
  }

  @Override
  public List<ChannelScheduleMapper> getListByOriginalChannelId(String channelId) {
    JPAQuery query = new JPAQuery(getEntityManager());
    QChannelScheduleMapper root = QChannelScheduleMapper.channelScheduleMapper;
    QChannel channel =QChannel.channel;
    query.from(root).join(root.channel, channel).where(root.id.originalChannelId.eq(channelId).and(channel.enabled.isTrue()));
    return query.list(root);
  }

  @Override
  public String getOriginalChannelIdFromChannelId(String channelId) {
    JPAQuery query = new JPAQuery(getEntityManager());
    QChannelScheduleMapper root = QChannelScheduleMapper.channelScheduleMapper;
    query.from(root).where(root.id.channelId.eq(channelId));
    return query.singleResult(root.id.originalChannelId);
  }

  @Override
  public Map<String, String> getChannelScheduleMapperMap(List<String> servicePlatformChannelIds, String requestPlatformId) {
    JPAQuery query = new JPAQuery(getEntityManager());
    QChannelScheduleMapper root = QChannelScheduleMapper.channelScheduleMapper;
    QChannel channel = QChannel.channel;
    QChannel originalChannel = new QChannel("originalChannel");
    query.from(root).join(root.channel, channel).join(root.originalChannel, originalChannel);
    query.where(originalChannel.id.in(servicePlatformChannelIds));
    query.where(channel.platform.id.eq(requestPlatformId));
    return query.map(originalChannel.id, channel.id);
  }
}
