package com.laive.core.repository;

import java.util.Date;
import java.util.List;

import com.laive.core.util.ConfigUtil;
import com.laive.core.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.constants.CommonCodes;
import com.laive.core.domain.QLibrary;
import com.laive.core.domain.QPlatform;
import com.laive.core.domain.QVod;
import com.laive.core.domain.Vod;
import com.laive.core.domain.VodType;
import com.laive.core.domain.stats.QWatchVodStats;
import com.laive.core.domain.stats.WatchVodStats;
import com.mysema.query.jpa.impl.JPAQuery;
import com.mysema.query.types.Predicate;
import com.mysema.query.types.expr.BooleanExpression;

@Repository("watchVodStatsRepository")
public class WatchVodStatsRepositoryJpa extends GenericRepositoryJpa<WatchVodStats, WatchVodStats.Id> implements WatchVodStatsRepository {

  @Autowired
  private ConfigUtil configUtil;

  public WatchVodStatsRepositoryJpa() {
    super(WatchVodStats.class);
  }

  @Override
  public List<Vod> getTopNByPlatformOrderByWatchCount(String platformId, Date date, int count) {

    JPAQuery query = new JPAQuery(entityManager);
    QWatchVodStats watchVodStats = QWatchVodStats.watchVodStats;
    QVod vod = QVod.vod;
    QLibrary library = QLibrary.library;
    QPlatform platform = QPlatform.platform;

    query.from(watchVodStats).join(watchVodStats.vod, vod).join(vod.library, library).join(vod.platform, platform).where(platform.id.eq(platformId));

    Date startDt = DateUtil.addDays(date, -1 * configUtil.getHotBadgeThresholdBeforeDays());
    query.where(watchVodStats.id.referenceDt.between(startDt, date));
    query.where(getEnabledLibraryPredicates(library));
    query.where(getEnabledVodPredicates(vod));
    query.where(vod.type.eq(VodType.SINGLE));
    query.groupBy(vod.id);
    query.orderBy(watchVodStats.watchCount.sum().desc());
    query.limit(count);
    return query.list(vod);
  }

  private Predicate getEnabledLibraryPredicates(QLibrary library) {
    BooleanExpression otherPredicates = library.type.eq(CommonCodes.LibraryType.VOD).and(library.deleted.isFalse());
    return library.isNull().or(otherPredicates);
  }

  private Predicate getEnabledVodPredicates(QVod vod) {
    return vod.enabled.isTrue().and(vod.deleted.isFalse());
  }

}
