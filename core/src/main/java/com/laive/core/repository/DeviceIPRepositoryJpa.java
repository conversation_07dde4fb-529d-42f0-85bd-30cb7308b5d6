package com.laive.core.repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.domain.DeviceIP;
import com.laive.core.domain.QDeviceIP;
import com.laive.core.domain.User;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.DeviceIPExcelListVO;
import com.laive.core.vo.DeviceIPListVO;
import com.laive.core.vo.QDeviceIPExcelListVO_Data;
import com.mysema.query.jpa.impl.JPAQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("deviceIPRepository")
public class DeviceIPRepositoryJpa extends GenericRepositoryJpa<DeviceIP, String> implements DeviceIPRepository {

    public DeviceIPRepositoryJpa() {
        super(DeviceIP.class);
    }

    @Override
    public DeviceIPListVO getList(DeviceIPListVO listVO) {
//        listVO.getSearch().setFromDate(DateUtil.changeDateTimeElements(listVO.getSearch().getFromDate(), 0, 0, 0));
//        listVO.getSearch().setToDate(DateUtil.changeDateTimeElements(listVO.getSearch().getToDate(), 23, 59, 59));

        JPAQuery query = new JPAQuery(entityManager);
        QDeviceIP deviceIP = QDeviceIP.deviceIP;
//        query.from(deviceIP).where(deviceIP.createdDt.between(listVO.getSearch().getFromDate(), listVO.getSearch().getToDate())).orderBy(deviceIP.updatedDt.desc());
        query.from(deviceIP);

        if (StringUtils.isNotBlank(listVO.getSearch().getSearchWord())) {
            String searchWord = listVO.getSearch().getSearchWord().trim();
            query.where(deviceIP.user.email.contains(searchWord)
                    .or(deviceIP.user.name.contains(searchWord).or(deviceIP.ipAddress.contains(searchWord))
                            .or(deviceIP.user.mobile.contains(searchWord))));
        }
        query.orderBy(deviceIP.updatedDt.desc());

        long entryCount = query.count();
        listVO.setFullListSize(entryCount);
        int pageCount = (int) Math.ceil(((double) entryCount / listVO.getPageSize()));

        if(listVO.getPage() > pageCount)
            listVO.setPage(pageCount);

        if (listVO.getPageSize() > 0) {
            query.limit(listVO.getPageSize()).offset(listVO.getFirstRecordIndex());
        }

        listVO.setList(query.list(deviceIP));

        return listVO;
    }

    @Override
    public DeviceIPExcelListVO getExcelList(DeviceIPExcelListVO excelListVO) {
//        excelListVO.getSearch().setFromDate(DateUtil.changeDateTimeElements(excelListVO.getSearch().getFromDate(), 0, 0, 0));
//        excelListVO.getSearch().setToDate(DateUtil.changeDateTimeElements(excelListVO.getSearch().getToDate(), 23, 59, 59));

        JPAQuery query = new JPAQuery(entityManager);
        QDeviceIP deviceIP = QDeviceIP.deviceIP;
//        query.from(deviceIP).where(deviceIP.createdDt.between(excelListVO.getSearch().getFromDate(), excelListVO.getSearch().getToDate())).orderBy(deviceIP.updatedDt.desc());
        query.from(deviceIP);

        if (StringUtils.isNotBlank(excelListVO.getSearch().getSearchWord())) {
            String searchWord = excelListVO.getSearch().getSearchWord().trim();
            query.where(deviceIP.user.email.contains(searchWord)
                    .or(deviceIP.user.name.contains(searchWord).or(deviceIP.ipAddress.contains(searchWord))
                            .or(deviceIP.user.mobile.contains(searchWord))));
        }
        query.orderBy(deviceIP.updatedDt.desc());

        excelListVO.setFullListSize(query.count());
        excelListVO.pagination(query);

        excelListVO.setList(query.list(new QDeviceIPExcelListVO_Data(deviceIP.id, deviceIP.ipAddress, deviceIP.updatedDt, deviceIP.user.name, deviceIP.userCity, deviceIP.userState, deviceIP.userCountry, deviceIP.user.email, deviceIP.user.mobile,
                deviceIP.user.subId, deviceIP.user.tvsSubId, deviceIP.user.updatedDt, deviceIP.user, deviceIP.user.enabled)));
        return excelListVO;
    }

    @Override
    public DeviceIP getByUser(User user) {
        JPAQuery query = new JPAQuery(entityManager);
        QDeviceIP deviceIP = QDeviceIP.deviceIP;
        query.from(deviceIP).where(deviceIP.user.id.eq(user.getId()));
        return query.singleResult(deviceIP);
    }

    @Override
    public List<DeviceIP> getDeviceIpByUserAndDevice(String userId, String deviceId) {
        JPAQuery query = new JPAQuery(entityManager);
        QDeviceIP deviceIP = QDeviceIP.deviceIP;
        query.from(deviceIP).where(deviceIP.user.id.eq(userId)).where(deviceIP.device.id.eq(deviceId)).where(deviceIP.device.id.isNotNull());
        return query.list(deviceIP);
    }

    @Override
    public List<DeviceIP> getDeviceIpByDevice(String deviceId) {
        JPAQuery query = new JPAQuery(entityManager);
        QDeviceIP deviceIP = QDeviceIP.deviceIP;
        query.from(deviceIP).where(deviceIP.device.id.eq(deviceId));
        return query.list(deviceIP);
    }

}
