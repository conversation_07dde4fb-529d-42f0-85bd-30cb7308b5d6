package com.laive.core.repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;
import javax.persistence.Query;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.domain.stats.AccessCountryStat;
import com.laive.core.util.DateUtil;
import com.laive.core.util.NumberUtils;
import com.laive.core.util.ServicePropertiesUtil;
import com.laive.core.vo.AccessCountryStatVO;

@Repository("accessCountryStatRepository")
public class AccessCountryStatRepositoryJpa extends GenericRepositoryJpa<AccessCountryStat, AccessCountryStat.Id> implements
        AccessCountryStatRepository {

  @Autowired
  private ServicePropertiesUtil servicePropertiesUtil;

  @Resource(name = "sqlProperties")
  private Properties sqlProperties;

  public AccessCountryStatRepositoryJpa() {
    super(AccessCountryStat.class);
  }

  @Override
  public List<AccessCountryStatVO> getDailyStatVOList(Date date) {
    String sql = sqlProperties.getProperty("accessCountry.getDailyStats");
    int timezoneOffset = DateUtil.getTimezoneOffsetHour(servicePropertiesUtil.getServiceTimezone());
    sql = StringUtils.replace(sql, "{offset}", String.format("'%d'", timezoneOffset));
    String dateformat = "%Y%m%d";
    sql = StringUtils.replace(sql, "{dateformat}", String.format("'%s'", dateformat));

    String additionalClause = "";

    sql = StringUtils.replace(sql, "{additionalClause}", additionalClause);
    Query query = entityManager.createNativeQuery(sql);
    query.setParameter("fromDt", DateUtil.addDays(date, -1)).setParameter("toDt", date);

    List<Object[]> list = (List<Object[]>)query.getResultList();
    log.debug(list);
    List<AccessCountryStatVO> statsList = new ArrayList<>();
    for (Object[] arr : list) {
      String dt = (String)arr[0];
      String country = (String)arr[1];
      long count = NumberUtils.toLong(arr[2]);
      long uniqueCount = NumberUtils.toLong(arr[3]);
      if (StringUtils.isNotBlank(dt)) {
        AccessCountryStatVO data = new AccessCountryStatVO(dt, country, count, uniqueCount);
        statsList.add(data);
      }
    }
    return statsList;
  }
}
