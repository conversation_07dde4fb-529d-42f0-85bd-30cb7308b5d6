package com.laive.core.repository;

import com.laive.core.domain.QUser;
import com.laive.core.util.DateUtil;
import com.laive.core.vo.CsCountVO;
import com.laive.core.vo.QCsCountVO;
import org.springframework.stereotype.Repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.domain.Cs;
import com.laive.core.domain.QAdmin;
import com.laive.core.domain.QAgent;
import com.laive.core.domain.QCs;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.CsListVO;
import com.laive.core.vo.CsOperatorListVO;
import com.laive.core.vo.QCsOperatorListVO_OperatorVO;
import com.mysema.query.jpa.impl.JPAQuery;
import com.mysema.query.types.Order;
import com.mysema.query.types.expr.NumberExpression;

@Repository("csRepository")
public class CsRepositoryJpa extends GenericRepositoryJpa<Cs, String> implements CsRepository {

  public CsRepositoryJpa() {
    super(Cs.class);
  }

  @Override
  public CsListVO getListVO(CsListVO listVO) {

    JPAQuery query = new JPAQuery(entityManager);
    QCs cs = QCs.cs;
    query.from(cs).orderBy(cs.createdDt.desc());

    CsListVO.Search search = listVO.getSearch();
    if (StringUtils.isNotBlank(search.getAgentId())) {
      QAgent agent = QAgent.agent;
      query.join(cs.agent, agent);
      query.where(agent.id.eq(search.getAgentId()));
    }

    if (search.getSolved() != null) {
      query.where(cs.solved.eq(search.getSolved()));
    }

    if (StringUtils.isNotBlank(search.getSearchword())) {
      query.where(cs.user.searchPredicates(search.getSearchword()));
    }

    if (search.getCsType() != null) {
      query.where(cs.csType.eq(search.getCsType()));
    }

    if (StringUtils.isNotBlank(search.getUserId())) {
      query.where(cs.user.id.eq(search.getUserId()));
    }


    long entryCount = query.count();
    listVO.setFullListSize(entryCount);
    int pageCount = (int) Math.ceil(((double) entryCount / listVO.getPageSize()));

    if (listVO.getPage() > pageCount)
      listVO.setPage(pageCount);

    listVO.pagination(query);
    listVO.setList(query.list(cs));
    return listVO;
  }

  @Override
  public CsOperatorListVO getOperatorListVO(CsOperatorListVO listVO) {
    JPAQuery query = new JPAQuery(entityManager);
    QCs cs = QCs.cs;
    QAdmin operator = QAdmin.admin;
    query.from(cs).join(cs.operator, operator);

    CsOperatorListVO.Search search = listVO.getSearch();
    if (StringUtils.isNotBlank(search.getSearchword())) {
      query.where(operator.fullName.contains(search.getSearchword()).or(operator.email.contains(search.getSearchword()))
          .or(operator.username.contains(search.getSearchword())));
    }

    if (search.getSearchStartDt() != null && search.getSearchEndDt() != null) {
      query.where(cs.receivedDt.between(search.getSearchStartDt(), DateUtil.addDays(search.getSearchEndDt(), 1)));
    }

    NumberExpression<Long> csCount = cs.count();
    NumberExpression<Long> solvedCount = cs.solved.castToNum(Long.class).sum();
    NumberExpression<Long> solvedRatio = solvedCount.divide(csCount);

    String sortTargetColumn = listVO.getSortTargetColumn();
    Order order = listVO.getAscendingOrder();
    
    if (StringUtils.isNotBlank(sortTargetColumn)) {
      if (sortTargetColumn.equals("ASSIGNED")) {
        query.orderBy(Order.ASC.equals(order) ? csCount.asc() : csCount.desc());
      } else if (sortTargetColumn.equals("SOLVED")) {
        query.orderBy(Order.ASC.equals(order) ? solvedCount.asc() : solvedCount.desc());
      } else if (sortTargetColumn.equals("SOLVED_RATIO")) {
        query.orderBy(Order.ASC.equals(order) ? solvedRatio.asc() : solvedRatio.desc());
      }
    }
    Long count = query.singleResult(operator.id.countDistinct());
    if (count == null)
      count = 0L;
    listVO.setFullListSize(count);

    query.groupBy(operator.id, operator.fullName, operator.username);
    listVO.pagination(query);
    listVO.setList(query.list(new QCsOperatorListVO_OperatorVO(operator.id, operator.fullName, operator.username, csCount, solvedCount)));
    return listVO;
  }

  @Override
  public boolean existByUser(String userId) {
    JPAQuery query = new JPAQuery(entityManager);
    QCs cs = QCs.cs;
    QUser user = QUser.user;
    query.from(cs).join(cs.user, user).where(user.id.eq(userId));
    return query.exists();
  }

  @Override
  public CsCountVO getCsCountVOByUser(String userId) {
    JPAQuery query = new JPAQuery(entityManager);
    QCs cs = QCs.cs;
    QUser user = QUser.user;
    query.from(cs).join(cs.user, user).where(user.id.eq(userId));
    return query.singleResult(new QCsCountVO(user.id, cs.solved.castToNum(Long.class).sum(), cs.id.count()));
  }
}
