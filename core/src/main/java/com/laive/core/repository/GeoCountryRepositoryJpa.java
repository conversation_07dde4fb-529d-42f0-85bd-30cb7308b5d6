package com.laive.core.repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.domain.GeoCountry;
import com.laive.core.domain.QGeoCountry;
import com.mysema.query.jpa.impl.JPAQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("geoCountryRepository")
public class GeoCountryRepositoryJpa extends GenericRepositoryJpa<GeoCountry, String> implements GeoCountryRepository {

    public GeoCountryRepositoryJpa() {
        super(GeoCountry.class);
    }

    @Override
    public List<GeoCountry> getAllOrderByCountryName() {
        JPAQuery query = new JPAQuery(entityManager);
        QGeoCountry geoCountry = QGeoCountry.geoCountry;
        query.from(geoCountry).orderBy(geoCountry.name.asc());
        return query.list(geoCountry);
    }
}
