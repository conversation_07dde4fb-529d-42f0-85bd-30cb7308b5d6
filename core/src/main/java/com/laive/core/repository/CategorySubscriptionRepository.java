package com.laive.core.repository;

import com.laive.core.base.GenericRepository;
import com.laive.core.domain.BillingMethod;
import com.laive.core.domain.Category;
import com.laive.core.domain.CategorySubscription;
import com.laive.core.domain.Platform;
import com.laive.core.domain.calculation.UserCalculation;
import com.laive.core.domain.calculation.UserCalculationLog;
import com.laive.core.vo.AnalyticsCategorySubscriptionListVO;
import com.laive.core.vo.BillingListVO;
import com.laive.core.vo.CategorySubscriptionCountStatVO;
import com.laive.core.vo.CategorySubscriptionVO;
import com.laive.core.vo.SubscribeListVO;
import com.laive.core.vo.SubscriptionListVO;
import com.laive.core.vo.SubscriptionStatsVO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CategorySubscriptionRepository extends GenericRepository<CategorySubscription, String> {

  boolean existsActivePayment(String userId, String categoryId);

  SubscribeListVO getListVO(SubscribeListVO listVO);

  List<Category> getActiveSubscribedCategories(String userId);

  SubscriptionListVO getSubscriptionListVO(SubscriptionListVO listVO);

  Double getSubscriptionSum(SubscriptionListVO listVO);

  long getSubscriptionCount(Platform platform, Date fromDt, Date toDt);

  Map<String, CategorySubscription> getActiveCategorySubscriptionMap(String userId, Set<String> categoryIds);

  SubscriptionStatsVO getDailySubscriptionStatsVO(SubscriptionStatsVO statsVO);

  AnalyticsCategorySubscriptionListVO getCategorySubscriptionListVOForAnalytics(AnalyticsCategorySubscriptionListVO listVO);

  long getActiveSubscriptionCount(Platform platform, Date toDt);

  CategorySubscriptionCountStatVO getCountStatVO(String platformId);

  SubscriptionStatsVO getMonthlySubscriptionStatsVO(SubscriptionStatsVO statsVO);

  BillingListVO getBillingListVO(BillingListVO listVO);

  CategorySubscription getRecentActiveCategorySubscription(String categoryId, String userId);
  CategorySubscription getRecentActiveCategorySubscription(String categoryId, String userId, BillingMethod billingMethod);
  CategorySubscription getRecentActiveCategorySubscription(String userId);

  List<CategorySubscriptionVO>  getWillBeExpiredSubscription(String platformId, String userId, String timezone);

  void removeAllByUserIdAndBillingMethod(String userId, BillingMethod billingMethod);

  void removeAllByUserIdAndBillingMethods(String userId, List<BillingMethod> removeTargetBillingMethods);

  List<UserCalculationLog> getUserCalculationLogList(UserCalculation userCalculation);

  long getUserCount(String platformId, Date start, Date end);
}
