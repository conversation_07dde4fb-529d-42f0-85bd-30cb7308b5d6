package com.laive.core.repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.domain.MessageBoxType;
import com.laive.core.domain.MessageBoxUserMessage;
import com.laive.core.domain.QMessageBoxUserMessage;
import com.mysema.query.jpa.impl.JPAQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("messageBoxUserMessageRepository")
public class MessageBoxUserMessageRepositoryJpa extends GenericRepositoryJpa<MessageBoxUserMessage, String> implements MessageBoxUserMessageRepository {

    public MessageBoxUserMessageRepositoryJpa() {
        super(MessageBoxUserMessage.class);
    }

    @Override
    public MessageBoxUserMessage getByUserIdAndMessageId(String userId, String messageId) {
        JPAQuery query = new JPAQuery(entityManager);
        QMessageBoxUserMessage messageBoxUserMessage = QMessageBoxUserMessage.messageBoxUserMessage;
        query.from(messageBoxUserMessage);
        query.where(messageBoxUserMessage.user.id.eq(userId).and(messageBoxUserMessage.messageBoxMessage.id.eq(messageId)));
        return query.singleResult(messageBoxUserMessage);
    }

    @Override
    public List<MessageBoxUserMessage> getByUserIdAndIsRead(String userId, boolean isRead) {
        JPAQuery query = new JPAQuery(entityManager);
        QMessageBoxUserMessage messageBoxUserMessage = QMessageBoxUserMessage.messageBoxUserMessage;
        query.from(messageBoxUserMessage);
        query.where(messageBoxUserMessage.user.id.eq(userId).and(messageBoxUserMessage.isRead.eq(isRead)).and(messageBoxUserMessage.messageBoxMessage.messageBoxType.eq(MessageBoxType.MESSAGE_BOX.name())));
        query.orderBy(messageBoxUserMessage.messageBoxMessage.updatedDt.desc()).limit(20);
        return query.list(messageBoxUserMessage);
    }

    @Override
    public List<MessageBoxUserMessage> getByUserIdAndIsSent(String userId, Boolean isSent) {
        JPAQuery query = new JPAQuery(entityManager);
        QMessageBoxUserMessage messageBoxUserMessage = QMessageBoxUserMessage.messageBoxUserMessage;
        query.from(messageBoxUserMessage);
        query.where(messageBoxUserMessage.user.id.eq(userId).and(messageBoxUserMessage.messageBoxMessage.isSent.eq(isSent)));
        query.orderBy(messageBoxUserMessage.messageBoxMessage.updatedDt.desc()).limit(20);
        return query.list(messageBoxUserMessage);
    }

    @Override
    public List<MessageBoxUserMessage> getByUserIdAndMessageBoxType(String userId, MessageBoxType messageBoxType) {
        JPAQuery query = new JPAQuery(entityManager);
        QMessageBoxUserMessage messageBoxUserMessage = QMessageBoxUserMessage.messageBoxUserMessage;
        query.from(messageBoxUserMessage);
        query.where(messageBoxUserMessage.user.id.eq(userId).and(messageBoxUserMessage.messageBoxMessage.messageBoxType.eq(messageBoxType.name())));
        query.orderBy(messageBoxUserMessage.messageBoxMessage.updatedDt.desc());
        return query.list(messageBoxUserMessage);
    }

    public List<MessageBoxUserMessage> getByUserIdAndIsSentWithoutLimit(String userId, Boolean isSent) {
        JPAQuery query = new JPAQuery(entityManager);
        QMessageBoxUserMessage messageBoxUserMessage = QMessageBoxUserMessage.messageBoxUserMessage;
        query.from(messageBoxUserMessage);
        query.where(messageBoxUserMessage.user.id.eq(userId).and(messageBoxUserMessage.messageBoxMessage.isSent.eq(isSent)));
        query.orderBy(messageBoxUserMessage.messageBoxMessage.updatedDt.desc());
        return query.list(messageBoxUserMessage);
    }
}
