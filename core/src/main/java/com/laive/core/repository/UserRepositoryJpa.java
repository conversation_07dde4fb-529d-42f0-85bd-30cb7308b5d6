package com.laive.core.repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.config.CacheConfig;
import com.laive.core.domain.*;
import com.laive.core.domain.QAgent;
import com.laive.core.domain.QDevice;
import com.laive.core.domain.QPlatform;
import com.laive.core.domain.QUser;
import com.laive.core.domain.QUserBalance;
import com.laive.core.domain.QUserDevice;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.*;
import com.laive.core.vo.QDeviceVO;
import com.laive.core.vo.QUserExcelListVO_Data;
import com.laive.core.vo.QUserSearchListVO_Data;
import com.mysema.query.jpa.impl.JPAQuery;
import com.mysema.query.types.Order;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.mysema.query.group.GroupBy.groupBy;
import static com.mysema.query.group.GroupBy.list;

@Repository("userRepository")
public class UserRepositoryJpa extends GenericRepositoryJpa<User, String> implements UserRepository, UserDetailsService {

    public UserRepositoryJpa() {
        super(User.class);
    }

    @Autowired
    protected HttpServletRequest request;

    @Override
    public UserDetails loadUserByUsername(String username) {
        User user = getUserByUsername(username);
        return user;
    }

    @Override
    public User getUserByUsername(String username) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.username.eq(username));
        return query.singleResult(user);
    }

    @Override
    public boolean existUserByUsername(String username) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.username.eq(username));
        return query.count() > 0;
    }

    @Override
    public User getUserByEmail(String email) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.email.eq(email));
        return query.singleResult(user);
    }

    @Override
    public UserListVO getList(UserListVO listVO) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        QPlatform platform = QPlatform.platform;

        query.from(user).join(user.platforms, platform);

        UserListVO.Search search = listVO.getSearch();
        setSearchCondition(query, user, platform, search);

        if (listVO.getSortBySTB()) {
            query.where(user.boxes.size().gt(1));
        }

        long entryCount = query.count();
        listVO.setFullListSize(entryCount);
        int pageCount = (int) Math.ceil(((double) entryCount / listVO.getPageSize()));

        if (listVO.getPage() > pageCount)
            listVO.setPage(pageCount);

        listVO.setFullListSize(query.count());
        if (listVO.getPageSize() > 0) {
            query.limit(listVO.getPageSize()).offset(listVO.getFirstRecordIndex());
        }

        if (search.getSortColumn() != null) {
            if (listVO.needToSortByName()) {
                query.orderBy(Order.ASC.equals(listVO.getAscendingOrder()) ? user.name.asc() : user.name.desc());

            } else if (listVO.needToSortByUpdatedDt()) {
                query.orderBy(Order.ASC.equals(listVO.getAscendingOrder()) ? user.updatedDt.asc() : user.updatedDt.desc());
            }
        } else {
            query.orderBy(user.updatedDt.desc());
        }

        listVO.setList(query.list(user));
        return listVO;
    }

    @Override
    public UserExcelListVO getExcelList(UserExcelListVO listVO) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        QUserBalance userBalance = QUserBalance.userBalance;
        QPlatform platform = QPlatform.platform;
        QAgent agent = QAgent.agent;

        query.from(user).leftJoin(user.userBalance, userBalance).join(user.platforms, platform).leftJoin(user.agent, agent);
        query.orderBy(user.updatedDt.desc());

        UserListVO.Search search = listVO.getSearch();
        setSearchCondition(query, user, platform, search);

        listVO.setFullListSize(query.count());
        listVO.pagination(query);
        listVO.setList(query.list(new QUserExcelListVO_Data(user.id, user.name, user.username, user.mobile, user.subId, user.city, user.state, user.country,
                userBalance.balance, agent.name, user.enabled, user.tvsSubId, user)));
        return listVO;
    }

    @Override
    public boolean existUserBySubId(String subId) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.subId.eq(subId));
        return query.exists();
    }

    @Override
    public boolean existUserByTvsSubId(String tvsSubId) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.tvsSubId.eq(tvsSubId));
        return query.exists();
    }

    @Override
    public User getUserByName(String name) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.name.eq(name));
        return query.singleResult(user);
    }

    @Override
    public List<User> getUserListByUserIds(String[] userIds) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.id.in(userIds));
        return query.list(user);
    }

    private void setSearchCondition(JPAQuery query, QUser user, QPlatform platform, UserListVO.Search search) {
        if (StringUtils.isNotBlank(search.getPlatformId()))
            query.where(platform.id.eq(search.getPlatformId()));

        if (StringUtils.isNotBlank(search.getSearchword())) {
            String searchWord = search.getSearchword();
            query.where(user.email.contains(searchWord)
                    .or(user.name.contains(searchWord).or(user.username.contains(searchWord))
                            .or(user.mobile.contains(searchWord).or(user.id.contains(searchWord)))));
        }

        if (search.getEnabled() != null) {
            query.where(user.enabled.eq(search.getEnabled()));
        }

        if (StringUtils.isNotBlank(search.getAgentId())) {
            QAgent agent = QAgent.agent;
            query.join(user.agent, agent);
            query.where(agent.id.eq(search.getAgentId()));
        }
    }

    @Override
    public long getJoinCount(String platformId, Date searchDate) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.createdDt.between(searchDate, DateUtils.addDays(searchDate, 1)));

        if (StringUtils.isNotBlank(platformId)) {
            QPlatform platform = QPlatform.platform;
            query.join(user.platforms, platform);
            query.where(platform.id.eq(platformId));
        }
        return query.singleResult(user.id.countDistinct());
    }

    @Cacheable(value = CacheConfig.CACHE_FREE_USER)
    @Override
    public boolean isFreeUser(String userId) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.id.eq(userId).and(user.freeUser.isTrue()));
        return query.count() > 0;
    }

    @Override
    public boolean existUserByMobile(String mobile) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.mobile.eq(mobile));
        return query.exists();
    }

    @Override
    public List<User> getListByMobile(String mobile) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.mobile.eq(mobile));
        return query.list(user);
    }

    @Override
    public User getUserBySubId(String subId) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.subId.eq(subId));
        return query.singleResult(user);
    }

    @Override
    public User getUserByMobile(String mobile) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.mobile.eq(mobile));
        return query.singleResult(user);
    }

    @Override
    public UserSearchListVO getSearchListVO(UserSearchListVO listVO) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        QAgent agent = QAgent.agent;
        QPlatform platform = QPlatform.platform;
        QUserBalance userBalance = QUserBalance.userBalance;

        query.from(user).join(user.platforms, platform).leftJoin(user.userBalance, userBalance).leftJoin(user.agent, agent);

        if (listVO.isLikeSearch()) {
            query.where(user.searchPredicates(listVO.getKeyword()));
        } else {
            query.where(user.email.eq(listVO.getKeyword()).or(user.mobile.eq(listVO.getKeyword())));
        }

        if (ArrayUtils.isNotEmpty(listVO.getExcludeAuthInstitutes()))
            query.where(user.authInstitute.notIn(listVO.getExcludeAuthInstitutes()));

        query.orderBy(user.createdDt.desc());

        listVO.setFullListSize(query.count());
        listVO.pagination(query);
        listVO.setList(query.list(new QUserSearchListVO_Data(user.id, user.subId, platform.id, platform.name, user.email, user.name, user.mobile,
                userBalance.balance, agent.id, agent.name)));
        return listVO;
    }

    @Override
    public Map<String, List<DeviceVO>> getUserDeviceMap(List<String> userIds) {
        JPAQuery query = new JPAQuery(entityManager);
        QDevice device = QDevice.device;
        QUserDevice userDevice = QUserDevice.userDevice;
        QUser user = QUser.user;
        query.from(userDevice).join(userDevice.user, user).join(userDevice.device, device).where(user.id.in(userIds));

        return query.transform(groupBy(user.id).as(list(new QDeviceVO(device.id, userDevice.id.slot, device.deviceType, device.deviceModel))));
    }

    @Override
    public List<String> getEnabledUserIdsByMobile(String[] mobile) {
        JPAQuery query = new JPAQuery(entityManager);
        QUser user = QUser.user;
        query.from(user).where(user.mobile.in(mobile).and(user.enabled.isTrue()));
        return query.list(user.id);
    }
}
