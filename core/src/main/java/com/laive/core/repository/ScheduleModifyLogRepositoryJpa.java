package com.laive.core.repository;

import com.laive.core.domain.QRole;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.RoleListVO;
import com.laive.core.vo.ScheduleModifyLogListVO;
import com.mysema.query.jpa.impl.JPAQuery;
import org.springframework.stereotype.Repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.domain.ScheduleModifyLog;

@Repository("scheduleModifyLogRepository")
public class ScheduleModifyLogRepositoryJpa extends GenericRepositoryJpa<ScheduleModifyLog, String> implements ScheduleModifyLogRepository {

  public ScheduleModifyLogRepositoryJpa() {
    super(ScheduleModifyLog.class);
  }

  @Override
  public ScheduleModifyLogListVO getListVO(ScheduleModifyLogListVO listVO) {
//    JPAQuery query = new JPAQuery(entityManager);
//    QRole role = QRole.role;
//
//    query.from(role).orderBy(role.id.desc());
//    ScheduleModifyLogListVO.Search search = listVO.getSearch();
//    if (StringUtils.isNotBlank(search.getSearchword())) {
//      query.where(role.name.contains(search.getSearchword()));
//    }
//
//    if (search.getServiceSite() != null)
//      query.where(role.serviceSite.eq(search.getServiceSite()));
//
//    listVO.setFullListSize(query.count());
//    listVO.pagination(query);
//    listVO.setList(query.list(role));
    return listVO;
  }


}
