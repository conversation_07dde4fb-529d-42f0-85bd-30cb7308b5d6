package com.laive.core.repository;

import com.laive.core.base.GenericRepository;
import com.laive.core.domain.ChannelScheduleMapper;

import java.util.List;
import java.util.Map;

public interface ChannelScheduleMapperRepository extends GenericRepository<ChannelScheduleMapper, ChannelScheduleMapper.Id> {

  List<ChannelScheduleMapper> getListByOriginalChannelId(String channelId);

  String getOriginalChannelIdFromChannelId(String channelId);

  Map<String,String> getChannelScheduleMapperMap(List<String> servicePlatformChannelIds, String requestPlatformId);
}
