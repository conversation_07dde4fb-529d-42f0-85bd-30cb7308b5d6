package com.laive.core.repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.domain.Contact;
import com.laive.core.domain.QAdmin;
import com.laive.core.domain.QContact;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.ContactListVO;
import com.mysema.query.jpa.impl.JPAQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("contactRepository")
public class ContactRepositoryJpa extends GenericRepositoryJpa<Contact, String> implements ContactRepository {

  public ContactRepositoryJpa() {
    super(Contact.class);
  }

  @Override
  public ContactListVO getListVO(ContactListVO listVO) {

    JPAQuery query = new JPAQuery(entityManager);
    QContact contact = QContact.contact;
    query.from(contact).orderBy(contact.updatedDt.desc());

    ContactListVO.Search search = listVO.getSearch();
    if (StringUtils.isNotBlank(search.getCsType())) {
      query.where(contact.csType.eq(search.getCsType()));
    }

    if (StringUtils.isNotBlank(search.getSearchword())) {
      QAdmin operator = QAdmin.admin;
      query.join(contact.operator, operator);
      query.where(operator.username.contains(search.getSearchword()).or(operator.email.contains(search.getSearchword
              ())).or(operator.fullName.contains(search.getSearchword())));
    }

    long entryCount = query.count();
    listVO.setFullListSize(entryCount);
    int pageCount = (int)Math.ceil(((double) entryCount / listVO.getPageSize()));
    if(listVO.getPage() > pageCount)
      listVO.setPage(pageCount);

    listVO.pagination(query);
    listVO.setList(query.list(contact));
    return listVO;
  }

  @Override
  public List<Contact> getListByCsType(String csType) {
    JPAQuery query = new JPAQuery(entityManager);
    QContact contact = QContact.contact;
    query.from(contact).where(contact.csType.eq(csType));
    return query.list(contact);
  }


}
