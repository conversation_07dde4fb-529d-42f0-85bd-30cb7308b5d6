package com.laive.core.repository;

import com.laive.core.base.GenericRepository;
import com.laive.core.domain.WatchChannelLog;
import com.laive.core.domain.WatchVodLog;
import com.laive.core.domain.stats.WatchChannelCount;
import com.laive.core.domain.stats.WatchChannelDailyStat;
import com.laive.core.domain.stats.WatchChannelDuration;
import com.laive.core.vo.*;

import java.util.Date;
import java.util.List;

public interface WatchChannelLogRepository extends GenericRepository<WatchChannelLog, String> {

  WatchChannelLog getWatchChannelLogOnWatching(String userId);

  List<WatchChannelCount> getWatchChannelCount();

  List<String> getWatchChannelLogIdsOnWatching(String userId, String channelId);

  void closeWatchChannelLogOnWatchingBeforeHours(int beforeHour);

  WatchChannelStatsVO getWatchChannelStatsVO(WatchChannelStatsVO statsVO);

  ChannelRankingListVO getChannelRankingListVO(ChannelRankingListVO listVO);

  WatchChannelDurationStatsVO getWatchDurationStatsGroupByChannel(WatchChannelDurationStatsVO statsVO);

  long getTotalWatchDuration(WatchChannelDurationStatsVO statsVO);

  long getUserCount(String platformId, Date searchDate);

  ChannelWatchStatsVO getChannelWatchStatsVO(ChannelWatchStatsVO channelWatchStatsVO);

  void closeWatchChannelLogs(List<String> watchChannelLogIds);

  List<WatchChannelDailyStat> getDailyStat(Date date);

  List<WatchChannelDailyStat> getHourlyStats(Date date);

  List<WatchChannelCount> getChannelCountStats(Date date);

  List<WatchChannelDuration> getChannelDurationStats(Date date);

  List<String> getLogIdsByBeforeDtAndSize(Date beforeDt, int deleteStepSize);

  long deleteLogsByIds(List<String> watchChannelLogIds);

  // added by rakie
  Date getOldestRawDateFromWatchChannelLog();

  Long removeRawData(Date startDt, Date endDt, int delCount);

  // Added by Piyal
  List <WatchChannelLog> findWatchChannelLogByUser(String userId);  // device id not null

  WatchChannelLog getWatchChannelLogIdsOnWatchingMobile(String userId, String channelId, String deviceId);

  List<WatchChannelLog> getChannelLogByUserIdAndDeviceId(String userId, String deviceId);

  List<WatchChannelLog> getChannelLogByDeviceId(String deviceId);

  ChannelAnalyticsExportVO getChannelAnalyticsExcelData(ChannelAnalyticsExportVO listVO);
}
