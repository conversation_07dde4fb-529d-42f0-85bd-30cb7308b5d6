package com.laive.core.repository;

import com.laive.core.domain.QSubMenu;
import com.mysema.query.jpa.impl.JPAQuery;
import org.springframework.stereotype.Repository;

import com.laive.core.base.GenericRepositoryJpa;
import com.laive.core.domain.SubMenu;

import java.util.Map;

@Repository("subMenuRepository")
public class SubMenuRepositoryJpa extends GenericRepositoryJpa<SubMenu, String> implements SubMenuRepository {

  public SubMenuRepositoryJpa() {
    super(SubMenu.class);
  }

  @Override
  public Map<String, String> getRequestUriMenuMap() {
    JPAQuery query = new JPAQuery(entityManager);
    QSubMenu subMenu = QSubMenu.subMenu;
    query.from(subMenu);
    return query.map(subMenu.uri, subMenu.parent.id);
  }
}
