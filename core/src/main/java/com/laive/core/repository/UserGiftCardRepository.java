package com.laive.core.repository;

import com.laive.core.base.GenericRepository;
import com.laive.core.domain.UserGiftCard;
import com.laive.core.vo.UserGiftCardListVO;
import com.laive.core.vo.UserGiftCardRegisterLogListVO;


public interface UserGiftCardRepository extends GenericRepository<UserGiftCard, String> {

  UserGiftCardListVO getListVO(UserGiftCardListVO listVO);

  UserGiftCardRegisterLogListVO getRegisterLogListVO(UserGiftCardRegisterLogListVO listVO);

}
