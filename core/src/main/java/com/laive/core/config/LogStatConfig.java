package com.laive.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.inject.Inject;

@Component
@Slf4j
public class LogStatConfig {
  @Inject
  private Environment env;

  private String protocol = "http://";
  private String host = null;
  private String port = null;
  private String dailyExcelUrl = null;
  private String excelStatUrl = null;
  private String dashboardGetStatUrl = null;
  private String campaignExposeCountUrl = null;
  private String inventoryExposeCountUrl = null;

  private Boolean isApiEnabledFlag = null;
  private Boolean isSendMessageEnabledFlag = null;


  public boolean isApiEnabled() {
    if (isApiEnabledFlag == null) {
      String enabledStr = env.getProperty("logstat.api.enabled", "0");
      if (enabledStr != null && enabledStr.equals("1")) {
        isApiEnabledFlag = Boolean.TRUE;
      }
      else {
        isApiEnabledFlag = Boolean.FALSE;
      }
    }

    return isApiEnabledFlag.booleanValue();
  }

  public boolean isSendMessageEnabled() {
    if (isSendMessageEnabledFlag == null) {
      String enabledStr = env.getProperty("logstat.send.message.enabled", "0");
      if (enabledStr != null && enabledStr.equals("1")) {
        isSendMessageEnabledFlag = Boolean.TRUE;
      }
      else {
        isSendMessageEnabledFlag = Boolean.FALSE;
      }
    }

    return isSendMessageEnabledFlag.booleanValue();
  }

  private String getHost() {
    if (host == null) {
      host = env.getProperty("logstat.host", "*************");
    }
    return host;
  }

  private String getPort() {
    if (port == null) {
      port = env.getProperty("logstat.port", "9090");
    }
    return port;
  }

  private String makeUrl(String queryUrl) {
    return protocol + getHost() + ":" + getPort() + queryUrl;
  }

  public String getDailyExcelUrl() {
    if (dailyExcelUrl == null) {
      String queryUrl = env.getProperty("logstat.api.getDailyExcel", "/campaign/adCampaignDaily");
      dailyExcelUrl = makeUrl(queryUrl);
    }
    return dailyExcelUrl;
  }

  public String getExcelStatUrl() {
    if (excelStatUrl == null) {
      String queryUrl = env.getProperty("logstat.api.getExcelStat", "/campaign/adCampaignStat");
      excelStatUrl = makeUrl(queryUrl);
    }
    return excelStatUrl;
  }

  public String getDashboardGetStatUrl() {
    if (dashboardGetStatUrl == null) {
      String queryUrl = env.getProperty("logstat.api.dashboardGetStat", "/campaign/dashboardGetStat");
      dashboardGetStatUrl = makeUrl(queryUrl);
    }
    return dashboardGetStatUrl;
  }

  public String getCampaignExposeCountUrl() {
    if (campaignExposeCountUrl == null) {
      String queryUrl = env.getProperty("logstat.api.getCampaignExposeCount", "/campaign/getCampaignExposeCount");
      campaignExposeCountUrl = makeUrl(queryUrl);
    }
    return campaignExposeCountUrl;
  }

  public String getInventoryExposeCountUrl() {
    if (inventoryExposeCountUrl == null) {
      String queryUrl = env.getProperty("logstat.api.getInventoryExposeCount", "/campaign/getInventoryExposeCount");
      inventoryExposeCountUrl = makeUrl(queryUrl);
    }
    return inventoryExposeCountUrl;
  }


}
