package com.laive.core.base;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.laive.core.constants.ColumnSizeConstants;

@SuppressWarnings("serial")
@MappedSuperclass
@EqualsAndHashCode(callSuper = false, of = { "id" })
@Getter
@Setter
@ToString
public abstract class BaseCodeEntity<PK extends Serializable> extends BaseEntity<PK> {

  @Id
  @GeneratedValue
  private PK id;

  @Column(name = "name", length = ColumnSizeConstants.CODE_NAME)
  private String name;

  @Column(name = "sort_seq")
  private Integer sortSeq;

  @Override
  public PK getId() {
    return id;
  }

}
