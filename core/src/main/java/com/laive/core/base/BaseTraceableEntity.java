package com.laive.core.base;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import lombok.Getter;
import lombok.Setter;

import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import com.laive.core.constants.ColumnSizeConstants;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@SuppressWarnings("serial")
@MappedSuperclass()
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseTraceableEntity<PK extends Serializable> extends BaseCreateTraceableEntity<PK> {

  @Column(name = "updated_dt", insertable = true, updatable = true)
  @Temporal(TemporalType.TIMESTAMP)
  @LastModifiedDate  
  protected Date updatedDt;

  @Column(name = "updated_by", insertable = true, updatable = true, length = ColumnSizeConstants.UUID)
  @LastModifiedBy  
  protected String updatedBy;

}