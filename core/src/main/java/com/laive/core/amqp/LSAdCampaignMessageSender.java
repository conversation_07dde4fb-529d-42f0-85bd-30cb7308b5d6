package com.laive.core.amqp;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.io.Serializable;

@Slf4j
public class LSAdCampaignMessageSender {
  private String exchangeName;
  private String routingKey;
  private final RabbitTemplate rabbitTemplate;

  public LSAdCampaignMessageSender(RabbitTemplate rabbitTemplate, String exchangeName, String routingKey) {
    this.exchangeName = exchangeName;
    this.routingKey = routingKey;
    this.rabbitTemplate = rabbitTemplate;
  }

  public void send(final LSCampaignExposeMessage message) {
    rabbitTemplate.convertAndSend(exchangeName, routingKey, message);
    log.info("LSAdCampaignMessageSender.send() -> exchangeName:{} sent..", exchangeName);
  }
}
