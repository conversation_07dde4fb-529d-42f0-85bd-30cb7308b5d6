package com.laive.core.aws;

import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.laive.core.util.ConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AwsConfig {

  @Autowired
  private ConfigUtil configUtil;


  @Bean
  public TransferManager transferManager() {
    return new TransferManager(basicAWSCredentials());
  }

  private BasicAWSCredentials basicAWSCredentials() {
    String accessKey = configUtil.getAwsAccessKey();
    String secretKey = configUtil.getAwsSecretKey();
    return new BasicAWSCredentials(accessKey, secretKey);
  }


}
