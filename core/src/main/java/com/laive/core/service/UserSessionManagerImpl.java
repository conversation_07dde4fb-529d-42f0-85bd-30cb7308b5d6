package com.laive.core.service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.User;
import com.laive.core.domain.UserSession;
import com.laive.core.exception.EntityNotExistException;
import com.laive.core.repository.UserSessionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("userSessionManager")
public class UserSessionManagerImpl extends GenericManagerImpl<UserSession, UserSession.Id> implements UserSessionManager {


  private UserSessionRepository userSessionRepository;

  @Autowired
  public void setUserSessionRepository(UserSessionRepository userSessionRepository) {
    super.repository = userSessionRepository;
    this.userSessionRepository = userSessionRepository;
  }

  @Override
  public void create(User user, String deviceType, String jsessionId) {
    UserSession userSession;
    try {
      userSession = userSessionRepository.get(new UserSession.Id(user.getId(), deviceType));
      userSession.setSessionId(jsessionId);
    } catch (EntityNotExistException e) {
      userSession = new UserSession(user, deviceType, jsessionId);
    }
    merge(userSession);
  }
}
