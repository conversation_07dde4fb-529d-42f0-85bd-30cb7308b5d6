package com.laive.core.service;

import java.util.*;

import com.laive.core.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.*;
import com.laive.core.repository.VodPaymentRepository;
import com.laive.core.vo.*;
import com.laive.core.vo.SalesOverviewVO.SalesStatVO;

@Service("vodPaymentManager")
public class VodPaymentManagerImpl extends GenericManagerImpl<VodPayment, String> implements VodPaymentManager {

  private VodPaymentRepository vodPaymentRepository;

  @Autowired
  public void setVodPaymentRepository(VodPaymentRepository vodPaymentRepository) {
    super.repository = vodPaymentRepository;
    this.vodPaymentRepository = vodPaymentRepository;
  }

  @Override
  public boolean existsActivePaymentByPurchaseType(String userId, String vodId, PurchaseType purchaseType) {
    return vodPaymentRepository.existsActivePaymentByPurchaseType(userId, vodId, purchaseType);
  }

  @Override
  public VodPaymentListVO getPayments(VodPaymentListVO listVO) {
    return vodPaymentRepository.getPayments(listVO);
  }

  @Override
  public VodPaymentListVO getRentList(VodPaymentListVO listVO) {
    listVO.setPurchaseType(PurchaseType.RENT);
    return vodPaymentRepository.getPayments(listVO);
  }

  @Override
  public VodPaymentListVO getBuyList(VodPaymentListVO listVO) {
    listVO.setPurchaseType(PurchaseType.BUY);
    return vodPaymentRepository.getPayments(listVO);
  }

  @Override
  public List<VodPayment> getActivePaymentsByVods(String userId, List<Vod> vodList) {
    return vodPaymentRepository.getActivePaymentsByVods(userId, vodList);
  }

  @Override
  public List<VodPayment> getActivePaymentsByVod(String userId, Vod vod) {
    return getActivePaymentsByVods(userId, Arrays.asList(vod));
  }

  @Override
  public SalesStatVO getSalesStatVO(Platform platform, Date fromDt, Date toDt) {
    String platformId = "";
    if (platform != null)
      platformId = platform.getId();
    return vodPaymentRepository.getSalesStatVO(platformId, fromDt, toDt);
  }

  @Override
  public List<SalesStatVO> getBestSellingVods(Platform platform, Date fromDt, Date toDt) {
    long limitSize = 2;
    return vodPaymentRepository.getSalesCountStats(platform.getId(), limitSize, fromDt, toDt);
  }

  @Override
  public List<SalesStatVO> getSalesCountStats(Platform platform, Date fromDt, Date toDt) {
    long limitSize = 0;
    return vodPaymentRepository.getSalesCountStats(platform.getId(), limitSize, fromDt, toDt);
  }

  @Override
  public List<SalesStatVO> getSalesAmountStats(Platform platform, Date fromDt, Date toDt) {
    return getSalesAmountStats(platform, fromDt, toDt, 0, null);
  }

  @Override
  public List<SalesStatVO> getSalesAmountStats(Platform platform, Date fromDt, Date toDt, long limitSize, VodSaleType vodSaleType) {
    String platformId = platform == null ? "" : platform.getId();
    return vodPaymentRepository.getSalesAmountStats(platformId, limitSize, fromDt, toDt, vodSaleType);
  }

  @Override
  public SalesListVO getSalesList(SalesListVO listVO) {
    listVO = vodPaymentRepository.getSalesList(listVO);
    listVO.setSum(vodPaymentRepository.getSalesSum(listVO));
    return listVO;
  }

  @Override
  public void removeByUserAndVod(String userId, String vodId) {
    vodPaymentRepository.removeByUserAndVod(userId, vodId);
  }

  @Override
  public List<VodPayment> getActivePaymentsByParentVod(String userId, Vod vod) {
    if (CollectionUtils.isEmpty(vod.getParentVods()))
      return Collections.emptyList();

    return getActivePaymentsByParentVods(userId, Arrays.asList(vod));
  }

  @Override
  public List<VodPayment> getActivePaymentsByParentVods(String userId, List<Vod> parentVods) {
    if (CollectionUtils.isEmpty(parentVods))
      return Collections.emptyList();

    List<VodPayment> vodPayments = vodPaymentRepository.getActivePaymentsByPackageVods(userId, parentVods);
    return vodPayments;
  }

  @Override
  public List<VodPayment> getActivePaymentsByVodAndType(String userId, String vodId, PurchaseType purchaseType) {
    List<VodPayment> vodPayments = vodPaymentRepository.getActivePaymentsByVodAndType(userId, vodId, purchaseType);
    return vodPayments;
  }

  @Override
  public Map<String, VodPayment> getLastPaymentMaps(String userId, Set<String> vodPriceIdSets) {
    return vodPaymentRepository.getLastPaymentMaps(userId, vodPriceIdSets);
  }

  @Override
  public Map<String, VodPayment> getActivePaymentsMapByProductId(String userId, Set<String> productIdSets) {
    return vodPaymentRepository.getActivePaymentsMapByProductId(userId, productIdSets);
  }

  @Override
  public SalesStatsVO getDailySalesStatsVO(SalesStatsVO statsVO) {
    return vodPaymentRepository.getDailySalesStatsVO(statsVO);
  }

  @Override
  public SalesStatsVO getMonthlySalesStatsVO(SalesStatsVO statsVO) {
    return vodPaymentRepository.getMonthlySalesStatsVO(statsVO);
  }

  @Override
  public SalesByTitleListVO getSalesByTitleList(SalesByTitleListVO listVO) {
    return vodPaymentRepository.getSalesByTitleList(listVO);
  }

  @Override
  public BillingListVO getBillingListVO(BillingListVO listVO) {
    return vodPaymentRepository.getBillingListVO(listVO);
  }

  @Override
  public List<VodPayment> getActivePaymentsByVodAndTypeAndBillingMethod(String userId, String vodId, PurchaseType purchaseType,
                                                                        BillingMethod billingMethod) {
    List<VodPayment> vodPayments = vodPaymentRepository.getActivePaymentsByVodAndTypeAndBillingMethod(userId, vodId, purchaseType,
        billingMethod);
    return vodPayments;
  }

  @Override
  public void removeAllByUserIdAndBillingMethod(String userId, BillingMethod billingMethod) {
    vodPaymentRepository.removeAllByUserIdAndBillingMethod(userId, billingMethod);
  }

  @Override
  public void removeAllByUserIdAndBillingMethods(String userId, List<BillingMethod> removeTargetBillingMethods) {
    vodPaymentRepository.removeAllByUserIdAndBillingMethods(userId, removeTargetBillingMethods);
  }

  @Override
  public VodPayment getActivePaymentByVodAndEndDt(String userId, String vodId, Date endDt) {
    return vodPaymentRepository.getActivePaymentByVodAndEndDt(userId, vodId, endDt);
  }

  @Override
  public List<VodPayment> getActivePaymentsByUserId(String userId) {
    return vodPaymentRepository.getActivePaymentsByUserId(userId);
  }

  @Override
  public Payment getRecentActiveRentPayment(String userId, String vodId) {
    return vodPaymentRepository.getRecentActiveRentPayment(userId, vodId);
  }

  @Override
  public boolean existPastPaymentHistory(String userId, String vodId) {
    return vodPaymentRepository.existPastPaymentHistory(userId, vodId);
  }
}
