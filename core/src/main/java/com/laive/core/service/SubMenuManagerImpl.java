package com.laive.core.service;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.SubMenu;
import com.laive.core.repository.SubMenuRepository;

@Service("subMenuManager")
public class SubMenuManagerImpl extends GenericManagerImpl<SubMenu, String> implements SubMenuManager {

  private SubMenuRepository subMenuRepository;

  @Autowired
  public void setSubMenuRepository(SubMenuRepository subMenuRepository) {
    super.repository = subMenuRepository;
    this.subMenuRepository = subMenuRepository;
  }

  @Override
  public Map<String, String> getRequestUriMenuMap() {
    return subMenuRepository.getRequestUriMenuMap();
  }
}
