package com.laive.core.service;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.GiftCardPromotion;
import com.laive.core.repository.GiftCardPromotionRepository;
import com.laive.core.vo.GiftCardPromotionListVO;

@Service("giftCardPromotionManager")
public class GiftCardPromotionManagerImpl extends GenericManagerImpl<GiftCardPromotion, String> implements
    GiftCardPromotionManager {

  private GiftCardPromotionRepository giftCardPromotionRepository;

  @Autowired
  public void setGiftCardPromotionRepository(GiftCardPromotionRepository giftCardPromotionRepository) {
    super.repository = giftCardPromotionRepository;
    this.giftCardPromotionRepository = giftCardPromotionRepository;
  }

  @Override
  public GiftCardPromotionListVO getListVO(GiftCardPromotionListVO listVO) {
    return giftCardPromotionRepository.getListVO(listVO);
  }

  @Override
  public boolean existSameGiftCardPromotion(GiftCardPromotion giftCardPromotion, Date startDt, Date endDt) {
    return giftCardPromotionRepository.existSameGiftCardPromotion(giftCardPromotion, startDt, endDt);
  }

  @Override
  public GiftCardPromotion getActivePromotion(String giftCardId) {
    return giftCardPromotionRepository.getActivePromotion(giftCardId);
  }

}
