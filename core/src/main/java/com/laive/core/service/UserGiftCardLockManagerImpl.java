package com.laive.core.service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.domain.UserGiftCardLock;
import com.laive.core.domain.UserGiftCardLock.LockStep;
import com.laive.core.exception.BusinessException;
import com.laive.core.exception.EntityNotExistException;
import com.laive.core.repository.UserGiftCardLockRepository;
import com.laive.core.repository.UserRepository;
import com.laive.core.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service("userGiftCardLockManager")
public class UserGiftCardLockManagerImpl extends GenericManagerImpl<UserGiftCardLock, String> implements UserGiftCardLockManager {

  @Autowired
  private UserRepository userRepository;

  private UserGiftCardLockRepository userGiftCardLockRepository;

  @Autowired
  public void setUserGiftCardLockRepository(UserGiftCardLockRepository userGiftCardLockRepository) {
    super.repository = userGiftCardLockRepository;
    this.userGiftCardLockRepository = userGiftCardLockRepository;
  }

  @Override
  @Transactional
  public UserGiftCardLock check(String userId) {
    UserGiftCardLock userGiftCardLock = null;
    try {
      userGiftCardLock = get(userId);
      userGiftCardLock = check(userGiftCardLock);
    } catch(EntityNotExistException ex) {
      // ignore
    }
    return userGiftCardLock;
  }

  private UserGiftCardLock check(UserGiftCardLock userGiftCardLock) {
    if (userGiftCardLock.getLockDate() != null) {
      if (userGiftCardLock.getLockStep().equals(LockStep.FIRST)) {
        if (DateUtil.getDateDiffHours(new Date(), userGiftCardLock.getLockDate()) < 1) {
          throw new BusinessException(ReturnCodes.ERROR_USER_GIFTCARD_LOCK_FIRST);
        } else {
          userGiftCardLock = clearLock(userGiftCardLock);
        }
      } else {
        if (DateUtil.getDateDiffHours(new Date(), userGiftCardLock.getLockDate()) < 24) {
          throw new BusinessException(ReturnCodes.ERROR_USER_GIFTCARD_LOCK_SECOND);
        } else {
          userGiftCardLock = clearLock(userGiftCardLock);
        }
      }
    }
    return userGiftCardLock;
  }

  @Override
  public UserGiftCardLock clearLock(UserGiftCardLock userGiftCardLock) {
    userGiftCardLock.clearLock();
    return merge(userGiftCardLock);
  }

  @Override
  @Transactional(noRollbackFor = {})
  public UserGiftCardLock fail(String userId) {
    UserGiftCardLock userGiftCardLock = null;
    try {
      userGiftCardLock = get(userId);
      userGiftCardLock.setFailCount(userGiftCardLock.getFailCount() + 1);
    } catch(EntityNotExistException ex) {
      userGiftCardLock = new UserGiftCardLock();
      userGiftCardLock.setUser(userRepository.get(userId));
      userGiftCardLock.setFailCount(1);
    }

    if (userGiftCardLock.getFailCount() >= 3) {
      userGiftCardLock.setLockDate(new Date());
      if (userGiftCardLock.getLockStep() == null) {
        userGiftCardLock.setLockStep(LockStep.FIRST);

      } else if(userGiftCardLock.getLockStep().equals(LockStep.FIRST)) {
        userGiftCardLock.setLockStep(LockStep.SECOND);
      }
    }

    userGiftCardLock = merge(userGiftCardLock);
    return userGiftCardLock;
  }


}
