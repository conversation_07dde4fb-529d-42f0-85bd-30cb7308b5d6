package com.laive.core.service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.Payment;
import com.laive.core.repository.PaymentRepository;
import com.laive.core.vo.PaymentListVO;
import com.laive.core.vo.UserPaymentListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("paymentManager")
public class PaymentManagerImpl extends GenericManagerImpl<Payment, String> implements PaymentManager {

    private PaymentRepository paymentRepository;

    @Autowired
    public void setPaymentRepository(PaymentRepository paymentRepository) {
        super.repository = paymentRepository;
        this.paymentRepository = paymentRepository;
    }

    @Override
    public PaymentListVO getList(PaymentListVO listVO) {
        return paymentRepository.getListVO(listVO);
    }

    @Override
    public void deletePayment(String paymentId) {
        paymentRepository.deletePayment(paymentId);
    }

    @Override
    public void deletePaymentAll(String userId) {
        paymentRepository.deletePaymentAll(userId);
    }

    @Override
    public boolean isPaymentOwner(String userId, String paymentId) {
        return paymentRepository.isPaymentOwner(userId, paymentId);
    }

    @Override
    public UserPaymentListVO getUserPaymentList(UserPaymentListVO listVO) {
        return paymentRepository.getUserPaymentList(listVO);
    }

    @Override
    public Payment getUserPaymentExpiration(String userId) {
        return paymentRepository.getUserPaymentExpiration(userId);
    }

    @Override
    public List<Payment> getUserPaymentExpiration(List<String> userIds) {
        return paymentRepository.getUserPaymentExpiration(userIds);
    }

    @Override
    public Payment getLastPaymentByUserIdAndType(String userId, String type) {
        return paymentRepository.getLastPaymentByUserIdAndType(userId, type);
    }


}
