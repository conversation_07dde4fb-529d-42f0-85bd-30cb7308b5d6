package com.laive.core.service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.Channel;
import com.laive.core.domain.ScheduleVersion;
import com.laive.core.repository.ChannelRepository;
import com.laive.core.repository.ScheduleVersionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("scheduleVersionManager")
public class ScheduleVersionManagerImpl extends GenericManagerImpl<ScheduleVersion, String> implements ScheduleVersionManager {

  @Autowired
  private ChannelRepository channelRepository;

  private ScheduleVersionRepository scheduleVersionRepository;

  @Autowired
  public void setScheduleTemplateRepository(ScheduleVersionRepository scheduleVersionRepository) {
    this.repository = scheduleVersionRepository;
    this.scheduleVersionRepository = scheduleVersionRepository;
  }

//  @Async
  @Override
  public void save(String channelId, long timestamp) {
    scheduleVersionRepository.disableAll(channelId);

    Channel channel = channelRepository.get(channelId);
    ScheduleVersion scheduleVersion = new ScheduleVersion();
    scheduleVersion.setChannel(channel);
    scheduleVersion.setTimestamp(timestamp);
    scheduleVersion.setEnabled(true);
    merge(scheduleVersion);
  }

  @Override
  public List<ScheduleVersion> getAllActiveScheduleVersions(String platformId, String countryCode) {
    return scheduleVersionRepository.getAllActiveScheduleVersions(platformId, countryCode);
  }

  @Override
  public ScheduleVersion getByChannelId(String channelId) {
    return scheduleVersionRepository.getByChannelId(channelId);
  }
}
