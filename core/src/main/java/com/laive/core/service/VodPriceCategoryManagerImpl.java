package com.laive.core.service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.VodPriceCategory;
import com.laive.core.repository.VodPriceCategoryRepository;
import com.laive.core.vo.VodCategoryListVO;
import com.laive.core.vo.VodCategoryVO;
import com.laive.core.vo.VodPriceCategoryListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("vodPriceCategoryManager")
public class VodPriceCategoryManagerImpl extends GenericManagerImpl<VodPriceCategory, VodPriceCategory.Id> implements VodPriceCategoryManager {

  private VodPriceCategoryRepository vodPriceCategoryRepository;

  @Autowired
  public void setVodPriceCategoryRepository(VodPriceCategoryRepository vodPriceCategoryRepository) {
    super.repository = vodPriceCategoryRepository;
    this.vodPriceCategoryRepository = vodPriceCategoryRepository;
  }

  @Override
  public VodPriceCategoryListVO getListVO(VodPriceCategoryListVO listVO) {
    return vodPriceCategoryRepository.getListVO(listVO);
  }

  @Override
  public VodCategoryListVO getVodCategoryListVO(VodCategoryListVO listVO) {
    return vodPriceCategoryRepository.getVodCategoryListVO(listVO);
  }

  @Override
  public void saveProductDisplayOrder(VodCategoryVO vodCategoryVO) {

    List<VodPriceCategory> vodPriceCategories = vodPriceCategoryRepository.getListByCategoryIdAndVodId(vodCategoryVO.getCategoryId(), vodCategoryVO.getVodId());
    for (VodPriceCategory vodPriceCategory : vodPriceCategories) {
      vodPriceCategory.setSortSeq(vodCategoryVO.getSortSeq());
      vodPriceCategoryRepository.merge(vodPriceCategory);
    }
  }

}
