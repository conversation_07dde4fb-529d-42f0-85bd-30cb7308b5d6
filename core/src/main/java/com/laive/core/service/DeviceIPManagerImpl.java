package com.laive.core.service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.Device;
import com.laive.core.domain.DeviceIP;
import com.laive.core.domain.User;
import com.laive.core.repository.DeviceIPRepository;
import com.laive.core.util.GeoLocationUtil;
import com.laive.core.vo.DeviceIPExcelListVO;
import com.laive.core.vo.DeviceIPListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("deviceIPManager")
@RequiredArgsConstructor
@Slf4j
public class DeviceIPManagerImpl extends GenericManagerImpl<DeviceIP, String> implements DeviceIPManager {

    private DeviceIPRepository deviceIPRepository;

    private final GeoLocationProviderService geoLocationProviderService;

    @Autowired
    public void setDeviceIPRepository(DeviceIPRepository deviceIPRepository) {
        super.repository = deviceIPRepository;
        this.deviceIPRepository = deviceIPRepository;
    }

    @Override
    public DeviceIPListVO getList(DeviceIPListVO listVO) {
        return deviceIPRepository.getList(listVO);
    }

    @Override
    public DeviceIP saveDeviceIp(Device device, User user, String requestURI, String remoteAddr) {
        DeviceIP deviceIP = deviceIPRepository.getByUser(user);

        if (deviceIP != null) {
            GeoLocationUtil geoLocationUtil = geoLocationProviderService.getGeoLocationUtil(remoteAddr);
            if (geoLocationUtil != null) {
                log.info("GEO-LOCATION EXTRACTED FROM MAXMIND: {}", geoLocationUtil);

                deviceIP.setUserCity(geoLocationUtil.getCity());
                deviceIP.setUserState(geoLocationUtil.getState());
                deviceIP.setUserCountry(geoLocationUtil.getCountry());
            }

            deviceIP.setDevice(device);
            deviceIP.setIpAddress(remoteAddr);
            deviceIP.setRequestedUrl(requestURI);
            return deviceIPRepository.merge(deviceIP);
        } else {
            return deviceIPRepository.merge(new DeviceIP(user, device, requestURI, remoteAddr));
        }
    }

    @Override
    public DeviceIPExcelListVO getExcelList(DeviceIPExcelListVO listVO) {
        return deviceIPRepository.getExcelList(listVO);
    }
}
