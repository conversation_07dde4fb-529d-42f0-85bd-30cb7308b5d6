package com.laive.core.service;

import com.laive.core.base.GenericManager;
import com.laive.core.domain.UserNotification;
import com.laive.core.push.PushTargetVO;
import com.laive.core.vo.UserNotificationListVO;

import java.util.List;

public interface UserNotificationManager extends GenericManager<UserNotification, UserNotification.Id> {

  UserNotificationListVO getListVO(UserNotificationListVO listVO);

  long getNotReadCount(String userId);

  void readAll(String userId);

  void create(String title, String content, List<PushTargetVO> pushTargetVOs);
}
