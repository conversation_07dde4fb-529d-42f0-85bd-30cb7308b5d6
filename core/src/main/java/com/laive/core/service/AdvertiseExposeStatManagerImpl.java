package com.laive.core.service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.config.LogStatConfig;
import com.laive.core.domain.stats.AdvertiseExposeStat;
import com.laive.core.repository.AdvertiseExposeStatRepository;
import com.laive.core.repository.CampaignRepository;
import com.laive.core.vo.CampaignDailyExcelListVO;
import com.laive.core.vo.CampaignExcelListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("advertiseExposeStatManager")
public class AdvertiseExposeStatManagerImpl extends GenericManagerImpl<AdvertiseExposeStat, AdvertiseExposeStat.Id> implements AdvertiseExposeStatManager {

  @Autowired
  private CampaignRepository campaignRepository;

  @Autowired
  private LogStatConfig logStatConfig;

  private AdvertiseExposeStatRepository advertiseExposeStatRepository;

  @Autowired
  public void setAdvertiseExposeStatRepository(AdvertiseExposeStatRepository advertiseExposeStatRepository) {
    super.repository = advertiseExposeStatRepository;
    this.advertiseExposeStatRepository = advertiseExposeStatRepository;
  }

  @Override
  public Long getExposeSum(String campaignId, String inventoryId) {
    return advertiseExposeStatRepository.getExposeSum(campaignId, inventoryId);
  }

  @Override
  public CampaignDailyExcelListVO getExposedDailyStatList(CampaignDailyExcelListVO listVO) {
    Map<String, String> campaignNameMap = campaignRepository.getCampaignNameMap(listVO.getCampaignIds());

    // edit by rakie
    if (logStatConfig.isApiEnabled()) {
      listVO = advertiseExposeStatRepository.getDailyExposedStatListUsingLogStat(listVO);
    } else {
      listVO = advertiseExposeStatRepository.getDailyExposedStatList(listVO);
    }

    listVO.getList().stream().forEach(vo -> {
      String name = campaignNameMap.getOrDefault(vo.getCampaignId(), "Unknown");
      vo.setCampaignName(name);
    });
    return listVO;
  }

  @Override
  public CampaignExcelListVO getExposedStatList(CampaignExcelListVO listVO) {

    Map<String, String> campaignNameMap = campaignRepository.getCampaignNameMap(listVO.getCampaignIds());
    Map<String, Long> campaignExposureGoalCountMap = campaignRepository.getCampaignExposeGoalCountMap(listVO.getCampaignIds());

    // edit by rakie
    if (logStatConfig.isApiEnabled()) {
      listVO = advertiseExposeStatRepository.getExposedStatListUsingLogStat(listVO);
    } else {
      listVO = advertiseExposeStatRepository.getExposedStatList(listVO);
    }

    listVO.getList().stream().forEach(vo -> {
      String name = campaignNameMap.getOrDefault(vo.getCampaignId(), "Unknown");
      vo.setCampaignName(name);

      Long totalGoalCount = campaignExposureGoalCountMap.get(vo.getCampaignId());
      vo.setContractCount(totalGoalCount);
    });
    return listVO;
  }


}
