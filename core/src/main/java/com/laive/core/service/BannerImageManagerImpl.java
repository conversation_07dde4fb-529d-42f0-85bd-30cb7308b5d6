package com.laive.core.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.BannerImage;
import com.laive.core.repository.BannerImageRepository;

import java.util.List;

@Service("bannerImageManager")
public class BannerImageManagerImpl extends GenericManagerImpl<BannerImage, BannerImage.Id> implements BannerImageManager {

  private BannerImageRepository bannerImageRepository;

  @Autowired
  public void setBannerImageRepository(BannerImageRepository bannerImageRepository) {
    super.repository = bannerImageRepository;
    this.bannerImageRepository = bannerImageRepository;
  }

  @Override
  public List<BannerImage> getListByScheduleByPass(String bypassId) {
    return bannerImageRepository.getListByScheduleByPass(bypassId);
  }
}
