package com.laive.core.service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.constants.ReturnCodes;
import com.laive.core.domain.UserVoucher;
import com.laive.core.domain.Voucher;
import com.laive.core.repository.UserVoucherRepository;
import com.laive.core.util.CollectionUtils;
import com.laive.core.util.DateUtil;
import com.laive.core.vo.UserVoucherListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("userVoucherManager")
public class UserVoucherManagerImpl extends GenericManagerImpl<UserVoucher, UserVoucher.Id> implements UserVoucherManager {

  private UserVoucherRepository userVoucherRepository;

  @Autowired
  public void setUserVoucherRepository(UserVoucherRepository userVoucherRepository) {
    super.repository = userVoucherRepository;
    this.userVoucherRepository = userVoucherRepository;
  }

  @Override
  public UserVoucherListVO getActivatedListVO(UserVoucherListVO listVO) {
    listVO = userVoucherRepository.getActivatedListVO(listVO);
    listVO = adjustTimezoneToDate(listVO);
    listVO.setReturnCode(ReturnCodes.OK);
    return listVO;
  }

  private UserVoucherListVO adjustTimezoneToDate(UserVoucherListVO listVO) {

    String timezoneId = listVO.getTimezone();
    List<UserVoucher> list = listVO.getList();
    if (CollectionUtils.isEmpty(list))
      return listVO;

    for (UserVoucher userVoucher : list) {
      Voucher voucher = userVoucher.getVoucher();
      if (voucher.getStartDt() != null) {
        String startDtString = DateUtil.getDateString("yyyyMMdd", voucher.getStartDt(), timezoneId);
        voucher.setStartDtString(startDtString);
      }
      if (voucher.getEndDt() != null) {
        String endDtString = DateUtil.getDateString("yyyyMMdd", voucher.getEndDt(), timezoneId);
        voucher.setEndDtString(endDtString);
      }
    }
    return listVO;
  }

}
