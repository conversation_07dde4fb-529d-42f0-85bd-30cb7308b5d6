package com.laive.core.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.BoxActivation;
import com.laive.core.repository.BoxActivationRepository;

@Service("boxActivationManager")
public class BoxActivationManagerImpl extends GenericManagerImpl<BoxActivation, String> implements BoxActivationManager {

  private BoxActivationRepository boxActivationRepository;

  @Autowired
  public void setBoxActivationRepository(BoxActivationRepository boxActivationRepository) {
    super.repository = boxActivationRepository;
    this.boxActivationRepository = boxActivationRepository;
  }

  @Override
  public boolean isValid(String mac, String code, int boxActivationLimitMinutes) {
    return boxActivationRepository.isValid(mac, code, boxActivationLimitMinutes);
  }

  @Override
  public BoxActivation getByTaskId(String taskId) {
    return boxActivationRepository.getByTaskId(taskId);
  }

  @Override
  public BoxActivation getByMacAddressAndCode(String macAddress, String code) {
    return boxActivationRepository.getByMacAddressAndCode(macAddress, code);
  }

  @Override
  public BoxActivation getBySerialNumber(String serialNumber) {
    return boxActivationRepository.getBySerialNumber(serialNumber);
  }

  @Override
  public BoxActivation getByBoxId(String boxId) {
    return boxActivationRepository.getByBoxId(boxId);

  }
}
