package com.laive.core.service;

import com.laive.core.vo.PrepaidCardExcelListVO;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.PrepaidCard;
import com.laive.core.domain.PrepaidCardGroup;
import com.laive.core.repository.PrepaidCardGroupRepository;
import com.laive.core.repository.PrepaidCardRepository;
import com.laive.core.util.ExcelUtil;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.PrepaidCardListVO;

@Service("prepaidCardManager")
public class PrepaidCardManagerImpl extends GenericManagerImpl<PrepaidCard, String> implements PrepaidCardManager {

  @Autowired
  private PrepaidCardGroupRepository prepaidCardGroupRepository;

  private PrepaidCardRepository prepaidCardRepository;

  @Autowired
  public void setPrepaidCardRepository(PrepaidCardRepository prepaidCardRepository) {
    super.repository = prepaidCardRepository;
    this.prepaidCardRepository = prepaidCardRepository;
  }

  @Override
  public PrepaidCardListVO getList(PrepaidCardListVO listVO) {
    return prepaidCardRepository.getList(listVO);
  }

  @Override
  public void create(String cardNumber, String cardGroupName) {
    PrepaidCard prepaidCard = prepaidCardRepository.getByNumber(cardNumber);
    if (prepaidCard == null) {
      prepaidCard = new PrepaidCard();
      prepaidCard.setNumber(cardNumber);
      prepaidCard.setStatus(PrepaidCard.PrepaidCardStatus.NOT_ISSUED);
      String code = RandomStringUtils.randomAlphanumeric(8).toUpperCase();
      prepaidCard.setVerifyCode(code);
    }

    if (StringUtils.isNotBlank(cardGroupName)) {
      PrepaidCardGroup cardGroup = prepaidCardGroupRepository.getByName(cardGroupName);
      prepaidCard.setCardGroup(cardGroup);
    }
    merge(prepaidCard);
  }

  @Override
  public PrepaidCardExcelListVO getExcelList(PrepaidCardExcelListVO listVO) {
    return prepaidCardRepository.getExcelList(listVO);
  }

  @Override
  public PrepaidCard getByNumber(String cardNumber) {
    return prepaidCardRepository.getByNumber(cardNumber);
  }
}
