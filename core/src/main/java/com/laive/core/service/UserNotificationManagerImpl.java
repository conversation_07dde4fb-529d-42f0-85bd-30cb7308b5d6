package com.laive.core.service;

import com.laive.core.domain.User;
import com.laive.core.push.PushTargetVO;
import com.laive.core.repository.UserRepository;
import com.laive.core.vo.UserNotificationListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.laive.core.base.GenericManagerImpl;
import com.laive.core.domain.UserNotification;
import com.laive.core.repository.UserNotificationRepository;

import java.util.Date;
import java.util.List;

@Service("userNotificationManager")
public class UserNotificationManagerImpl extends GenericManagerImpl<UserNotification, UserNotification.Id> implements
    UserNotificationManager {

  @Autowired
  private UserRepository userRepository;

  private UserNotificationRepository userNotificationRepository;

  @Autowired
  public void setUserNotificationRepository(UserNotificationRepository userNotificationRepository) {
    super.repository = userNotificationRepository;
    this.userNotificationRepository = userNotificationRepository;
  }

  @Override
  public UserNotificationListVO getListVO(UserNotificationListVO listVO) {
    return userNotificationRepository.getListVO(listVO);
  }

  @Override
  public long getNotReadCount(String userId) {
    return userNotificationRepository.getNotReadCount(userId);
  }

  @Override
  public void readAll(String userId) {
    userNotificationRepository.readAll(userId);
  }

  @Override
  public void create(String title, String content, List<PushTargetVO> pushTargetVOs) {

    Date sendDt = new Date();
    for (PushTargetVO targetVO : pushTargetVOs) {
      User user = userRepository.get(targetVO.getUserId());
      UserNotification userNotification = new UserNotification(user, sendDt, title, content);
      merge(userNotification);
    }
  }
}
