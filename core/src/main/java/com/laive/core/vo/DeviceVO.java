package com.laive.core.vo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import com.laive.core.base.BaseObject;
import com.mysema.query.annotations.QueryProjection;

@Getter
@Setter
@NoArgsConstructor
public class DeviceVO extends BaseObject {

  private static final long serialVersionUID = 4860629052308431852L;

  private String deviceId;

  private Integer slot;

  private String deviceType;

  private String deviceModel;

  @QueryProjection
  public DeviceVO(String deviceId, Integer slot, String deviceType, String deviceModel) {
    this.deviceId = deviceId;
    this.slot = slot;
    this.deviceType = deviceType;
    this.deviceModel = deviceModel;
  }
}
