package com.laive.core.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laive.core.base.BaseObject;
import com.laive.core.base.PaginatedListVO;
import com.laive.core.domain.BillingMethod;
import com.laive.core.domain.BillingType;
import com.laive.core.domain.PaymentAdjustType;
import com.laive.core.domain.PaymentType;
import com.laive.core.domain.User;
import com.laive.core.util.DateUtil;
import com.laive.core.util.StringUtils;
import com.mysema.query.annotations.QueryProjection;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
public class UserPaymentListVO extends PaginatedListVO<UserPaymentListVO.Data> {

  private static final long serialVersionUID = 1941394605838026407L;

  public enum SortColumn {
    BUY_DT
  }

  @JsonIgnore
  private String timezone;

  @JsonIgnore
  private UserPaymentListVO.Search search = new UserPaymentListVO.Search();

  @JsonProperty
  private PayUser user;

  @Getter
  @Setter
  public static class Search extends BaseObject {

    private static final long serialVersionUID = -1218252963141105532L;

    private String subId;

    private String paymentType;

    private Date searchBuyDtFrom;

    private Date searchBuyDtTo;

    // api only
    private boolean activeOnly;
    private String deviceId;

    //

    public void adjustTimezone(String timezone) {
      if (searchBuyDtFrom == null)
        searchBuyDtFrom = DateUtil.addDays(new Date(), -7);
      searchBuyDtFrom = DateUtil.adjustAndTrucate(searchBuyDtFrom, timezone);
      searchBuyDtTo = DateUtil.adjustAndTrucate(searchBuyDtTo, timezone);
    }

  }

  @Getter
  @Setter
  @NoArgsConstructor
  public static class PayUser extends BaseObject {

    private static final long serialVersionUID = -1365471392443263595L;

    private String userId;

    private String userName;

    private String subId;

    private String email;

    private String mobile;

    public static PayUser from(User user) {
      PayUser payUser = new PayUser();
      payUser.setUserId(user.getId());
      payUser.setUserName(user.getName());
      payUser.setSubId(user.getSubId());
      payUser.setMobile(user.getMobile());
      payUser.setEmail(user.getEmail());
      return payUser;
    }
  }

  @Getter
  @Setter
  @NoArgsConstructor
  public static class Data extends BaseObject {

    private static final long serialVersionUID = 6699460166643605998L;

    private String paymentId;

    private String productName;

    private String paymentType;

    private String paymentTypeLabel;

    private Double originalPrice;

    private Double price;

    private Double adjustPrice;

    @JsonIgnore
    private Date buyDt;

    @JsonIgnore
    private Date expireDt;

    private Double beforeUserBalance;

    private Double afterUserBalance;

    private BillingMethod billingMethod;

    private PaymentAdjustType paymentAdjustType;

    @JsonIgnore
    private String adjustFromPaymentId;

    @JsonIgnore
    private String adjustToPaymentId;

    @JsonProperty("buyDt")
    private String buyDtString;

    @JsonProperty("expireDt")
    private String expireDtString;

    private String billingType;

    @QueryProjection
    public Data(String paymentId, String vodName, String categoryName, String channelPackageName, String paymentType, Double price,
                Double originalPrice, Date buyDt, Date expireDt, Double beforeUserBalance, Double afterUserBalance, BillingMethod billingMethod,
                PaymentAdjustType paymentAdjustType, Double adjustPrice, String adjustFromPaymentId, String adjustToPaymentId
            , BillingType billingType, String timezone) {

      this.paymentId = paymentId;
      if (StringUtils.isNotBlank(vodName)) {
        this.productName = vodName;
      }
      if (StringUtils.isNotBlank(categoryName)) {
        this.productName = categoryName;
      }
      if (StringUtils.isNotBlank(channelPackageName)) {
        this.productName = channelPackageName;
      }
      this.paymentType = paymentType;

      if (PaymentType.CHANNEL_PACKAGE.equals(paymentType)) {
        this.paymentTypeLabel = "CHANNEL_PACKAGE";
      } else if (PaymentType.CATEGORY.equals(paymentType)) {
        this.paymentTypeLabel = "SVOD";
      } else if (PaymentType.VOD.equals(paymentType)) {
        this.paymentTypeLabel = "TVOD";
      }

      this.price = price;
      this.originalPrice = originalPrice;
      this.buyDt = buyDt;
      this.expireDt = expireDt;
      this.beforeUserBalance = beforeUserBalance;

      this.afterUserBalance = afterUserBalance;
      this.billingMethod = billingMethod;

      this.paymentAdjustType = paymentAdjustType;
      this.adjustPrice = adjustPrice;
      this.adjustFromPaymentId = adjustFromPaymentId;
      this.adjustToPaymentId = adjustToPaymentId;

      if (buyDt != null)
        this.buyDtString = DateUtil.getDateTimeString(buyDt, timezone);
      if (expireDt != null)
        this.expireDtString = DateUtil.getDateTimeString(expireDt, timezone);

      this.billingType = billingType == null ? BillingType.PRE.name() : billingType.name();
    }
  }

  @Override
  public Class<?> getDataClass() {
    return UserPaymentListVO.Data.class;
  }
}
