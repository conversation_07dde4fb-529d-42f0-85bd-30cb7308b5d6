package com.laive.core.vo;

import com.laive.core.base.BaseObject;
import com.laive.core.base.PaginatedListVO;
import com.mysema.query.annotations.QueryProjection;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
public class ContractMovieListVO extends PaginatedListVO<ContractMovieListVO.Data> {

  private static final long serialVersionUID = 3875585497394305827L;

  private String platformId;

  private String contractId;

  private String tvodLicenseType;

  private String title;

  private String timezone;

  public ContractMovieListVO() {
  }

  public ContractMovieListVO(String returnCode) {
    super(returnCode);
  }

  @Override
  public Class<?> getDataClass() {
    return ContractMovieListVO.Data.class;
  }


  @Getter
  @Setter
  @ToString
  public static class Data extends BaseObject {

    private String contractId;

    private String movieId;

    private String movieTitle;

    @QueryProjection
    public Data(String contractId, String movieId, String movieTitle) {
      this.contractId = contractId;
      this.movieId = movieId;
      this.movieTitle = movieTitle;

    }
  }
}
