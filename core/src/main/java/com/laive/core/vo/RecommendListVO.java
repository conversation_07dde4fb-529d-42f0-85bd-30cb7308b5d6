package com.laive.core.vo;

import com.laive.core.base.BaseObject;
import com.laive.core.base.PaginatedListVO;
import com.laive.core.domain.Recommend;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RecommendListVO extends PaginatedListVO<Recommend> {

  /**
   *
   */
  private static final long serialVersionUID = -1268855536339407863L;

  private String platformId;

  private RecommendListVO.Search search = new RecommendListVO.Search();

  public RecommendListVO() {
  }

  public RecommendListVO(String returnCode) {
    super(returnCode);
  }

  @Getter
  @Setter
  public static class Search extends BaseObject {

    private static final long serialVersionUID = -7430299015251413275L;

    private String vodName;
  }

  @Override
  public Class<?> getDataClass() {
    return Recommend.class;
  }

}
