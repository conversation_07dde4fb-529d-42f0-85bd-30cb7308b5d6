package com.laive.core.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laive.core.base.BaseObject;
import com.laive.core.base.PaginatedListVO;
import com.laive.core.domain.Promotion;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class PromotionListVO extends PaginatedListVO<Promotion> {

  private static final long serialVersionUID = -3952304686810700191L;

  @JsonIgnore
  private PromotionListVO.Search search = new PromotionListVO.Search();

  @Getter
  @Setter
  public static class Search extends BaseObject {

    private static final long serialVersionUID = -4972698686690266390L;

    private String platformId;

    private String name;
  }

  @Override
  public Class<?> getDataClass() {
    return Promotion.class;
  }

}
