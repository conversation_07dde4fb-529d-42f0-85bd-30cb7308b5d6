package com.laive.core.vo;

import com.laive.core.base.BaseObject;
import com.mysema.query.annotations.QueryProjection;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class StatsVO extends BaseObject {

    private static final long serialVersionUID = 7336564614438181177L;

    private String timezoneId;

    private Date fromDate;

    private Date toDate;

    private List<StatsData> list;

    @Getter
    @Setter
    public static class StatsData extends BaseObject {

        private static final long serialVersionUID = -2916163500280135970L;

        private String key;

        private String label;

        private long value;


        @QueryProjection
        public StatsData(String label, long value) {
            this(label, label, value);
        }

        @QueryProjection
        public StatsData(String key, String label, long value) {
            super();
            this.key = key;
            this.label = label;
            this.value = value;
        }
    }



}
