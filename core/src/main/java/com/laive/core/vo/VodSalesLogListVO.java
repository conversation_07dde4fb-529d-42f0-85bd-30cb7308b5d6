package com.laive.core.vo;

import com.laive.commons.core.AbstractVO;
import com.laive.core.base.PaginatedListVO;
import com.laive.core.domain.VodSalesLog;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class VodSalesLogListVO extends PaginatedListVO<VodSalesLog> {

  private static final long serialVersionUID = 6436369770481340925L;

  private Search search = new Search();

  @Override
  public Class<?> getDataClass() {
    return VodSalesLog.class;
  }


  @Getter
  @Setter
  @ToString
  public static class Search extends AbstractVO {

    private static final long serialVersionUID = 8917994803063321892L;

    private String platformId;
  }


}
