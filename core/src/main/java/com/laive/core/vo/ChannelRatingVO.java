package com.laive.core.vo;

import com.laive.core.base.BaseObject;
import com.mysema.query.annotations.QueryProjection;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
public class ChannelRatingVO extends BaseObject {

    private static final long serialVersionUID = 7126920340622767600L;

    private String channelId;

    private Long watchCount;

    @QueryProjection
    public ChannelRatingVO(String channelId, Long watchCount) {
        super();
        this.channelId = channelId;
        this.watchCount = watchCount;
    }

}
