package com.laive.core.vo;

import com.laive.core.base.PaginatedListVO;
import com.laive.core.domain.calculation.UserCalculation;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserCalculationListVO extends PaginatedListVO<UserCalculation> {

  private static final long serialVersionUID = 3875585497394305827L;

  private String platformId;

  private String title;

  private String timezone;

  public UserCalculationListVO() {}

  public UserCalculationListVO(String returnCode) {
    super(returnCode);
  }

  @Override
  public Class<?> getDataClass() {
    return UserCalculation.class;
  }

}
