package com.laive.core.vo;

import lombok.Getter;
import lombok.Setter;

import com.laive.core.base.PaginatedListVO;
import com.laive.core.domain.Platform;

@Getter
@Setter
public class PlatformListVO extends PaginatedListVO<Platform> {



  /**
   *
   */
  private static final long serialVersionUID = -5856511805418947676L;

  @Override
  public Class<?> getDataClass() {
    return Platform.class;
  }

}
