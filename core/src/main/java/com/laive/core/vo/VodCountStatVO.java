package com.laive.core.vo;

import com.laive.core.base.BaseObject;
import com.laive.core.domain.VodSaleType;
import com.mysema.query.annotations.QueryProjection;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class VodCountStatVO extends BaseObject {

  private static final long serialVersionUID = -947685352906177666L;

  private Date fromDate;

  private Date toDate;

  private List<StatsData> list;

  @Getter
  @Setter
  public static class StatsData extends BaseObject {


    private static final long serialVersionUID = 463597793602261144L;

    private VodSaleType saleType;

    private long count;

    @QueryProjection
    public StatsData(VodSaleType saleType, long count) {
      super();
      this.saleType = saleType;
      this.count = count;
    }
  }

  public long getVodCountByLicenseType(VodSaleType saleType) {
    for (StatsData data : list) {
      if (saleType.equals(data.getSaleType()))
        return data.getCount();
    }
    return 0;
  }

}
