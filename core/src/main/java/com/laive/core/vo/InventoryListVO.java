package com.laive.core.vo;

import com.laive.core.base.PaginatedListVO;
import com.laive.core.domain.Inventory;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class InventoryListVO extends PaginatedListVO<Inventory> {

  private static final long serialVersionUID = 3256512720436779018L;

  private String searchWord;

  private String[] inventoryIds; // excel

  private String platformId;

  @Override
  public Class<?> getDataClass() {
    return Inventory.class;
  }

}
