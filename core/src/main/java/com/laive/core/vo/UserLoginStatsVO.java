package com.laive.core.vo;

import com.laive.commons.core.AbstractVO;
import com.laive.core.annotation.ExcelAutoDetect;
import com.laive.core.annotation.ExcelProperty;
import com.laive.core.base.PaginatedListVO;
import com.mysema.query.annotations.QueryProjection;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.TimeZone;

@Getter
@Setter
public class UserLoginStatsVO extends PaginatedListVO<UserLoginStatsVO.Data> {

  private static final long serialVersionUID = -8566980573332472797L;

  private Search search = new Search();

  @Getter
  @Setter
  @ToString
  public static class Search extends AbstractVO {

    private static final long serialVersionUID = -8476632281533687722L;

    private String timezoneId;

    private String platformId;

    private String deviceType;

    private PeriodType periodType;

    private Date fromDate;
    private Date toDate;
    private String formattedFromDate;
    private String formattedToDate;

    public Search() {
      this.periodType = PeriodType.Hourly;
      this.timezoneId = TimeZone.getDefault().getID();
    }

  }

  @Getter
  @Setter
  @ToString
  @NoArgsConstructor
  @ExcelAutoDetect
  public static class Data extends AbstractVO {

    private static final long serialVersionUID = -2640102487794081496L;

    private String id;

    @ExcelProperty(resourceKey = "Day")
    private String name;

    @ExcelProperty(resourceKey = "User Count")
    private long count;

    @ExcelProperty(resourceKey = "View Count")
    private long userCount;

    public Data(String label, long count, long userCount) {
      this.id = label;
      this.name = label;
      this.count = count;
      this.userCount = userCount;
    }

    @QueryProjection
    public Data(String id, String name, long count, long userCount) {
      this.id = id;
      this.name = name;
      this.count = count;
      this.userCount = userCount;
    }

  }

  @Override
  public Class<?> getDataClass() {
    return UserLoginStatsVO.Data.class;
  }

  public enum PeriodType {
    Hourly, Daily, Monthly, Yearly
  }
}
