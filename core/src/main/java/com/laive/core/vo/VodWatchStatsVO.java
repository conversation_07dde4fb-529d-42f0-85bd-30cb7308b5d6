package com.laive.core.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.laive.commons.core.AbstractVO;
import com.laive.commons.core.util.delete.DateUtils;
import com.laive.commons.core.util.StringUtils;
import com.laive.core.annotation.ExcelAutoDetect;
import com.laive.core.annotation.ExcelProperty;
import com.laive.core.base.PaginatedListVO;
import com.mysema.query.annotations.QueryProjection;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Date;
import java.util.TimeZone;

@Getter
@Setter
public class VodWatchStatsVO extends PaginatedListVO<VodWatchStatsVO.Data> {

  private static final long serialVersionUID = -2563997119153536702L;

  private Search search = new Search();
  

  @Getter
  @Setter
  @ToString
  public static class Search extends AbstractVO {


    private static final long serialVersionUID = -5601098112713012982L;

    private String timezoneId;

    private String platformId;

    private Date fromDate;

    private Date toDate;

    @JsonProperty
    public String getFormattedFromDate() {
      return DateUtils.getDateString(this.fromDate, DateUtils.getDateFormat(LocaleContextHolder.getLocale()));
    }

    @JsonProperty
    public String getFormattedToDate() {
      return DateUtils.getDateString(this.toDate, DateUtils.getDateFormat(LocaleContextHolder.getLocale()));
    }

    public Search() {
      this.timezoneId = TimeZone.getDefault().getID();
    }

  }

  @Getter
  @Setter
  @ToString
  @NoArgsConstructor
  @ExcelAutoDetect
  public static class Data extends AbstractVO {

    private static final long serialVersionUID = 3933651358928798726L;

    private String id;

    @ExcelProperty(resourceKey = "Title")
    private String title;
    
    @ExcelProperty(resourceKey = "Studio")
    private String studio;
    
    @ExcelProperty(resourceKey = "Classification")
    private String classification;
    
    @ExcelProperty(resourceKey = "Version")
    private String version;
    
    @ExcelProperty(resourceKey = "Start Date")
    private Date startDate;
    
    @ExcelProperty(resourceKey = "End Date")
    private Date endDate;

    @ExcelProperty(resourceKey = "Units")
    private long unitCount;

    private double ratio;

    @QueryProjection
    public Data(String id, String title, String studio, String classification, String version, long count, String libraryTitle ) {
      this.id = id;
      this.title = StringUtils.defaultIfBlank(title, libraryTitle);
      this.studio = studio;
      this.classification = classification;
      this.version = version;
      this.unitCount = count;
    }   

  }

  @Override
  public Class<?> getDataClass() {
    return VodWatchStatsVO.Data.class;
  }


}
