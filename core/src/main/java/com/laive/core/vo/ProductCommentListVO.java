package com.laive.core.vo;

import lombok.Getter;
import lombok.Setter;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.laive.core.base.PaginatedListVO;
import com.laive.core.domain.VodComment;

@JsonIgnoreProperties(value = { "categoryId", "platformId", "startsWith", "orderBy", "categoryIds" })
@Getter
@Setter
public class ProductCommentListVO extends PaginatedListVO<VodComment> {

  /**
   *
   */
  private static final long serialVersionUID = 3018095257445219732L;

  private String productId;
  private String timezone;

  @Override
  public Class<?> getDataClass() {
    return VodComment.class;
  }

}
