package com.laive.core.vo;

import com.laive.core.domain.Task;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import com.laive.core.base.BaseObject;
import com.laive.core.base.PaginatedListVO;
import com.laive.core.domain.BoxModel;

@Getter
@Setter
@NoArgsConstructor
public class TaskListVO extends PaginatedListVO<Task> {


  private static final long serialVersionUID = -9151454428160559956L;
  private TaskListVO.Search search = new TaskListVO.Search();

  @Getter
  @Setter
  public static class Search extends BaseObject {


    private static final long serialVersionUID = -7903666015112860466L;
    private String searchword;
    private String agentId;
    private String job;
    private Boolean solved;

  }

  @Override
  public Class<?> getDataClass() {
    return Task.class;
  }

}
