package com.laive.core.vo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;

import com.laive.core.base.PaginatedListVO;

@Getter
@Setter
@NoArgsConstructor
public class FavoriteCatchupListVO extends PaginatedListVO<ScheduleByPassVO> {

  private static final long serialVersionUID = -1121481320005111459L;
  @NonNull
  private String userId;

  private String timezone;

  @Override
  public Class<?> getDataClass() {
    return ScheduleByPassVO.class;
  }

}
