package com.laive.core.util;

import java.util.Arrays;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;

public class CollectionUtils {

    public static boolean isEmpty(Collection<?> collection) {
        return org.springframework.util.CollectionUtils.isEmpty(collection);
    }

    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    public static boolean containsAny(Collection<?> collection, Collection<?> collection2) {
        return org.apache.commons.collections.CollectionUtils.containsAny(collection, collection2);
    }

    public static Object intersection(List<String> list1, List<String> list2) {
        return org.apache.commons.collections.CollectionUtils.intersection(list1, list2);
    }

    public static Object union(Collection<?> collection1, Collection<?> collection2) {
        return org.apache.commons.collections.CollectionUtils.union(collection1, collection2);
    }

    public static List<?> removeDuplicates(List<?> list) {
        LinkedHashSet<Object> set = new LinkedHashSet<Object>(list);
        List<?> resultList = Arrays.asList(set.toArray());
        return resultList;
    }

}
