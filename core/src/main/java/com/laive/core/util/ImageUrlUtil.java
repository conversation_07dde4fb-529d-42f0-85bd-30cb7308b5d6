package com.laive.core.util;

import lombok.extern.apachecommons.CommonsLog;

import com.laive.core.base.BaseEntity;
import com.laive.core.config.SystemPropertiesConfig;
import com.laive.core.domain.BannerImage;
import com.laive.core.domain.Library;
import com.laive.core.domain.Vod;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ImageUrlUtil {

  public static String getProductPosterImageUrl(String productId, String productFilename, String libraryId, String libraryFilename) {

    if (StringUtils.isNotBlank(productFilename)) {
      String imageBaseUrl = getBaseImageUrl(Vod.class);
      return String.format("%s/%s/%s", imageBaseUrl, productId, productFilename);
    } else if (StringUtils.isNotBlank(libraryFilename)) {
      String imageBaseUrl = getBaseImageUrl(Library.class);
      return String.format("%s/%s/%s", imageBaseUrl, libraryId, libraryFilename);
    } else {
      return String.format("%s/default.png", ImageUrlUtil.getBaseImageUrl(BannerImage.class));
    }
  }

  public static String getProductPosterImageUrl(String productId, String productFilename) {
    return getProductPosterImageUrl(productId, productFilename, null, null);
  }

  public static String getLibraryPosterImageUrl(String libraryId, String libraryFilename) {
    String imageBaseUrl = getBaseImageUrl(Library.class);
    return String.format("%s/%s/%s", imageBaseUrl, libraryId, libraryFilename);
  }

  public static String getLibrarySnapshotUrl(String libraryId) {
    String imageBaseUrl = getBaseImageUrl(Library.class);
    return String.format("%s/%s/%s.jpg", imageBaseUrl, libraryId, libraryId);
  }

  public static String getLibrarySnapshotUrl(String libraryId, int number) {
    String imageBaseUrl = getBaseImageUrl(Library.class);
    return String.format("%s/%s/%s_%d.jpg", imageBaseUrl, libraryId, libraryId, number);
  }

  public static String getBaseImageUrl(Class<? extends BaseEntity<?>> clazz) {
    return String.format("%s/%s", System.getProperty(SystemPropertiesConfig.UPLOAD_URL), StringUtils.lowerCase(clazz.getSimpleName()));
  }

}
