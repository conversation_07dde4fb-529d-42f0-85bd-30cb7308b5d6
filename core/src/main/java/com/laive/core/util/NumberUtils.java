package com.laive.core.util;

import java.math.BigDecimal;
import java.math.BigInteger;

public class NumberUtils {

  public static double round(double value, int places) {
    if (places < 0)
      throw new IllegalArgumentException();

    long factor = (long) Math.pow(10, places);
    value = value * factor;
    long tmp = Math.round(value);
    return (double) tmp / factor;
  }

  public static long toLong(Object param) {
    if (param == null)
      return 0l;
    if (param instanceof BigInteger)
      return ((BigInteger) param).longValue();
    else if (param instanceof BigDecimal)
      return ((BigDecimal) param).longValue();
    else if (param instanceof String) {
      return org.apache.commons.lang3.math.NumberUtils.toLong((String) param);
    } else
      return (Long) param;
  }

  public static double toDouble(String str) {
    return org.apache.commons.lang3.math.NumberUtils.toDouble(str);
  }


  public static double toDouble(Object param) {
    if (param == null) {
      return 0;
    }
    if (param instanceof BigInteger) {
      return ((BigInteger) param).doubleValue();
    } else if (param instanceof BigDecimal) {
      return ((BigDecimal) param).doubleValue();
    } else if (param instanceof Long) {
      return ((Double) param).doubleValue();
    } else if (param instanceof String) {
      return Double.parseDouble((String) param);
    } else {
      return (Double) param;
    }
  }

  public static int toInt(String str) {
    return org.apache.commons.lang3.math.NumberUtils.toInt(str);
  }

  public static int toInt(Object param) {
    if (param == null)
      return 0;
    if (param instanceof BigInteger)
      return ((BigInteger) param).intValue();
    else if (param instanceof BigDecimal)
      return ((BigDecimal) param).intValue();
    else if (param instanceof String) {
      return org.apache.commons.lang3.math.NumberUtils.toInt((String) param);
    } else
      return (Integer) param;
  }

  public static int toInt(String str, int defaultValue) {
    return org.apache.commons.lang3.math.NumberUtils.toInt(str, defaultValue);
  }

  public static boolean isNumber(String str) {
    return org.apache.commons.lang3.math.NumberUtils.isNumber(str);
  }

}
