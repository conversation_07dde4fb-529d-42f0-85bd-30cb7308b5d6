package com.laive.core.util;

import org.apache.commons.lang3.CharUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.dao.SaltSource;
import org.springframework.security.authentication.encoding.ShaPasswordEncoder;
import org.springframework.stereotype.Component;

@Component
public class PasswordUtil {

    private ShaPasswordEncoder passwordEncoder;

    @Autowired
    public void setPasswordEncoder(ShaPasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
    }

    private static SaltSource saltSource = null; // 추후 고객요구사항 있을시 salt 이용한 보안처리

    private PasswordUtil() {}

    public String getTempPassword() {
        char[] charSet = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
                'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z' };

        int idx = 0;
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < 10; i++) {
            idx = (int)(charSet.length * Math.random());
            sb.append(charSet[idx]);
        }

        return sb.toString();
    }

    public String encodePassword(String rawPassword) {
        return passwordEncoder.encodePassword(rawPassword, saltSource);
    }

    public boolean isMatchedPassword(String paramPassword, String originalPassword) {

        boolean result = false;
        if (passwordEncoder.isPasswordValid(originalPassword, paramPassword, saltSource)) {
            result = true;
        }
        return result;
    }

    public boolean isValidPassword(String password, int charDuplicateLimitLength) {
        return isPasswordSequenceLimitCheck(password, charDuplicateLimitLength)
                && isPasswordDuplicateLimitCheck(password, charDuplicateLimitLength);
    }

    private boolean isPasswordSequenceLimitCheck(String password, int charDuplicateLimitLength) {
        int count = 1;
        int length = password.length();
        int duplicateLimitLength = charDuplicateLimitLength - 1;
        char charPassword;
        char nextCharPassword;
        char nextChar;

        for (int i = 0; i < length; i++) {
            charPassword = password.charAt(i);

            try {
                CharUtils.toIntValue(charPassword);
            } catch (IllegalArgumentException e) {
                continue;
            }

            if ((i + 1) == length) {
                break;
            } else {
                nextCharPassword = password.charAt(i + 1);
                nextChar = (char)(charPassword + 1);
            }

            if (nextChar == nextCharPassword)
                count = count + 1;
            else
                count = 1;

            if (count > duplicateLimitLength)
                break;
        }

        return count <= duplicateLimitLength;
    }

    private boolean isPasswordDuplicateLimitCheck(String password, int charDuplicateLimitLength) {
        char c;
        int cnt = 0;
        int duplicateLimitLength = charDuplicateLimitLength - 1;
        boolean isBreak = false;

        for (int i = 0; i < password.length() - 1; i++) {
            cnt = 0;
            c = password.charAt(i);

            try {
                CharUtils.toIntValue(c);
            } catch (IllegalArgumentException e) {
                continue;
            }

            for (int j = i + 1; j < password.length(); j++) {
                if (c == password.charAt(j)) {
                    cnt++;
                } else {
                    break;
                }
                if (cnt >= duplicateLimitLength) {
                    isBreak = true;
                    break;
                }
            }

            if (isBreak)
                break;
        }

        return cnt < duplicateLimitLength;
    }
}
