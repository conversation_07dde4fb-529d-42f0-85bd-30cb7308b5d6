package com.laive.core.web.listener;

import java.util.List;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import com.laive.core.base.BaseConstants;
import com.laive.core.constants.Constants;
import com.laive.core.service.LookupManager;
import com.laive.core.util.DateUtil;
import com.laive.core.util.ServicePropertiesUtil;

public class StartupListener implements ServletContextListener {

  private final Log log = LogFactory.getLog(StartupListener.class);

  @Override
  public void contextInitialized(ServletContextEvent event) {
    setupContext(event.getServletContext());
  }

  private void setupContext(ServletContext servletContext) {

    setupDateFormat(servletContext);

    ApplicationContext ctx = WebApplicationContextUtils.getRequiredWebApplicationContext(servletContext);

    LookupManager lookupManager = (LookupManager)ctx.getBean("lookupManager");
    List<String> supportedLanguages = lookupManager.getSupportedLanguages();
    servletContext.setAttribute(BaseConstants.SUPPORTED_LANGUAGES, supportedLanguages);

    ServicePropertiesUtil servicePropertiesUtil = (ServicePropertiesUtil)ctx.getBean("servicePropertiesUtil");
    setupBuildTimestamp(servletContext, servicePropertiesUtil);
  }

  private void setupDateFormat(ServletContext servletContext) {
    servletContext.setAttribute(Constants.DATE_PATTERN, DateUtil.getDatePattern());
    servletContext.setAttribute(Constants.DATETIME_PATTERN, DateUtil.getDateTimePattern());
    log.debug("datePattern:" + DateUtil.getDatePattern());
  }

  private void setupBuildTimestamp(ServletContext servletContext, ServicePropertiesUtil servicePropertiesUtil) {
    servletContext.setAttribute(Constants.PROJECT_BUILD_TIMESTAMP, servicePropertiesUtil.getProjectBuildtimestamp());
  }

  @Override
  public void contextDestroyed(ServletContextEvent sce) {

  }
}
