package com.laive.core.web.util;

import com.laive.core.base.BaseObject;
import com.laive.core.util.StringUtils;

public class PageHistoryVO extends BaseObject {

    private static final long serialVersionUID = -331964770374109951L;

    private String uri;

    private String query;

    public PageHistoryVO() {

    }

    public PageHistoryVO(String uri, String query) {
        this.uri = uri;
        this.query = query;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getUrl() {
        String url = this.uri;
        if (StringUtils.isNotBlank(this.query))
            url += "?" + this.query;
        return url;
    }
}
