/* ================================ DDL ================================ */
drop table mt_user_card;

CREATE TABLE `mt_user_card` (
  `id` varchar(50) NOT NULL,
  `created_dt` datetime DEFAULT NULL,
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  `updated_dt` datetime DEFAULT NULL,
  `expiration` varchar(30) DEFAULT NULL,
  `number` varchar(100) DEFAULT NULL,
  `payment_token` varchar(100) DEFAULT NULL,
  `user_id` varchar(50) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_q01v9mxoh1ujgrwp0eeffr3pm` (`number`),
  UNIQUE KEY `UK_3jeur2wasdtvsm7tosk2evtcu` (`payment_token`),
  KEY `FKjvyqer4r5dow6w4jhougg5hwp` (`user_id`),
  CONSTRAINT `FKjvyqer4r5dow6w4jhougg5hwp` FOREIGN KEY (`user_id`) REFERENCES `mt_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/* ===================================================================== */

/* ================================ DML ================================ */
