<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.giitd.tvstorm</groupId>
    <artifactId>deploy-all</artifactId>
    <version>1.0</version>
    <relativePath>deploy-all.xml</relativePath>
  </parent>
  <artifactId>deploy-api</artifactId>
  <packaging>pom</packaging>
  <name>deploy-api</name>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.cargo</groupId>
        <artifactId>cargo-maven2-plugin</artifactId>
        <version>1.1.1</version>
        <configuration>
          <container>
            <containerId>${cargo.remote.containerId}</containerId>
            <type>remote</type>
          </container>
          <configuration>
            <type>runtime</type>
            <properties>
              <cargo.remote.uri>${cargo.remote.uri}</cargo.remote.uri>
              <cargo.remote.username>${cargo.remote.username}</cargo.remote.username>
              <cargo.remote.password>${cargo.remote.password}</cargo.remote.password>
            </properties>
          </configuration>
          <deployer>
            <type>remote</type>
            <deployables>
              <deployable>
                <groupId>com.giitd.tvstorm</groupId>
                <artifactId>api</artifactId>
                <type>war</type>
                <properties>
                  <context>/api</context>
                </properties>
              </deployable>
            </deployables>
          </deployer>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>com.giitd.tvstorm</groupId>
      <artifactId>api</artifactId>
	  <version>${deploy.version}</version>
      <type>war</type>
    </dependency>
  </dependencies>
  <properties>
    <!-- cargo deploy setting -->
    <cargo.remote.uri>${cargo.remote.uri.api}</cargo.remote.uri>
  </properties>

</project>