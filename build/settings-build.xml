<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <pluginGroups>
  </pluginGroups>
  <proxies>
  </proxies>
  <servers>
    <server>
      <id>snapshots</id>
      <username>deployment</username>
      <password>deployment123</password>
    </server>
    <server>
      <id>release</id>
      <username>deployment</username>
      <password>deployment123</password>
    </server>
  </servers>
  <mirrors>
  </mirrors>
  <profiles>
    <profile>
      <id>build</id>
      <properties>
        <target-env>production</target-env>
        <target-db>mysql</target-db>
        <db.name>tvstorm_build</db.name>
        <db.address>localhost</db.address>
        <db.username>root</db.username>
        <db.password>nuvus!@#$</db.password>
        <project.rootdir>${jenkins.workspace}</project.rootdir>

        <!-- cargo deploy -->
        <cargo.remote.containerId>tomcat7x</cargo.remote.containerId>
        <cargo.remote.username>admin</cargo.remote.username>
        <cargo.remote.password>nuvus!@#$</cargo.remote.password>
        <cargo.remote.uri.sms>http://***********:8080/manager/text</cargo.remote.uri.sms>
        <cargo.remote.uri.cms>http://***********:8081/manager/text</cargo.remote.uri.cms>
        <cargo.remote.uri.api>http://***********/manager/text</cargo.remote.uri.api>
      </properties>
    </profile>
  </profiles>
  <activeProfiles>
    <activeProfile>build</activeProfile>
  </activeProfiles>
</settings>

