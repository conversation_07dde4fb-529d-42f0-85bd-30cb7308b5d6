package com.laive.cms.schedule.sdf;

import com.laive.cms.base.BaseManagerTestCase;
import com.laive.core.domain.Channel;
import com.laive.core.service.ChannelManager;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;


@Ignore
public class ScheduleFileCreatorTest extends BaseManagerTestCase {

  @Autowired
  private ScheduleFileCreator creator;

  @Autowired
  private ChannelManager channelManager;

  @Test
  public void create() {
    String channelId = "4028c6856fd0d66d017032d0246a7f90";
    Channel channel = channelManager.get(channelId);
    String scheduleDt = "20200701000000"; // yyyyMMddHHmmss
    creator.create(channel, new Date().getTime(), scheduleDt);
    flushAndClear();

  }
}