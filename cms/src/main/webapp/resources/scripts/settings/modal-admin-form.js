$(function(){
  $('#modal-admin-form').on('show.bs.modal', function() {
  });

  $('#modal-admin-form').on('hidden.bs.modal', function(e) {
    document.location.reload();
  });

  var options = {
    url : '/account/save',
    type : 'post',
    dataType : 'json',
    clearForm : true,
    beforeSubmit : beforeSubmitForm,
    success : function(data) {
      if (data.resultCode == '100') {
        alert('Saved');
        $('#modal-admin-form').modal('hide');
      } else {
        alert('Save Fails..:' + data.message);
      }
    }
  };

  $('#adminForm').ajaxForm(options);


  $('#adminForm').validate({
    rules : {
      username : {
        required : true
      },
      fullName : {
        required : true
      },
      password : {
        required : true
      },
      'platform.id' : {
        required : true
      },
      'role.id' : {
        required : true
      }
    }
  });

  function beforeSubmitForm() {
    var password = $(':input[name="password"]').val();
    var confirmPassword = $(':input[name="confirmPassword"]').val();
    if(confirmPassword) {
      if (password != confirmPassword) {
        alert('Your password and confirmation password do not match.');
        return false;
      }
    }
    return $('#adminForm').valid();
  }
});




