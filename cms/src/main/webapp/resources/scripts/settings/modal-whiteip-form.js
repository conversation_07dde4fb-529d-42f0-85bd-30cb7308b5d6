var whiteIpForm = (function($, win) {
  'use strict';
  $('#modal-whiteip-form').on('show.bs.modal', function() {
  });

  $('#modal-whiteip-form').on('hidden.bs.modal', function(e) {
    document.location.reload();
  });

  $('#whiteIpForm').validate({
    rules: {
      id: {
        required: true
      },
      name: {
        required: true
      }
    }
  });

  var options = {
    url: 'whiteip-save',
    type: 'post',
    dataType: 'json',
    clearForm: true,
    beforeSubmit: beforeSubmitForm,
    success: function(data) {
      if (data.resultCode == '100') {
        alert('Saved');
        $('#modal-whiteip-form').modal('hide');
        document.location.href = 'whiteip';
      } else if (data.resultCode == '997') {
        alert(data.debugMessage);
        return false;
      } else {
        alert('Save Fails..:' + data.message);
        return false;
      }

    },
    errorClass: 'form-validation-error',
    highlight: function(element, errorClass, validClass) {
      $(element).parent().addClass('has-error');
    },
    unhighlight: function(element, errorClass, validClass) {
      $(element).parent().removeClass('has-error');
    }
  };

  $('#whiteIpForm').ajaxForm(options);
}(jQuery, this));

function beforeSubmitForm() {
  return $('#whiteIpForm').valid();

}
