var platformForm = (function($, win) {
  'use strict';
  $('#modal-platform-form').on('show.bs.modal', function() {
  });

  $('#modal-platform-form').on('hidden.bs.modal', function(e) {
    document.location.reload();
  });

  $('#platformForm').validate({
    rules : {
      name : {
        required : true
      }
    }
  });

  var options = {
    url : '/platform/save',
    type : 'post',
    dataType : 'json',
    clearForm : true,
    beforeSubmit : beforeSubmitForm,
    success : function(data) {
      if (data.resultCode == '100') {
        alert('Saved');
        $('#modal-platform-form').modal('hide');
        document.location.href = '/platform/list';
      } else {
        alert('Save Fails..:' + data.message);
      }

    },
    errorClass : 'form-validation-error',
    highlight : function(element, errorClass, validClass) {
      $(element).parent().addClass('has-error');
    },
    unhighlight : function(element, errorClass, validClass) {
      $(element).parent().removeClass('has-error');
    }
  };

  $('#platformForm').ajaxForm(options);
}(jQ<PERSON>y, this));

function beforeSubmitForm() {
  return $('#platformForm').valid();

}
