
var pageSize = 100;
$(function() {

  $('#library-list-scroll-div')
      .paged_scroll(
          {
            handleScroll: function(page, container, doneCallback) {
              console.log('DIV scrolled to page:', page);
              $.ajax({
                url: '/library/getLibrariesByLibraryGroup',
                type: 'GET',
                data: {
                  page: page,
                  pageSize: pageSize,
                  'search.libraryGroupId': $('#library-group-select').val(),
                  'search.libraryName': $('#searchLibraryName').val(),
                  'search.libraryLicenseType': $('#library-license-type-select').val()
                },
                dataType: 'json'
              }).done(function(data) {
                console.log('start scroll draw:', data.list.length);
                if (data.list.length > 0) {
                  drawLibrary(data);
                } else {
                  $('.paged-scroll-loading').hide();
                }
              });

            },
            startPage: 1,
            triggerFromBottom: '10%',
            debug: false,
            checkScrollChange: 1000,
            targetElement: $('#library-list-group'),
            loading: {
              html: '<div class="paged-scroll-loading"><img width="60" height="60" alt="Loading..."' +
              ' src="/resources/images/loading/loading-bar2.gif" /></div>'
            }
          });

  getLibrarysByLibraryGroup('', '', '');
  $('#library-group-select, #library-license-type-select').on('change', function() {
    initTooltip();
    var libraryGroupId = $('#library-group-select').val();
    var searchLibraryName = $('#searchLibraryName').val();
    var libraryLicenseType = $('#library-license-type-select').val();
    getLibrarysByLibraryGroup(libraryGroupId, searchLibraryName, libraryLicenseType);
  });

  $('#searchLibraryName').keypress(function(event) {
    var keycode = (event.keyCode ? event.keyCode : event.which);
    if (keycode == '13') {
      var searchLibraryName = $(this).val();
      initTooltip();
      var libraryGroupId = $('#library-group-select').val();
      var libraryLicenseType = $('#library-license-type-select').val();
      getLibrarysByLibraryGroup(libraryGroupId, searchLibraryName, libraryLicenseType);
      event.preventDefault();
      return false;
    }
  });

  $('#library-name-search-btn').on('click', function() {
    initTooltip();
    var libraryGroupId = $('#library-group-select').val();
    var searchLibraryName = $('#searchLibraryName').val();
    var libraryLicenseType = $('#library-license-type-select').val();
    getLibrarysByLibraryGroup(libraryGroupId, searchLibraryName, libraryLicenseType);
  });

  $('#library-list-group').on('click', 'a', function(e) {
    $('#library-list-group a').each(function(e1) {
      if (!e.ctrlKey)
        $(this).removeClass('active');
    });
    $(this).addClass('active');
  });

  $('#library-assign-btn').on('click', function() {
    $('#library-list-group a').each(function() {
      if ($(this).hasClass('active')) {
        var libraryId = $(this).attr('id');
        if (checkPossibleToAssign(libraryId)) {
          createVodLibraryTableRow(libraryId);
        }
      }
    });
  });

  $('#library-list-group').on('dblclick', 'a', function() {
    if ($(this).hasClass('active')) {
      var libraryId = $(this).attr('id');
      if (checkPossibleToAssign(libraryId)) {
        createVodLibraryTableRow(libraryId);
      }
    }
  });

  $('#library-list-group').tooltip({
    selector: '[data-toggle=tooltip]',
    container: 'body'
  });

  var requestUri = window.location.pathname;
  if (requestUri.indexOf('vod/list') != -1) {
    $('#library-assign-btn').hide();
  } else {
    $('#library-assign-btn').show();
  }

  $('#librarySearchDivDisplayBtn').on('click', function() {
    toggleLibrarySearchDivDisplay($(this).find('i'));
  });

  function toggleLibrarySearchDivDisplay(iTag) {
    if (iTag.hasClass('fa-minus-circle')) {
      iTag.removeClass('fa-minus-circle');
      iTag.addClass('fa-plus-circle');
      $('#librarySearchDiv').hide(300, function() {
      });

    } else {
      iTag.removeClass('fa-plus-circle');
      iTag.addClass('fa-minus-circle');
      $('#librarySearchDiv').show(300, function() {
      });
    }
  }

});

function initTooltip() {
  $('.tooltip').hide();
}

function checkPossibleToAssign(libraryId) {

  var library = libraryMap[libraryId];
  if (library.formattedDuration == undefined) {
    alert('Library is not encoded yet..');
    return false;
  }

  var result = true;
  $('#assigned-library-table tbody tr').each(function() {
    var id = $(this).find('td:eq(0) img').attr('id');
    if (libraryId == id) {
      alert('Library Already Assigned.');
      result = false;
      return result;
    }
  });


  if ($('#vod-type-select').val() == 'SINGLE') {
    if ($('#assigned-library-table tbody tr').length > 0) {
      alert('Single Type Vod Can be Assgined Only 1 Library.');
      result = false;
      return result;
    }

    // 1-library- > 1-vod 규칙제거요청.
    if (library.composedVods.length > 0) {
      if (confirm('Library you select has already composed of other product! Create Another Product ?'))
        result = true;
      else
        result = false;
      return result;
    }
  }

  return result;
}

function createVodLibraryTableRow(libraryId) {

  var library = libraryMap[libraryId];
  var libraryHasVod = (library.composedVods) && (library.composedVods.length > 0);

  var tbody = $('#assigned-library-table-row tbody');
  var appendRow = tbody.html();
  appendRow = appendRow.replace(/%libraryId%/g, libraryId);
  appendRow = appendRow.replace(/%libraryName%/g, library.name);
  appendRow = appendRow.replace(/%libraryFormattedDuration%/g, library.formattedDuration);
  appendRow = appendRow.replace(/%libraryThumbnail%/g, library.posterLandscapeUrl);
  appendRow = appendRow.replace(/%hasVod%/g, libraryHasVod ? 'p' : '');
  appendRow = appendRow.replace(/%episodeName%/g, library.episodeName ? library.episodeName : '');
  appendRow = appendRow.replace(/%episodeNumber%/g, library.episodeNumber ? library.episodeNumber : '');
  appendRow = appendRow.replace(/%episodeNumberTag%/g, library.episodeNumber ? '<span class="badge"' +
    ' style="background-color: #b94a48">' + library.episodeNumber +'</span>' : '');


  if (libraryHasVod && ($('#vod-type-select').val() == 'PACKAGE' || $('#vod-type-select').val() == 'UNION')) {

    appendRow = appendRow.replace(/%vodId%/g, library.composedVods[0].productId);
    var typeLabelClass = (library.type == 'SINGLE') ? 'label-warning' : 'label-primary';
    appendRow = appendRow.replace(/%typeLabelClass%/g, typeLabelClass);
    appendRow = appendRow.replace(/%vodType%/g, library.composedVods[0].type);

    appendRow = appendRow.replace(/%saleTypeLabelClass%/g, '');
    appendRow = appendRow.replace(/%vodSaleType%/g, '');
    appendRow = appendRow.replace(/%vodPrice%/g, '');

  } else {
    appendRow = appendRow.replace(/%vodId%/g, '');
    appendRow = appendRow.replace(/%typeLabelClass%/g, '');
    appendRow = appendRow.replace(/%vodType%/g, '');

    appendRow = appendRow.replace(/%saleTypeLabelClass%/g, '');
    appendRow = appendRow.replace(/%vodSaleType%/g, '');
    appendRow = appendRow.replace(/%vodPrice%/g, '');
  }
  appendRow = appendRow.replace(/%index%/g, $('#assigned-library-table tbody tr').length);

  $('#assigned-library-table tbody').append(appendRow);

  if ($('#vod-type-select').val() == 'PACKAGE' || $('#vod-type-select').val() == 'UNION') {
    var invisibleProductTag = $('#invisible-product-btn-template').html();
    if (!libraryHasVod) {
      invisibleProductTag = invisibleProductTag.replace(/%productId%/g, '');
      invisibleProductTag = invisibleProductTag.replace(/%btnLabel%/g, 'create');
      $('#assigned-library-table tbody').find('#invisibleProductTag-position-' + libraryId).html(invisibleProductTag);
    } else {
      invisibleProductTag = invisibleProductTag.replace(/%productId%/g, library.composedVods[0].productId);
      invisibleProductTag = invisibleProductTag.replace(/%btnLabel%/g, 'edit');
      $('#assigned-library-table tbody').find('#invisibleProductTag-position-' + libraryId).html(invisibleProductTag);
    }
  }

}

var libraryMap = {};
function getLibrarysByLibraryGroup(libraryGroupId, searchLibraryName, libraryLicenseType){
  $
      .getJSON(
          '/library/getLibrariesByLibraryGroup?page=1&pageSize=' + pageSize + '&search.libraryGroupId=' + libraryGroupId + '&search.libraryName=' +
              searchLibraryName)
      .done(
          function(data) {
            $('#library-list-group').html('');
            drawLibrary(data);
          });
}

function drawLibrary(data) {
  var appendText = '';
  'use strict';
  $
      .each(
          data.list,
          function(i, item) {
            console.log('draw item:', item.name);
            appendText += '<a style="cursor:pointer;" class="font12 list-group-item" data-toggle="tooltip" data-placement="bottom" title="" data-original-title="' +
                item.name + '" id="' + item.id + '">';
            appendText += resizeTextWithLimit(item.name, 18);

            var composedVods = [];
            if (item.composedVods.length > 0) {
              $.each(item.composedVods, function(i, item) {
                var platformId = item.platform.id;
                if (sessionPlatformId == platformId)
                  composedVods.push(item);
              });
            }

            appendText += '</button>';

            var library = {};
            library.id = item.id;
            library.posterLandscapeUrl = item.posterLandscapeUrl;
            library.name = item.name;
            library.formattedDuration = item.formattedDuration;
            library.composedVods =  composedVods;
            library.episodeName = item.episodeName;
            library.episodeNumber = item.episodeNumber;
            libraryMap[item.id] = library;
            // $('#library-list-group').append(appendText);
          });
  $('#library-list-group').append(appendText);
}
