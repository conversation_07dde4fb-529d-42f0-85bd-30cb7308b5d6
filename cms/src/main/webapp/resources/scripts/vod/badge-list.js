$(function() {
  $('a.create-form').on('click', function(e) {
    event.preventDefault();
    $.getJSON('form', function(data) {
    });
    $('#modal-badge-form').modal('show');
  });

  $('#checkAll').on('click', function() {
    $('.checkbox').prop('checked', this.checked);
  });

  $('a.badge-id').on('click', function(event) {
    event.preventDefault();
    var badgeId = $(this).data('badgeid');
    $.getJSON('get/' + badgeId, function(badge) {
      $('#badgeForm input[name="id"]').val(badge.id);
      $('#badgeForm select[name="type"]').val(badge.type);
      $('#badgeForm input[name="image"]').val(badge.image);
      $('#badgeForm #badge-image').attr('src', badge.imageUrl);
      console.log('badge enabled:', badge.enabled == true);
      $('#badgeForm input[name="enabled"]').val(badge.enabled);
      $('#badgeForm input[name="enabled-checkbox"]').prop('checked', badge.enabled == true);

    });
    $('#modal-badge-form').modal('show');
  });

  $('#badgeForm input[name="enabled-checkbox"]').on('click', function() {
    console.log('clicked');
    $('#badgeForm input[name="enabled"]').val($(this).is(':checked'));
  });

  $('a.delete').on('click', function() {
    if ($('.checkbox').is(':checked')) {
      if (!confirm('Delete badges?')) {
        return;
      }
      $.ajax({
        url: 'delete',
        traditional: true,
        type: 'POST',
        data: $('#listVO').serialize(),
        dataType: 'json'
      }).done(function(data) {
        if (data.resultCode != '100') {
          alert(data.message);
          return;
        }
        document.location.reload();
      });
    } else {
      alert('Select badges !');
    }
  });

});
