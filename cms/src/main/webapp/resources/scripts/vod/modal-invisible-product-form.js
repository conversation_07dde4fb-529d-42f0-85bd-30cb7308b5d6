$(function() {

  $('#invisible-product-form').validate({
    rules : {
      name : {
        required : true,
        maxlength : 100
      },
      price : {
        required : true,
        regex : /^[0-9|.]*$/
      },
      period : {
        required : true,
        regex : /^[0-9]*$/
      },
      expireDt : {
        required : true
      }
    },
    messages : {
      price : {
        regex : 'Numbers And Dot(.) can be accepted.'
      },
      period : {
        regex : 'Number can be accepted.'
      }
    },
    errorClass : 'form-validation-error',
    highlight : function(element, errorClass, validClass) {
      $(element).parent().addClass('has-error');
    },
    unhighlight : function(element, errorClass, validClass) {
      $(element).parent().removeClass('has-error');
    },
    ignore : '.ignore'
  });

  var options = {
    url : 'createJustInPackageProduct',
    type : 'post',
    dataType : 'json',
    clearForm : false,
    beforeSubmit : function() {
      return $('#invisible-product-form').valid();
    },
    success : function(data) {

      var libraryId = $('#invisible-product-form #libraryId').val();
      $('#hasVod-' + libraryId).text('p');

      var typeHtml = data.type == 'SINGLE' ? '<span class="label label-warning">SINGLE</span>' : '<span class="label label-primary">PACKAGE</span>';
      $('#type-' + libraryId).html(typeHtml);

      var saleTypeHtml = data.saleType == 'SVOD' ? '<span class="label label-warning">SVOD</span>' : '<span class="label label-primary">TVOD</span>';
      $('#saleType-' + libraryId).html(saleTypeHtml);

      var priceHtml = '';
      if (data.priceRental != 0 && data.priceRental != '' && data.priceRental != undefined) {
        priceHtml = '<span class="badge">Rental</span>' + data.priceRental + '<br>';
      }
      if (data.priceEst != 0 && data.priceEst != '' && data.priceEst != undefined) {
        priceHtml = priceHtml + '<span class="badge">Est</span>' + data.priceEst;
      }
      $('#price-' + libraryId).html(priceHtml);

      $('#invisibleProductTag-position-' + libraryId).find('a').attr('productId', data.productId);
      $('#invisibleProductTag-position-' + libraryId).find('a').html('edit');
      $('#invisibleProductTag-position-' + libraryId).closest('tr').find('input[name$="vodId"]').val(data.productId);

      $('#modal-invisible-product-form').modal('hide');
    }
  };

  $('#modal-invisible-product-form').ajaxForm(options);

});
