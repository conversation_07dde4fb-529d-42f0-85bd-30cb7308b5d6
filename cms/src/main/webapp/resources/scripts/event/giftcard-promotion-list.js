$(function() {

  $('#checkAll').on('click', function() {
    $('.checkbox').prop('checked', this.checked);
  });


  $('table').first().find('th').on('click', function() {

    if ($(this).hasClass('click-disabled')) {
      return;
    }

    var sortTargetColumn = $(this).attr('sort');
    if (sortTargetColumn != '') {
      $('#sortTargetColumn').val(sortTargetColumn);
    }

    var order = '';
    if ($(this).hasClass('sorting')) {
      order = 'DESC';
    } else if ($(this).hasClass('sorting_asc')) {
      order = 'DESC';
    } else if ($(this).hasClass('sorting_desc')) {
      order = 'ASC';
    }
    $('#ascendingOrder').val(order);

    $("form").first().submit();
  });


  $('#delete-btn').on('click', function() {
    if ($(".checkbox").is(':checked')) {

      if (!confirm('Are you sure to delete selected Promotions?'))
        return;

      $.ajax({
        url : 'delete',
        traditional : true,
        type : 'POST',
        data : $('#listVO input[name="giftCardPromotionIds"]').serialize(),
        dataType : 'json'
      }).done(function(data) {
        if (data.resultCode != '100') {
          alert(data.message);
          return;
        }
        document.location.reload();
      });
    } else {
      alert('Select Promotions To Delete...');
    }
  });

});
