$(function() {

  $('#checkAll').on('click', function() {
    $('.checkbox').prop('checked', this.checked);
  });

  $('#delete-btn').on('click', function() {
    if (!$('.checkbox').is(':checked')) {
      alert('Select Promotion...');
      return false;
    }
    $.ajax({
      url: 'delete',
      traditional: true,
      type: 'POST',
      data: $('#listVO input[name="promotionIds"]').serialize(),
      dataType: 'json'
    }).done(function(data) {
      if (data.resultCode != '100') {
        alert(data.message);

      } else {
        alert('Save Ok !');
        document.location.reload();
      }
    });
  });

  $('#pageSize').on('change', function() {
    $('#listVO').submit();
  });

});
