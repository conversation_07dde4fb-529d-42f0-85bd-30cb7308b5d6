$(function() {

  $('#checkAll').on('click', function() {
    $('.checkbox').prop('checked', this.checked);
  });

  $('#pageSize').on('change', function() {
    $("form").first().submit();
  });

  $('table').first().find('th').on('click', function() {

    if ($(this).hasClass('click-disabled')) {
      return;
    }

    var sortTargetColumn = $(this).attr('sort');
    if (sortTargetColumn != '') {
      $('#sortTargetColumn').val(sortTargetColumn);
    }

    var order = '';
    if ($(this).hasClass('sorting')) {
      order = 'DESC';
    } else if ($(this).hasClass('sorting_asc')) {
      order = 'DESC';
    } else if ($(this).hasClass('sorting_desc')) {
      order = 'ASC';
    }
    $('#ascendingOrder').val(order);
    $('#listVO').submit();

  });

  $('#delete-btn').on('click', function() {
    if ($(".checkbox").is(':checked')) {
      if (!confirm('Are you sure to Delete?')) {
        return;
      }
      $.post('delete', $('#listVO').serialize()).done(function(data) {
        if (data.resultCode == '100') {
          document.location.reload();
          return true;
        } else if(data.resultCode == '991') {
          alert("You can't delete this channel package that has child channels!");
        } else {
          alert('Save Fails..:' + data.message);
        }
      });

    } else {
      alert('Select Channel Packages To Delete!');
    }
    return false;
  });
  
  $('a.channelPackageId').on('click', function(event) {
    event.preventDefault();
    var channelPackageId = $(this).attr('channelPackageId');
    $.getJSON('../channel_package/get?id=' + channelPackageId, function(group) {
      $('#channelPackageForm #id').val(group.id);
      $('#channelPackageForm #name').val(group.name);
    });

    $('#modal-channel-group-form').modal('show');
  });

  $('.save-btn').on('click', function() {

    var channelPackageId = $(this).parent().parent().find('input[name="channelPackageId"]').val();
    var sortSeq = $(this).parent().parent().find('input[name="sortSeq"]').val();
    console.log(channelPackageId, sortSeq);

    $.ajax({
      type : 'POST',
      url : 'saveSortSeq',
      data : {
        'channelPackageId' : channelPackageId,
        'sortSeq' : sortSeq
      },
      dataType : 'json'
    }).done(function(result) {
      if (result.resultCode == '100') {
        alert('Saved!');
        document.location.reload();
      } else {
        alert(result.message);
        return false;
      }
    }).fail(function(jqXHR) {
      alert('Ajax call Fails..');
    })
  });

});
