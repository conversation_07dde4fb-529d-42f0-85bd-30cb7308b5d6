var selectedCategory;
$(function() {

  $('.tree').on('click', 'li span', function() {
    console.log($(this).attr('id'), ' category selected..');
    var categoryId = $(this).attr('id');
    categorySelected(categoryId);
  });

  $('#add-root-category-btn').on('click', function() {
    openCategoryAddModal(selectedCategory);
  });

  $('#add-child-category-btn').on('click', function() {
    if (!selectedCategory) {
      alert('Select Parent Category!');
      return false;
    }
    openCategoryAddModal(selectedCategory);
  });

  $('#edit-category-btn').on('click', function() {
    if (!selectedCategory) {
      alert('Select Category!');
      return false;
    }
    openCategoryEditModal(selectedCategory);
  });

  $('#delete-category-btn').on('click', function() {
    if (!selectedCategory) {
      alert('Select Category!');
      return false;
    }
    deleteCategory(selectedCategory);
  });

  $('#sort-category-btn').on('click', function() {
    var params = '{ "list" :' + getCategorySortObjectArray() + ' }';
    if (!confirm('Are you sure to reorder Categories?')){
      return;
    }

    $.ajax({
      url: 'applySort',
      type: 'POST',
      contentType: 'application/json',
      data: params,
      dataType: 'json'
    }).done(function(data) {
      if (data.resultCode != '100') {
        alert(data.message);
        return;
      }
      document.location.reload();
    });

  });

  function getCategorySortObjectArray() {
    var arr = [];
    $('#category-div ul > li > span').each(function() {
      console.log('childcount:', $(this).attr('parentId'), '-', $(this).attr('id'));
      var parentId = $(this).attr('parentId');
      var id = $(this).attr('id');

      if (parentId) {
        var result = $.grep(arr, function(e) { return e.id == parentId; });
        console.log(result.length);
        console.log(result[0]);
        if (result.length > 0) {
          var parentChildrenArray = result[0].children;
          parentChildrenArray.push({id: id, children: []});
        } else {
          var obj = {id: parentId, children: []};
          arr.push(obj);
          obj.children.push({id: id, children: []});
        }

      } else {
        arr.push({id: id, children: []});
      }
    });
    console.log(JSON.stringify(arr));
    return JSON.stringify(arr);
  }

});

function categorySelected(categoryId) {

  var isToggle = false;
  if (selectedCategory == categoryId) {
    isToggle = true;
    selectedCategory = '';
  } else {
    selectedCategory = categoryId;
  }

  $('.tree li span').each(function() {
    var id = $(this).attr('id');
    if (id == categoryId && !isToggle) {
      $(this).addClass('category-selected');
    } else {
      $(this).removeClass('category-selected');
    }
  });

}
