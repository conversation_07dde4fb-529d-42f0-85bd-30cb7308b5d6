$(function() {

  $('#checkAll').on('click', function() {
    $('.checkbox').prop('checked', this.checked);
  });

  $('#delete-btn').on('click', function() {
    if ($(".checkbox").is(':checked')) {

      if (!confirm('Are you sure to delete selected Notices?'))
        return;

      $.ajax({
        url : 'delete',
        traditional : true,
        type : 'POST',
        data : $('#listVO input[name="noticeIds"]').serialize(),
        dataType : 'json'
      }).done(function(data) {
        if (data.resultCode != '100') {
          alert(data.message);
          return;
        }
        document.location.reload();
      });
    } else {
      alert('Select Notice To Delete...');
    }
  });

});
