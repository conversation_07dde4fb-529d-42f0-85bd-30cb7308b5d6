var previewPlayer;
$.ajax({
  url: '/library/getDrmPlayerConfig',
  dataType: 'json',
  success: function(data) {
    // console.log('previewPlayer init:', data);
    previewPlayer = new DashDrmPlayer('video-tag', data.licenseServerAddress, data.userId , data.sessionId , data.merchant);
  }
});

$(function() {

  $(document).mouseup(function(e) {
    var container = $('#listContextMenu');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      previewPlayer.unload();
      container.hide();
      $('#div-preview').hide();
      $('#thumbnail-div').show();
    }
  });

  $('#listContextMenu').on('click', 'a[id="play-btn"]', function() {

    var libraryId = $(this).closest('li').attr('libraryId');
    $('#thumbnail-div').hide();
    $('#div-preview').show();
    console.log('libraryId:', libraryId);
    $.ajax({
      url: '/library/getSignedUrl?libraryId=' + libraryId,
      dataType: 'json',
      async: false,
      success: function(data) {
        console.log('data.url:', data.url);
        // data.url = 'http://d3ciwet6q7g81e.cloudfront.net/staging/2c9f89bf62e3d50a016311d1701b000c/Manifest.mpd';
        if (data.url) {
          previewPlayer.load(libraryId, data.url);
        } else {
          previewPlayer.unload();
        }
      }
    });
  });

});
