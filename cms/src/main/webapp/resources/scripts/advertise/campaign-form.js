$(function() {
  var options = {
    url: 'save',
    type: 'post',
    dataType: 'json',
    clearForm: false,
    beforeSubmit: beforeSubmitForm,
    success: function(data) {
      if (data.resultCode == '100') {
        alert('Saved');
        document.location.href = 'list?' + queryString;
      } else {
        alert('Save Fails..:' + data.message);
      }
    }
  };

  $('#campaign-form').ajaxForm(options);

  $('#save-btn').on('click', function() {
    $('#countryIds option').prop('selected', true);
    $('#inventoryIds option').prop('selected', true);
    $('#campaign-form').submit();
  });

  function beforeSubmitForm() {
    return $('#campaign-form').valid();
  }

  setupFormValidator();
  function setupFormValidator() {
    $('#campaign-form').validate({
      rules: {
        name: {
          required: true,
          maxlength: 255
        },
        startDt: {
          required: true
        },
        endDt: {
          required: true
        }
      },
      messages: {
        name: {
          required: 'Name is required',
          maxlength: 'Name can\'t be over 255 characters'
        },
        name: {
          required: 'Start Date is required'
        },
        name: {
          required: 'End Date is required'
        }
      },
      errorClass: 'form-validation-error',
      highlight: function(element, errorClass, validClass) {
        $(element).parent().addClass('has-error');
      },
      unhighlight: function(element, errorClass, validClass) {
        $(element).parent().removeClass('has-error');
      },
      ignore: '.ignore'
    });
  }

  $('#campaign-form #type').on('change', function() {
    setupResourceDiv($(this).val());
    loadInventories($(this).val());
  });

  function setupResourceDiv(current) {
    if (current === 'BANNER') {
      $('#resource-banner-div').show();
      $('#resource-library-div').hide();
      $('#resource-ott-div').hide();
    } else if (current === 'OTT_RECOMMEND' || current === 'OTT_AD_WIDE') {
      $('#resource-banner-div').show();
      $('#resource-library-div').show();
      $('#resource-ott-div').show();
    } else if (current === 'OTT_AD_WIDE_LIST') {
      $('#resource-banner-div').hide();
      $('#resource-library-div').hide();
      $('#resource-ott-div').hide();
    } else {
      $('#resource-banner-div').hide();
      $('#resource-library-div').show();
      $('#resource-ott-div').hide();
    }
  }

  $('#campaign-form input[name="useCountryTarget"]').on('click', function() {
    var value = $(this).val();
    console.log('value1:', value == true);
    console.log('value2:', value == 'true');
    if (value == 'true') {
      $('.useRegionTargetAffected').show();
    } else {
      $('.useRegionTargetAffected').hide();
    }
  });

  $('#country-assign-btn').on('click', function() {
    var selectedOptions = $('#country-select option:selected');
    console.log('selectedOptions.length:', selectedOptions.length);
    if (selectedOptions.length == 0) {
      alert('Select Country!');
      return false;
    }
    $.each(selectedOptions, function(i, item) {
      var selectedOptionId = $(item).val();
      console.log('selectedOptionId:', selectedOptionId);

      var exists = 0 != $('#countryIds option[value=' + selectedOptionId + ']').length;
      console.log('selectedOptionId exist:', exists);
      if (exists) {
        return true;
      }

      console.log('movedSelectedOptionId:', selectedOptionId);
      $('#countryIds').append(item);
    });

  });

  $('#country-release-btn').on('click', function() {
    var selectedOptions = $('#countryIds option:selected');
    if (selectedOptions.length == 0) {
      alert('Select Country to remove!');
      return false;
    }
    $.each(selectedOptions, function(i, item) {
      $('#country-select').append(item);
    });

    $('#country-select').html($('option', $('#country-select')).sort(function(a, b) {
      var arel = $(a).val();
      var brel = $(b).val();
      return arel == brel ? 0 : arel < brel ? -1 : 1;
    }));

  });

  $('#inventory-add-btn').on('click', function() {
    var exposureCount = $('#exposureValue').val();
    if (exposureCount == 0 || !exposureCount) {
      alert('Input Exposure count!');
      $('#exposureValue').focus();
      return false;
    }

    var inventoryId = $('#inventory-select option:selected').val();
    var inventoryName = $('#inventory-select option:selected').text();

    var li = $('input[name=inventoryIds][value^="' + inventoryId + '@"]');
    if (li.val()) {
      console.log('li.val:', li.val());
      var existValue = li.val().split('@')[1];
      var newValue = Number(existValue) + Number(exposureCount);
      li.val(inventoryId + '@' + newValue);
      li.parent().find('span.badge').text(newValue);
    } else {
      var template = $('#assigned-inventory-template').html();
      template = template.replace(/%name%/g, inventoryName);
      template = template.replace(/%exposureCount%/g, exposureCount);
      template = template.replace(/%inventoryId%/g, inventoryId);
      $('#assigned-inventory-div').append(template);
    }
    recalculateExpense();
  });

  function recalculateExpense() {
    console.log('recalculateExpense');
    var sum = 0.0;
    $('#assigned-inventory-div input[name=inventoryIds]').each(function() {
      console.log('inventoryIds.val:', $(this).val());
      if ($(this).val().indexOf('@') !== -1) {
        var inventoryId = $(this).val().split('@')[0];
        var exposureCount = $(this).val().split('@')[1];
        var unitPrice = inventoryUnitPriceMap[inventoryId];
        console.log('unitprice.val:', unitPrice);
        if (!unitPrice)
          unitPrice = 0.0;

        var multiplyResult = exposureCount * unitPrice;
        sum = sum + multiplyResult;
      }
    });

    $('#expense').text(sum).currency();
  }

  $('#inventory-release-btn').on('click', function() {
    var selectedOptions = $('#inventoryIds option:selected');
    if (selectedOptions.length == 0) {
      alert('Select Inventory to remove!');
      return false;
    }
    $.each(selectedOptions, function(i, item) {

      var value = $(item).val().split('@')[0];
      var text = $(item).text().split('(')[0];
      var addOption = $('<option>', {
        value: value,
        text: text
      });
      $(this).remove();
      $('#inventory-select').append(addOption);
    });

    $('#inventory-select').html($('option', $('#inventory-select')).sort(function(a, b) {
      var arel = $(a).text();
      var brel = $(b).text();
      return arel == brel ? 0 : arel < brel ? -1 : 1;
    }));

  });

  $('#library-search-btn').on('click', function() {
    $('#modal-library-select-form').modal('show');
  });

  function initialize() {
    setupResourceDiv($('#type').val());
    recalculateExpense();
    loadInventories($('#type').val());
  }
  initialize();

  $('#ad-image-search-btn').on('click', function() {
    $('#modal-ad-image-select-form').modal('show');
  });

  $('#resource-banner-div').on('click', '.delete', function() {
    $(this).closest('div.row').remove();
  });

  function loadInventories(type) {
    inventoryUnitPriceMap = {};
    $.getJSON('inventories?campaignType=' + type).done(function(dataList) {
      $('#inventory-select option').remove();
      if (dataList.length > 0) {
        $.each(dataList, function(i, item) {
          var addOption = $('<option>', {
            value: item.id,
            text: item.name
          });
          inventoryUnitPriceMap[item.id] = Number(item.unitPrice);
          $('#inventory-select').append(addOption);
        });
      }

    });
  }

});
