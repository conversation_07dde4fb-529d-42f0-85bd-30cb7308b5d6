<!DOCTYPE html>
<%@ include file="/common/common.jsp"%>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8"%>

<head>
<title>Coupon Condition</title>
<style>
table.sorting-table thead .sorting {
	background: url('/resources/images/bootstrap/sort_both.png') no-repeat
		center right;
}

table.sorting-table thead .sorting_asc {
	background: url('/resources/images/bootstrap/sort_asc.png') no-repeat
		center right;
}

table.sorting-table thead .sorting_desc {
	background: url('/resources/images/bootstrap/sort_desc.png') no-repeat
		center right;
}
</style>
</head>
<div class="row">
  <div class="col-md-12">
    <h3 class="page-header">Coupon Condition List</h3>
  </div>
</div>

<form:form modelAttribute="listVO" method="get" action="list">
  <div class="row">
    <div class="col-md-6">
      <div class="form-group input-group">
        <input type="text" class="form-control input-sm" placeholder="Condition Title" name="search.title" value="${listVO.search.title }"> <span
          class="input-group-btn">
          <button class="btn btn-default btn-sm" type="submit">
            <i class="fa fa-search"></i>
          </button>
        </span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="form-inline pull-right">
        Show :
        <form:select path="pageSize" cssClass="form-control input-sm">
          <form:options items="${listVO.pageSizes }" itemValue="value" itemLabel="label" />
        </form:select>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12">
      <div class="table-responsive">
        <form:hidden path="sortTargetColumn" />
        <form:hidden path="ascendingOrder" />
        <c:set var="titleSortClass" value="sorting" />
        <c:if test="${listVO.sortTargetColumn eq 'title' }">
          <c:set var="titleSortClass" value="${listVO.ascendingOrder eq 'DESC' ? 'sorting_desc' :'sorting_asc' }" />
        </c:if>

        <c:set var="typeSortClass" value="sorting" />
        <c:if test="${listVO.sortTargetColumn eq 'type' }">
          <c:set var="typeSortClass" value="${listVO.ascendingOrder eq 'DESC' ? 'sorting_desc' :'sorting_asc' }" />
        </c:if>

        <c:set var="startDtSortClass" value="sorting" />
        <c:if test="${listVO.sortTargetColumn eq 'startDt' }">
          <c:set var="startDtSortClass" value="${listVO.ascendingOrder eq 'DESC' ? 'sorting_desc' :'sorting_asc' }" />
        </c:if>

        <c:set var="endDtSortClass" value="sorting" />
        <c:if test="${listVO.sortTargetColumn eq 'endDt' }">
          <c:set var="endDtSortClass" value="${listVO.ascendingOrder eq 'DESC' ? 'sorting_desc' :'sorting_asc' }" />
        </c:if>

        <table class="table table-striped table-bordered table-hover sorting-table">
          <thead>
            <tr>
              <th class="text-center click-disabled" style="width: 60px;"><input type="checkbox" class="checkbox" value="" id="checkAll"
                style="margin-left: 35%;"></th>
              <th class="text-center ${titleSortClass }" sort="title">Title</th>
              <th class="text-center col-sm-3">Condition</th>
              <th class="text-center col-sm-1 click-disabled">Type</th>
              <th class="text-center col-sm-1 click-disabled">Action</th>
              <th class="text-center col-sm-1 click-disabled">Amount</th>
              <th class="text-center col-sm-1 click-disabled">UsableCount</th>
              <th class="text-center col-sm-1 click-disabled">Active</th>
            </tr>
          </thead>
          <tbody>
            <c:forEach var="voucher" items="${listVO.list }" varStatus="status">
              <tr class="odd gradeX">
                <td align="center" class="vertical-middle"><input type="checkbox" class="checkbox" name="voucherIds" value="${voucher.id }"></td>
                <td class="text-center"><a href="view?voucherId=${voucher.id }" class="voucher-id" eventId="${voucher.id }">${voucher.title }</a></td>
                <td class="text-left font12">${voucher.issuedTargetLabels }</td>
                <td class="text-center font12">${voucher.type }</td>
                <td class="text-center font12">${voucher.action }</td>
                <td class="text-center">${voucher.amount }</td>
                <td class="text-center">${voucher.usableCount }</td>
                <td class="text-center">
                  <c:set var="enabledLabelClass" value="${voucher.enabled ? 'label-primary' : 'label-danger'}" />
                  <span class="label ${enabledLabelClass}">${voucher.enabled }</span>

                </td>
              </tr>
            </c:forEach>

          </tbody>
        </table>
      </div>

    </div>
    <div class="col-md-12">
      <ui:pagination name="${listVO}"></ui:pagination>
    </div>
    <div class="col-md-12">
      <div class="pull-right">
        <a href="form" class="btn btn-custom-default btn-sm">New</a>
        <button type="button" class="btn btn-custom-default btn-sm" id="activate-btn">Activate</button>
        <button type="button" class="btn btn-custom-default btn-sm" id="deactivate-btn">DeActivate</button>
      </div>
    </div>
</form:form>
<!-- Page-Level Demo Scripts - Tables - Use for reference -->
<jsp:include page="left_menu.jsp" />
<script src="${RESOURCE_SERVER_URL}/resources/scripts/unitel_coupon/coupon-group-condition-list.js?vs=${timestamp}"></script>


