<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8"%>
<head>
<style type="text/css">
#leftmenuContextMenu {
	position: absolute;
	display: none;
	z-index: 9999;
}

#leftmenuContextMenu ul>li {
	padding-left: 10px;
	padding-right: 10px;
	padding-bottom: 5px;
}

#leftmenuContextMenu ul>li.divider {
	padding: 0px;
}

.font-color-gray {
	color: #999999;
}
</style>
</head>

<div id="leftmenuContextMenu" class="dropdown clearfix">
  <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu"
    style="display: block; position: static; margin-top: 5px;">
    <li id="video" libraryId="">
      <div id="thumbnail-div" class="thumbnail-video">
        <img id="thumbnail" src="" width="320" height="180" />
        <a id="play-btn" style="cursor:pointer;"></a>
      </div>
      <div id="div-preview">
      </div>
    </li>
    <li role="presentation" class="divider"></li>
    <li><label>Info</label></li>
    <li>
      <div class="row">
        <div class="col-sm-4 font-color-gray">Name</div>
        <div class="col-sm-8" id="libraryName"></div>
      </div>
    </li>
    <li>
      <div class="row">
        <div class="col-sm-4 font-color-gray">Duration</div>
        <div class="col-sm-8" id="formattedDuration"></div>
      </div>
    </li>
    <li>
      <div class="row">
        <div class="col-sm-4 font-color-gray">Rating</div>
        <div class="col-sm-8" id="rating"></div>
      </div>
    </li>
    <li>
      <div class="row">
        <div class="col-sm-4 font-color-gray">StarRating</div>
        <div class="col-sm-8" id="starRating"></div>
      </div>
    </li>
    <li>
      <div class="row">
        <div class="col-sm-4 font-color-gray">Studio</div>
        <div class="col-sm-8" id="studio"></div>
      </div>
    </li>
    <li>
      <div class="row">
        <div class="col-sm-4 font-color-gray">Release</div>
        <div class="col-sm-8" id="releaseYear"></div>
      </div>
    </li>
    <li>
      <div class="top30"></div>
    </li>
  </ul>
</div>

<script type="text/javascript" src="${RESOURCE_SERVER_URL}/resources/scripts/player/osmf/swfobject.js?vs=${timestamp}"></script>
<script src="${RESOURCE_SERVER_URL}/resources/scripts/player/dash-player.js?vs=${timestamp}"></script>
<script src="${RESOURCE_SERVER_URL}/resources/scripts/schedule_template/view_context_menu.js?vs=${timestamp}"></script>

