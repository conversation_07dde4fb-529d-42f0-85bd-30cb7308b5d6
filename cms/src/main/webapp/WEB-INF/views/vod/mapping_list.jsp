<!DOCTYPE html>
<%@ include file="/common/common.jsp" %>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8" %>

<head>
  <title>Product Mapper With Univision</title>
  <style>
    table.sorting-table thead .sorting {
      background: url('/resources/images/bootstrap/sort_both.png') no-repeat center right;
    }

    table.sorting-table thead .sorting_asc {
      background: url('/resources/images/bootstrap/sort_asc.png') no-repeat center right;
    }

    table.sorting-table thead .sorting_desc {
      background: url('/resources/images/bootstrap/sort_desc.png') no-repeat center right;
    }

  </style>
  <link href="${RESOURCE_SERVER_URL}/resources/bootstrap/css/plugins/inline-edit/bootstrap-editable.css?vs=${timestamp}" rel="stylesheet">
</head>

<div class="row">
  <div class="col-md-12">
    <h3 class="page-header">Product Mapping (Univision)</h3>
  </div>
</div>

<form:form modelAttribute="listVO" method="get" action="mapping_list">
  <div class="row">
  <div class="col-md-3">
    <form:select path="search.categoryId" cssClass="form-control input-sm" id="search-category-list">
      <form:option value="">-- Select Category --</form:option>
      <c:forEach var="category" items="${categoryList }">
        <form:option value="${category.id }">${category.categoryNameWithParentName }</form:option>
      </c:forEach>
    </form:select>
  </div>
  <div class="col-md-4">
    <div class="form-group input-group">
      <input type="text" class="form-control input-sm" placeholder="Product Name" name="search.vodName" value="${listVO.search.vodName }"> <span
        class="input-group-btn">
          <button class="btn btn-default btn-sm" type="button" id="search-btn">
            <i class="fa fa-search"></i>
          </button>
        </span>
    </div>
  </div>

  <div class="col-md-3">
    <a id="excelExportLink" href="#" class="btn btn-default btn-sm">Export to Excel</a>
    <a href="#modal-excel-form" class="btn btn-default btn-sm" data-toggle="modal">Import Excel</a>
  </div>

  <div class="col-md-2">
    <div class="form-inline pull-right">
      <form:select path="pageSize" cssClass="form-control input-sm">
        <form:options items="${listVO.pageSizes }" itemValue="value" itemLabel="label"/>
      </form:select>
    </div>
  </div>

  <!-- list -->
  <div class="col-md-12" id="listType">
    <div class="table-responsive">
      <form:hidden path="sortTargetColumn"/>
      <form:hidden path="ascendingOrder"/>
      <c:set var="vodNameSortClass" value="sorting"/>
      <c:if test="${listVO.sortTargetColumn eq 'vodName' }">
        <c:choose>
          <c:when test="${listVO.ascendingOrder eq 'DESC' }">
            <c:set var="vodNameSortClass" value="sorting_desc"/>
          </c:when>
          <c:otherwise>
            <c:set var="vodNameSortClass" value="sorting_asc"/>
          </c:otherwise>
        </c:choose>
      </c:if>
      <c:set var="updatedDtSortClass" value="sorting"/>
      <c:if test="${listVO.sortTargetColumn eq 'updatedDt' }">
        <c:choose>
          <c:when test="${listVO.ascendingOrder eq 'DESC' }">
            <c:set var="updatedDtSortClass" value="sorting_desc"/>
          </c:when>
          <c:otherwise>
            <c:set var="updatedDtSortClass" value="sorting_asc"/>
          </c:otherwise>
        </c:choose>
      </c:if>
      <table class="table table-striped table-bordered table-hover sorting-table">
        <thead>
        <tr>
          <th class="text-center col-md-1 click-disabled">Type</th>
          <th class="text-center ${vodNameSortClass } vod-name-sort">Product Name</th>
          <th class="text-center click-disabled">Price Id</th>
          <th class="text-center click-disabled">Univision Id</th>
          <th class="text-center col-md-1 click-disabled">Mature</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach var="vod" items="${listVO.list }" varStatus="status">
          <c:forEach var="vodPrice" items="${vod.vodPrices}">
            <tr class="odd gradeX">
              <td class="text-center vertical-middle">
                <span class="label label-primary">${vodPrice.saleType }</span>
              </td>
              <td class="vertical-middle"><a href="view?vodId=${vod.id }">${vod.name }</a> <span class="label label-default"> <c:choose>
                <c:when test="${vod.type eq 'PACKAGE' }">package</c:when>
                <c:when test="${vod.type eq 'UNION' }">union</c:when>
                <c:otherwise>single</c:otherwise>
              </c:choose>
                </span> <c:if test="${vod.type eq 'PACKAGE' || vod.type eq 'UNION' }">
                <span class="badge">+${vod.childVodCount}</span>
              </c:if></td>
              <td class="text-center vertical-middle">${vodPrice.id}</td>
              <td class="text-center vertical-middle">
                <a href="#" class="editable-text" data-pk="${vodPrice.id}"  data-url="/vod/mapping_save">${vodPrice.univisionProductId}</a>
              </td>
              <td class="text-center vertical-middle">
                <c:set var="checked" value="${vod.mature eq true ? 'checked' : ''}" />
                <input type="checkbox" class="mature" data-vodid="${vod.id}" ${checked} />
              </td>
            </tr>
          </c:forEach>

        </c:forEach>

        </tbody>
      </table>
    </div>
  </div>

  <div class="col-md-12 text-center">
    <ui:pagination name="${listVO}"></ui:pagination>
  </div>
  <div class="col-md-12" style="margin-bottom: 50px;">
    <div class="row">
      <div class="col-md-12">
      </div>
    </div>
  </div>
</form:form>

<form id="excel-form" method="post" action="/vod/mapping_download">
</form>
<!-- Page-Level Demo Scripts - Tables - Use for reference -->
<script>

</script>
<script src="${RESOURCE_SERVER_URL}/resources/scripts/vod/mapping_list.js?vs=${timestamp}"></script>
<script src="${RESOURCE_SERVER_URL}/resources/bootstrap/js/bootstrap-multiselect.js?vs=${timestamp}"></script>
<script src="${RESOURCE_SERVER_URL}/resources/bootstrap/js/plugins/inline-edit/bootstrap-editable.js?vs=${timestamp}"></script>
<jsp:include page="left_menu_list.jsp"/>
<jsp:include page="modal-vod-mapping-excel-form.jsp" />

