<!DOCTYPE html>
<%@ include file="/common/common.jsp"%>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8"%>

<head>
<title>Banner Mobile</title>
<style>
#image-table td {
	padding: 0;
	margin: 0;
}

#image-table {
	border-collapse: collapse;
}
</style>
</head>

<div class="row">
  <div class="col-md-12">
    <h3 class="page-header">Banner Mobile</h3>
  </div>
</div>

<form:form modelAttribute="recommendBanner" method="post" action="banner_save" enctype="multipart/form-data">
  <form:hidden path="id"/>
  <div class="row">
    <div class="col-md-2">
      <label>Sale Type</label>
    </div>
    <div class="col-md-10">
      <c:set var="saleTypeDisabled" value="${not empty recommendBanner.id ? true : false }" />
      <form:select path="saleType" cssClass="form-control input-sm width300" disabled="${saleTypeDisabled }">
        <form:options items="${saleTypes }" />
      </form:select>
    </div>
  </div>

  <div class="row top15">
    <div class="col-md-2">
      <label>Banner Type</label>
    </div>
    <div class="col-md-10">
      <c:set var="bannerTypeDisabled" value="${not empty recommendBanner.id ? true : false }" />
      <form:select path="bannerType" cssClass="form-control input-sm width300" disabled = "${bannerTypeDisabled }">
        <form:options items="${bannerTypes }" />
      </form:select>
    </div>
  </div>

  <div class="row top15">
    <div class="col-md-2">
      <label>Guide</label>
    </div>
    <div class="col-md-10">
      <img src="${RESOURCE_SERVER_URL}/resources/images/recommend/type-${recommendBanner.bannerType}.png" />
    </div>
  </div>

  <div class="row top15">
    <div class="col-md-2">
      <label>Preview</label>
    </div>
    <div class="col-md-10">
      <jsp:include page="banner_type_${recommendBanner.bannerType }.jsp" />
    </div>
  </div>

  <div class="row top15">
    <div class="col-md-2">
      <label>Image Upload And VOD assign</label>
    </div>
    <div class="col-md-10">
      <c:forEach var="bannerImage" items="${recommendBanner.recommendBannerImages }" varStatus="status">
        <div class="form-inline top5">
          <input type="hidden" name="recommendBannerImagesTemp[${status.index }].id.seq" value="${bannerImage.id.seq }" />
          <input type="hidden" name="recommendBannerImagesTemp[${status.index }].vod.id" value="${bannerImage.vod.id }" />
          <input type="hidden" name="recommendBannerImagesTemp[${status.index }].bannerImage" value="${bannerImage.bannerImage }" />
          <label>${bannerImage.id.seq }.</label>
          <input type="file" name="recommendBannerImagesTemp[${status.index }].imageFile" class="form-control input-sm width300 bannerImageFile" />
          <input type="text" id="recommendBannerImagesTemp[${status.index }].vodName" class="form-control input-sm width300"
            readonly="readonly" value="${bannerImage.vod.name }">
          <button type="button" class="btn btn-default btn-sm vod-assign-btn" index="${status.index }">Vod Assign</button>
        </div>
      </c:forEach>
    </div>
  </div>

  <div class="row top30">
    <div class="col-md-12">
      <div class="pull-right">
        <button type="submit" class="btn btn-custom-default btn-sm">Save</button>
        <a href="banner_list?${bannerSearchQuery }" class="btn btn-custom-default btn-sm">List</a>
      </div>
    </div>
  </div>

</form:form>

<img src="" id="imageSizeCheck" style="display: none;" />

<script>
  var searchQuery = '${bannerSearchQuery}';
</script>

<!-- Page-Level Demo Scripts - Tables - Use for reference -->
<jsp:include page="/WEB-INF/views/vod/left_menu_list.jsp" />
<jsp:include page="modal-vod-assign-form.jsp" />
<script src="${RESOURCE_SERVER_URL}/resources/scripts/recommend/banner_form.js?vs=${timestamp}"></script>
<script src="${RESOURCE_SERVER_URL}/resources/bootstrap/js/plugins/paged-scroll/jquery-paged-scroll.min.js?vs=${timestamp}"></script>

