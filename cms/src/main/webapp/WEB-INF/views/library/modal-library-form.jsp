<!DOCTYPE html>
<%@ include file="/common/common.jsp" %>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8" %>
<style>
  #modal-library-form .modal-dialog {
    width: 1024px;
  }

  #modal-library-form .modal-content {
    height: 100%;
  }

  .movie-frame {
    width: 264px;
    height: 148px;
     	border: 1px solid #ccc;
  }

  .trailer-frame {
    width: 264px;
    height: 148px;
     	border: 1px solid #ccc;
  }

  .checkbox-inline.no_indent {
    margin-left: 0;
    margin-right: 10px;
  }

  .checkbox-inline.no_indent:last-child {
    margin-right: 0px;
  }

  .checkbox-inline input[type="checkbox"] {
    margin-top: 4px;
  }

  textarea {
    resize: none;
  }

  .movie-upload-btn {
    filter: alpha(opacity=0);
    opacity: 0;
  }

  .trailer-upload-btn {
    filter: alpha(opacity=0);
    opacity: 0;
  }

  .upload-container-btn-below {
    display: inline-block;
    width: 50px;
    margin-right: 45px;
  }

  .library-thumbnail-video {
    position: relative;
  }

  .library-thumbnail-video a {
    position: absolute;
    display: block;
    background: url('/resources/images/btn/play_small.png') center center no-repeat;
    background-size: 50px 50px;
    height: 50px;
    width: 50px;
    top: 49px;
    left: 109px;
    z-index: 999;
  }

  .subtitle {
    border: 1px solid #ccc;
    padding: 3px;
    border-radius: 3px;
  }
</style>
<link href="${RESOURCE_SERVER_URL}/resources/bootstrap/css/plugins/tag/bootstrap-tag-cloud.css?vs=${timestamp}" rel="stylesheet">

<div class="modal fade" id="modal-library-form" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true"
     style="display: none;">
  <div class="modal-dialog">
    <form id="library" enctype="multipart/form-data">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
          <h4 class="modal-title" id="myModalLabel">Edit Library (ID : <span id="id-display"></span>)</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" id="id" name="id" value=""/>
          <div class="row">
            <div class="col-md-5">
              <div class="row">
                <div class="col-md-7">
                  <label>Poster Horizontal</label>(16:9)
                  <div class="col-md-12">
                    <input type="hidden" name="posterLandscape" value=""/> <img class="media-object" src="" alt=""
                                                                                style="width: 220px; height: 123px; border: 0.01em solid #cccccc; margin-left: -15px;"
                                                                                id="posterLandscape-image">
                  </div>
                  <div class="col-md-4 media-body top5">
                    <div class="upload-file-container" style="margin-left: -14px;">
                      <input type="file" name="posterHorizontalFile" id="posterHorizontalFile" class="image-upload-btn"/>
                    </div>
                  </div>
                  <div class="col-md-8 top5">
                    <button type="button" class="btn btn-default btn-xs image-clear">Clear</button>
                  </div>
                </div>
                <div class="col-md-5">
                  <label>Poster Vertical</label>(16:25)
                  <div class="media" style="margin: 0">
                    <input type="hidden" name="posterPortrait" value=""/> <span class="pull-left" href="#"
                                                                                style="margin-right: 4px;"> <img class="media-object" src="" alt=""
                                                                                                                 style="width: 94.7px; height: 148px; border: 0.01em solid #cccccc"
                                                                                                                 id="posterPortrait-image">
                    </span>
                    <div class="media-body" style="margin-left: -10px;">
                      <div class="upload-file-container">
                        <input type="file" name="posterVerticalFile" id="posterVerticalFile" class="image-upload-btn"/>
                      </div>
                      <br>
                      <button type="button" class="btn btn-default btn-xs image-clear">Clear</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-7">
              <div class="row">

                <!--  movie -->
                <div class="col-md-6">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="form-inline">
                        <div class="form-group">
                          <label>Preview</label>
                        </div>
                        <div class="form-group pull-right">
                          <%--<button type="button" class="btn btn-primary btn-xs" id="movie-upload-file-select-btn">Upload</button>--%>
                        </div>
                      </div>
                      <div class="form-group">
                        <div id="movie-frame">
                          <div class="box black">
                            <div id="movie-thumbnail-div" class="library-thumbnail-video">
                              <img id="movie-thumbnail" src="" width="264" height="148"/> <a id="movie-play-btn"
                                                                                             style="cursor: pointer;"></a>
                            </div>
                            <div id="movie-video-div">

                            </div>
                            <%--<video id="moviestreaming" class="video-js vjs-default-skin vjs-big-play-centered" preload="auto" width="264px"--%>
                                   <%--height="148px">--%>

                            <%--</video>--%>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="top30"></div>
                  </div>
                  <div class="row" id="movie-upload-progress-bar-group">
                    <div class="col-md-12">
                      <button type="button" class="btn btn-danger btn-xs top5" id="movie-upload-start-btn">Start</button>
                    </div>
                    <div class="col-md-12 top5"></div>
                    <div class="col-md-2 movie-upload-progress-related">
                      <p>upload</p>
                    </div>
                    <div class="col-md-10 top5 movie-upload-progress-related">
                      <div class="progress progress-striped active inline" style="height: 10px;">
                        <div id="movie-upload-progress-bar" class="bar" style="height: 15px; width: 0%; background-color: #357ebd"></div>
                      </div>
                    </div>
                    <div class="col-md-2">
                      <p>encode</p>
                    </div>
                    <div class="col-md-10 top5">
                      <div class="progress progress-striped active inline" style="height: 10px;">
                        <div id="movie-encode-progress-bar" class="bar" style="height: 15px; width: 0%; background-color: #d9534f"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- // movie -->

                <!--  trailer -->
                <div class="col-md-6">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="form-inline">
                        <div class="form-group">
                          <label>Trailer</label>
                        </div>
                        <div class="form-group pull-right">
                          <%--<button type="button" class="btn btn-primary btn-xs" id="trailer-upload-file-select-btn">Upload</button>--%>
                        </div>
                      </div>
                      <div class="form-group">
                        <div id="trailer-frame">
                          <div class="box black">
                            <div id="trailer-thumbnail-div" class="library-thumbnail-video">
                              <img id="trailer-thumbnail" src="" width="264" height="148"/> <a id="trailer-play-btn"
                                                                                               style="cursor: pointer;"></a>
                            </div>
                            <div id="trailer-video-div">

                            </div>
                            <%--<video id="trailerstreaming" class="video-js vjs-default-skin vjs-big-play-centered" preload="auto" width="264px"--%>
                                   <%--height="148px">--%>
                            <%--</video>--%>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="top30"></div>
                  </div>
                  <div class="row" id="trailer-upload-progress-bar-group">
                    <div class="col-md-12">
                      <button type="button" class="btn btn-danger btn-xs top5" id="trailer-upload-start-btn">Start</button>
                    </div>
                    <div class="col-md-12 top5"></div>
                    <div class="col-md-2 trailer-upload-progress-related">
                      <p>upload</p>
                    </div>
                    <div class="col-md-10 top5 trailer-upload-progress-related">
                      <div class="progress progress-striped active inline" style="height: 10px;">
                        <div id="trailer-upload-progress-bar" class="bar"
                             style="height: 15px; width: 0%; background-color: #357ebd"></div>
                      </div>
                    </div>
                    <div class="col-md-2">
                      <p>encode</p>
                    </div>
                    <div class="col-md-10 top5">
                      <div class="progress progress-striped active inline" style="height: 10px;">
                        <div id="trailer-encode-progress-bar" class="bar"
                             style="height: 15px; width: 0%; background-color: #d9534f"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- // trailer -->
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <label>Title</label> <input id="name" name="name" class="form-control input-sm" placeholder="Enter Library name"
                                            maxlength="200" required data-toggle="tooltip" data-placement="top"
                                            data-original-title="Max 200 characters">

              </div>
              <div class="form-group">
                <label>Directors</label>
                <div id="director-info" class="form-group input-group">
                  <input type="text" class="form-control input-sm" placeholder="Enter Director names" id="director-add-input">
                  <span class="input-group-btn">
                    <button class="btn btn-default btn-sm" type="button" id="director-add-btn">
                      <i class="fa fa-plus"></i>
                    </button>
                  </span>
                </div>
              </div>
              <div class="form-group">
                <ul id="director-cloud" class="tag-cloud-info"></ul>
              </div>

              <div class="form-group">
                <label>Actors</label>
                <div id="actor-info" class="form-group input-group">
                  <input type="text" class="form-control input-sm" placeholder="Enter Actor names" id="actor-add-input"> <span
                    class="input-group-btn">
                    <button class="btn btn-default btn-sm" type="button" id="actor-add-btn">
                      <i class="fa fa-plus"></i>
                    </button>
                  </span>
                </div>
              </div>
              <div class="form-group">
                <ul id="actor-cloud" class="tag-cloud-info"></ul>
              </div>

              <div class="form-group">
                <label>Tags</label>
                <div id="tag-info" class="form-group input-group">
                  <input type="text" class="form-control input-sm" placeholder="Enter Tags" id="tag-add-input"> <span
                    class="input-group-btn">
                    <button class="btn btn-default btn-sm" type="button" id="tag-add-btn">
                      <i class="fa fa-plus"></i>
                    </button>
                  </span>
                </div>
              </div>
              <div class="form-group">
                <ul id="tag-cloud" class="tag-cloud-info"></ul>
              </div>

              <div class="form-group">
                <label>Genres</label>
                <div id="genre-info" class="form-group input-group">
                  <!--                   <input type="text" class="form-control input-sm" placeholder="Enter Genres" id="genre-add-input">  -->
                  <select class="form-control input-sm" multiple="multiple" id="genre-select">
                    <c:forEach var="item" items="${genres }">
                      <option value="${item.value }">${item.label }</option>
                    </c:forEach>
                  </select> <span class="input-group-btn">
                    <button class="btn btn-default btn-sm" type="button" id="genre-add-btn">
                      <i class="fa fa-plus"></i>
                    </button>
                  </span>
                </div>
              </div>
              <div class="form-group">
                <ul id="genre-cloud" class="tag-cloud-info"></ul>
              </div>

              <div class="form-inline">
                <label>Subtitle</label>
                <small>(.srt only available)</small>
                <button class="btn btn-default btn-xs pull-right" type="button" id="subtitle-add-btn">
                  <i class="fa fa-plus"></i>
                </button>
              </div>
              <div class="form-inline" id="subtitle-exist"></div>
              <div id="subtitle-div" class="top10"></div>

            </div>
            <div class="col-md-7">

              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label>Episode No.</label>
                    <input id="episodeNumber" name="episodeNumber" class="form-control input-sm numeric" placeholder="episodeNumber">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label>Episode Name</label>
                    <input id="episodeName" name="episodeName" class="form-control input-sm" placeholder="episodeName">
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <label>Rating</label>
                  <div class="form-group">
                    <select id="rating" name="rating" class="form-control input-sm">
                    </select>
                  </div>
                </div>
                <div class="col-md-6"></div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label>Studio</label>
                    <select id="studio" name="studio.id" class="form-control input-sm">
                    </select>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label>Duration</label>
                    <div class="input-group">
                      <input id="duration" name="duration" class="form-control input-sm" readonly="readonly"> <span
                        class="input-group-addon input-sm">sec</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <label>Group</label>
                  <div class="form-group">
                    <select id="libraryGroup" name="libraryGroup" class="form-control input-sm">
                    </select>
                  </div>
                </div>
                <div class="col-md-6">
                  <label>Release Year</label>
                  <div class="form-group">
                    <input id="releaseYear" name="releaseYear" class="form-control input-sm" maxlength="4" placeholder="yyyy">
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <label>Preview Offset</label>
                  <div class="input-group">
                    <input type="text" id="previewOffset" name="previewOffset" class="form-control input-sm numeric"/> <span
                      class="input-group-addon">seconds</span>
                  </div>
                </div>
                <div class="col-md-6">
                  <label>Preview Duration</label>
                  <div class="input-group">
                    <input type="text" id="previewDuration" name="previewDuration" class="form-control input-sm numeric"/> <span
                      class="input-group-addon">seconds</span>
                  </div>
                </div>
              </div>
              <div class="row top15">
                <div class="col-md-12">
                  <label><spring:eval expression="@environment['shortSynopsis.label']" /></label>
                  <div class="form-group">
                    <textarea id="shortSynopsis" name="shortSynopsis" class="form-control" rows="3"></textarea>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12">
                  <label>2nd Language Synopsis</label>
                  <div class="form-group">
                    <textarea id="longSynopsis" name="longSynopsis" class="form-control" rows="3"></textarea>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-12">
              <label>ScreenShot</label>
              <div class="form-group">
                <div class="row">
                  <c:forEach begin="0" end="5" varStatus="status">
                    <div class="col-md-2" style="padding-right: 10px;">
                      <div class="media" style="margin: 0">
                        <a class="pull-left" href="#"> <img id="screenshot_img_${status.index + 1 }" class="media-object"
                                                            src="" alt="" style="width: 140px; height: 105px; border: 0.01em solid #cccccc;"></a>
                      </div>
                    </div>
                  </c:forEach>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <input type="submit" class="btn btn-primary" value="Save"/>
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary" id="exportPdf-btn">PDF</button>
          </div>
        </div>
      </div>
      <!-- /.modal-content -->
    </form>
  </div>
  <!-- /.modal-dialog -->
  <form id="movieUploadForm">
    <input type="hidden" name="trailer" value=""/> <input type="hidden" id="encodingServerUrl" name="encodingServerUrl"/> <input
      type="hidden" id="libraryId" name="libraryId"/> <input type="hidden" id="channelId" name="channelId"
                                                             value="${sessionChannelId }"/> <input type="hidden" id="encoder" name="encoder"
                                                                                                   value=""/> <input type="hidden"
                                                                                                                     id="encoderId" name="encoderId"
                                                                                                                     value=""/>
    <div id="upload-hide-file-container">
      <input type="file" name="movieFile" id="movieFile" class="movie-upload-btn"/>
    </div>
  </form>
  <form id="trailerUploadForm">
    <input type="hidden" name="trailer" value="true"/> <input type="hidden" id="encodingServerUrl" name="encodingServerUrl"/>
    <input type="hidden" id="libraryId" name="libraryId"/> <input type="hidden" id="channelId" name="channelId"
                                                                  value="${sessionChannelId }"/> <input type="hidden" id="encoder" name="encoder"
                                                                                                        value=""/> <input type="hidden"
                                                                                                                          id="encoderId"
                                                                                                                          name="encoderId" value=""/>
    <div id="upload-hide-file-container">
      <input type="file" name="trailerFile" id="trailerFile" class="trailer-upload-btn"/>
    </div>
  </form>
</div>

<div id="subtitle-template" style="display: none;">
  <div class="form-inline">
    <div class="form-group">
      <input type="hidden" name="librarySubtitlesTemp[%index%].id.libraryId" value="%libraryId%"/> <select
        name="librarySubtitlesTemp[%index%].id.language" class="form-control input-sm">
      <option value="">-- Select --</option>
      <c:forEach var="lang" items="${languages }">
        <option value="${lang.value }">${lang.label }</option>
      </c:forEach>
    </select>
    </div>
    <div class="form-group">
      <input type="file" name="librarySubtitlesTemp[%index%].subtitleFile" class="form-control input-sm">
    </div>
    <div class="form-group">
      <button type="button" class="btn btn-default btn-xs delete">
        <i class="fa fa-times"></i>
      </button>
    </div>
  </div>
</div>

<img src="" id="sizeCheckImage"/>

<script>
  $(function () {
    $(".image-upload-btn").change(function () {
      var $img = $(this).parent().parent().parent().find('img');
      readURL(this, $img);
      setTimeout(function () {

        if ($img.attr('id') == 'posterLandscape-image') {
          var limitWidth = 960;
          var landScapeImage = document.getElementById('posterLandscape-image');
          var width = landScapeImage.naturalWidth;
          if (width < limitWidth) {
            alert('PosterLandscape Image Width should be over or equals with ' + limitWidth);
            $(landScapeImage).attr('src', '');
            if ($('#posterHorizontalFile').val()) {
              $('#posterHorizontalFile').val('');
            }
            return false;
          }
        } else if ($img.attr('id') == 'posterPortrait-image') {
          var limitHeight = 204;
          var portraitImage = document.getElementById('posterPortrait-image');
          var height = portraitImage.naturalHeight;
          if (height < limitHeight) {
            alert('PosterPortrait Image Height should be over or equals with ' + limitHeight);
            $(portraitImage).attr('src', '');
            if ($('#posterVerticalFile').val()) {
              $('#posterVerticalFile').val('');
            }
            return false;
          }
        }

      }, 500);
    });

    $('.form-group').tooltip({
      selector: "[data-toggle=tooltip]"
    });

  });
</script>
<script>
  var licenseDisabled = '${licenseDisabled}';
</script>
