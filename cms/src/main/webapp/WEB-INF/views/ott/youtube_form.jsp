<!DOCTYPE html>
<%@ include file="/common/common.jsp" %>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8" %>
<head>
  <title>Ott Banner</title>
  <link href="${resourceServerUrl}/resources/css/ott/youtube.css?vs=${timestamp}" rel="stylesheet" type="text/css">
  <link href="${resourceServerUrl}/resources/bootstrap/css/plugins/tag/bootstrap-tag-cloud.css?vs=${timestamp}" rel="stylesheet">
  <style>
  </style>
</head>

<div class="row">
  <div class="col-md-12">
    <h3 class="page-header">OTT Youtube Banner</h3>
  </div>
</div>

<form:form modelAttribute="ottBanner" method="post" enctype="multipart/form-data">
  <form:hidden path="id"/>

  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <label>Ott Banner Type</label>
        <form:input path="type" cssClass="form-control input-sm width300" readonly="true"/>
      </div>
    </div>

  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        <label>Start Date</label>
        <input type="text" class="form-control input-sm datetimepicker" readonly="readonly" name="startDt"
               value="<fmt:formatDate value="${ottBanner.startDt }" pattern="${dateTimePattern }" timeZone="${sessionTimezone }" />">
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        <label>End Date</label>
        <input type="text" class="form-control input-sm datetimepicker" readonly="readonly" name="endDt"
               value="<fmt:formatDate value="${ottBanner.endDt }" pattern="${dateTimePattern }" timeZone="${sessionTimezone }" />">
      </div>
    </div>
    <div class="col-md-4">
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <div class="form-group">
        <label>Youtube</label>
        <button class="btn btn-default btn-xs resource-add-btn" type="button" data-resourcename="youtube">
          <i class="fa fa-plus"></i>
        </button>
        <div id="resource-div" class="top15">
          <c:forEach var="item" items="${ottBanner.ottBannerImages}" varStatus="status">
            <div class="ott-item col-md-4" data-index="${status.index}">
              <div class="card card-horizontal">
                <input type="hidden" name="ottBannerImageList[${status.index}].id.ottBannerId" value="${ottBanner.id}"/>
<%--                <input type="hidden" name="ottBannerImageList[${status.index}].id.seq" value="${status.index}"/>--%>
                <input type="hidden" name="ottBannerImageList[${status.index}].ottBanner.id" value="${ottBanner.id}"/>
                <input type="hidden" name="ottBannerImageList[${status.index}].bannerImage" value="${item.bannerImage}"/>
                <input type="hidden" name="ottBannerImageList[${status.index}].type" value="${item.type}"/>

                <div class="droparea-horizontal">
                  <input type="file" name="ottBannerImageList[${status.index}].imageFile" class="drop" multiple>
                  <i class="fa fa-cloud-upload"></i>
                  <p style="margin-left: 100px;">Drop Files</p>
                  <span>
                    <c:if test="${fn:containsIgnoreCase(item.bannerImageUrl, 'http')}">
                      <img src="${item.bannerImageUrl}"/>
                    </c:if>
                  </span>
                </div>

                <div class="card-horizontal-delete">
                  <span class="card-delete"><label>X</label></span>
                </div>

                <div class="card-container">
                  <div class="row top10">
                    <div class="col-md-3" style="margin-left: -5px;" >
                      <input type="text" name="ottBannerImageList[${status.index}].id.seq" class="form-control input-sm"
                            value="${item.id.seq}" placeholder="idx"/>
                    </div>
                    <div class="col-md-9" style="padding-bottom: 12px; padding-left: 8px; padding-right: 8px;">
                      <div class="input-group">
                        <input type="text" class="form-control input-sm keyword-add-input" placeholder="Input Keyword"/>
                        <span class="input-group-btn">
                          <button class="btn btn-default btn-sm keyword-add-btn" type="button" data-index="${status.index}" data-bannertype="dynamic">
                            <i class="fa fa-plus"></i>
                          </button>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <ul class="tag-cloud-info" style="margin-left: -32px;">
                      <c:forEach var="keyword" items="${item.youtubeKeywords}">
                        <li class="tag-cloud tag-cloud-info">${keyword}<input type="hidden"
                                                                               name="ottBannerImageList[${status.index}].youtubeKeywordsTemp" value="${keyword}" /></li>
                      </c:forEach>
                    </ul>
                  </div>
                </div>

              </div>
            </div>

          </c:forEach>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <div class="pull-right">
        <button type="submit" class="btn btn-custom-default btn-sm">Save</button>
        <a href="list?${ottBannerSearchQuery }" class="btn btn-custom-default btn-sm">List</a>
      </div>
    </div>
  </div>
  <br>
  <br>
</form:form>

<!-- Youtube type -->
<div id="youtube-banner-template" style="display: none;">
  <div class="ott-item col-md-4" data-index="%index%">
    <div class="card card-horizontal">
      <input type="hidden" name="ottBannerImageList[%index%].id.ottBannerId" value="${ottBanner.id}"/>
      <input type="hidden" name="ottBannerImageList[%index%].ottBanner.id" value="${ottBanner.id}"/>
      <input type="hidden" name="ottBannerImageList[%index%].bannerImage" value=""/>
      <input type="hidden" name="ottBannerImageList[%index%].type" value="YOUTUBE"/>

      <div class="droparea-horizontal">
        <input type="file" name="ottBannerImageList[%index%].imageFile" class="drop" multiple>
        <i class="fa fa-cloud-upload"></i>
        <p style="">Drop Files</p>
        <span>
        </span>
      </div>

      <div class="card-horizontal-delete">
        <span class="card-delete"><label>X</label></span>
      </div>

      <div class="card-container">
        <div class="row top10">
          <div class="col-md-3" style="margin-left: -5px;">
            <input type="text" name="ottBannerImageList[%index%].id.seq" class="form-control input-sm" value="" placeholder="idx"/>
          </div>
          <div class="col-md-9" style="padding-bottom: 12px; padding-left: 8px; padding-right: 8px;">
            <div class="input-group">
              <input type="text" class="form-control input-sm keyword-add-input" placeholder="Input Keyword"/>
              <span class="input-group-btn">
                <button class="btn btn-default btn-sm keyword-add-btn" type="button" data-index="%index%" data-bannertype="dynamic">
                  <i class="fa fa-plus"></i>
                </button>
              </span>
            </div>
          </div>
        </div>
        <div class="row">
          <ul class="tag-cloud-info" style="margin-left: -32px;"></ul>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  var queryString = '${ottBannerSearchQuery}';
  $(function () {
    $('.datetimepicker').datetimepicker({
      format: 'YYYY/MM/DD HH:mm:ss'
    });

  });
</script>

<jsp:include page="ott_left_menu.jsp"/>
<script src="${RESOURCE_SERVER_URL}/resources/scripts/ott/youtube_form.js?vs=${timestamp}"></script>
<script src="${RESOURCE_SERVER_URL}/resources/scripts/ott/ott_form_common.js?vs=${timestamp}"></script>
<script src="${RESOURCE_SERVER_URL}/resources/bootstrap/js/plugins/datepicker/bootstrap-moment.js?vs=${timestamp}"></script>
<script src="${RESOURCE_SERVER_URL}/resources/bootstrap/js/plugins/datepicker/bootstrap-datetimepicker.min.js?vs=${timestamp}"></script>



