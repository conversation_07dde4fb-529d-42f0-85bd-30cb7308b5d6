package com.laive.cms.ott.youtube;

import com.laive.cms.ott.OttBannerImageService;
import com.laive.core.base.BaseController;
import com.laive.core.base.ResponseVO;
import com.laive.core.domain.YoutubeCategory;
import com.laive.core.domain.YoutubeChannel;
import com.laive.core.exception.ParameterException;
import com.laive.core.service.YoutubeCategoryManager;
import com.laive.core.service.YoutubeChannelManager;
import com.laive.core.util.CollectionUtils;
import com.laive.core.util.DateUtil;
import com.laive.core.util.FileUtil;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.YoutubeCategoryListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.bind.support.SessionStatus;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.beans.PropertyEditorSupport;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Slf4j
@Controller
@RequestMapping("/youtube-category")
@SessionAttributes(value = {"youtubeCategory"})
public class YoutubeSpecialController extends BaseController {

  @Autowired
  private YoutubeCategoryManager youtubeCategoryManager;

  @Autowired
  private YoutubeChannelManager youtubeChannelManager;

  @Autowired
  private OttBannerImageService ottBannerImageService;

  @InitBinder
  public void initBinder(WebDataBinder binder) {
    SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.getDateTimePattern());
    binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    binder.registerCustomEditor(MultipartFile.class, new PropertyEditorSupport() {

      @Override
      public void setAsText(String text) {
        setValue(null);
      }
    });
  }

  @PreAuthorize("hasPermission('Product', 'READ')")
  @RequestMapping("/list")
  public String list(YoutubeCategoryListVO listVO, Model model, HttpServletRequest request) {
    request.getSession().setAttribute("youtubeCategorySearchQuery", request.getQueryString());
    listVO.getSearch().setPlatformId(getCurrentAdmin().getPlatform().getId());
    listVO = youtubeCategoryManager.getListVO(listVO);
    model.addAttribute("listVO", listVO);

    return "ott/youtube_category_list";
  }

  @PreAuthorize("hasPermission('Product', 'WRITE')")
  @RequestMapping(value = "/form", method = RequestMethod.GET)
  public String form(Model model) {

    YoutubeCategory youtubeCategory = new YoutubeCategory();
    youtubeCategory.setPlatform(getCurrentAdmin().getPlatform());
    model.addAttribute("youtubeCategory", youtubeCategory);

    return "ott/youtube_category_form";
  }

  @PreAuthorize("hasPermission('Product', 'READ')")
  @RequestMapping(value = "/view", method = RequestMethod.GET)
  public String view(@RequestParam String id, Model model) {

    YoutubeCategory youtubeCategory = youtubeCategoryManager.get(id);
    model.addAttribute("youtubeCategory", youtubeCategory);
    return "ott/youtube_category_form";
  }

  @PreAuthorize("hasPermission('Product', 'WRITE')")
  @PostMapping(value = "/delete")
  @ResponseBody
  public ResponseVO delete(String[] youtubeCategoryIds) {
    if (ArrayUtils.isEmpty(youtubeCategoryIds)) {
      throw new ParameterException();
    }

    for (String id : youtubeCategoryIds) {
      try {
        youtubeCategoryManager.remove(id);
      } catch (Exception e) {
        log.error(e.getMessage());
      }
    }
    return ResponseVO.ok();
  }

  @PreAuthorize("hasPermission('Product', 'WRITE')")
  @PostMapping(value = "/save")
  @ResponseBody
  public ResponseVO save(@ModelAttribute YoutubeCategory youtubeCategory, BindingResult bindingResult, SessionStatus sessionStatus) {

    checkBindingResult(bindingResult);

    List<YoutubeChannel> youtubeChannelSaveList = youtubeCategory.getYoutubeChannelSaveList();

    if (StringUtils.isBlank(youtubeCategory.getId())) {
      Integer maxSortSeq = youtubeCategoryManager.getMaxSeq(getCurrentAdmin().getPlatform().getId());
      youtubeCategory.setSortSeq(maxSortSeq + 1);
    }
    youtubeCategory = youtubeCategoryManager.save(youtubeCategory);

    if (CollectionUtils.isNotEmpty(youtubeChannelSaveList)) {
      for (YoutubeChannel youtubeChannel : youtubeChannelSaveList) {
        if (StringUtils.isBlank(youtubeChannel.getName())) {
          continue;
        }
        MultipartFile imageFile = youtubeChannel.getImageFile();
        youtubeChannel.setYoutubeCategory(youtubeCategory);
        youtubeChannel = youtubeChannelManager.merge(youtubeChannel);

        if (imageFile != null && !imageFile.isEmpty()) {
          File uploadedFile = FileUtil.uploadWithUniqueName(imageFile, youtubeChannel.getUploadPath());
          ottBannerImageService.processImageResize(youtubeChannel, uploadedFile.getName());
          youtubeChannel.setImage(uploadedFile.getName());
          youtubeChannelManager.merge(youtubeChannel);
        }
      }
    }
    sessionStatus.setComplete();
    return ResponseVO.ok();
  }


  @PreAuthorize("hasPermission('Product', 'WRITE')")
  @PostMapping(value = "/saveOrder")
  @ResponseBody
  public ResponseVO saveOrder(String[] sortCategoryIds, Integer[] sortSeqs ) {
    if (ArrayUtils.isEmpty(sortCategoryIds)) {
      throw new ParameterException();
    }

    int i =0;
    for (String id : sortCategoryIds) {
      try {
        YoutubeCategory youtubeCategory = youtubeCategoryManager.get(id);
        youtubeCategory.setSortSeq(sortSeqs[i++]);
        youtubeCategoryManager.merge(youtubeCategory);
      } catch (Exception e) {
        log.error(e.getMessage());
      }
    }
    return ResponseVO.ok();
  }


}
