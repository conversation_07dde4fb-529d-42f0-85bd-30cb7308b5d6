package com.laive.cms.settings;

import com.laive.cms.common.Menu;
import com.laive.core.base.BaseController;
import com.laive.core.base.ResponseVO;
import com.laive.core.domain.Encode;
import com.laive.core.service.EncodeManager;
import com.laive.core.util.ParameterUtil;
import com.laive.core.vo.EncodeListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.bind.support.SessionStatus;

import javax.inject.Inject;

@Slf4j
@Controller
@SessionAttributes(value = { "encode" })
public class EncodeController extends BaseController {

  @Inject
  private EncodeManager encodeManager;

  @PreAuthorize("hasPermission('Settings', 'READ')")
  @RequestMapping(value = "/settings/encode", method = RequestMethod.GET)
  @Menu({ 10, 0 })
  public String list(EncodeListVO listVO, Model model) {
    listVO = encodeManager.getListVO(listVO);
    model.addAttribute("listVO", listVO);
    return "settings/encode-list";
  }

  @PreAuthorize("hasPermission('Settings', 'WRITE')")
  @RequestMapping(value = "/settings/encode", method = RequestMethod.POST)
  @ResponseBody
  public ResponseVO save(Encode encode, SessionStatus sessionStatus) {
    encode.setResolution();
    encodeManager.merge(encode);
    sessionStatus.setComplete();
    return ResponseVO.ok();
  }

  @PreAuthorize("hasPermission('Settings', 'WRITE')")
  @RequestMapping(value = "/settings/encodeToggle", method = RequestMethod.POST)
  @ResponseBody
  public ResponseVO toggle(String id) {
    ParameterUtil.checkParameterEmpty(id);
    encodeManager.toggle(id);
    return ResponseVO.ok();
  }

  @PreAuthorize("hasPermission('Settings', 'READ')")
  @RequestMapping(value = "/settings/encode/{id}", method = RequestMethod.GET)
  @ResponseBody
  public Encode get(@PathVariable String id, Model model) {
    Encode encode = encodeManager.get(id);
    model.addAttribute("encode", encode);
    return encode;
  }

}
