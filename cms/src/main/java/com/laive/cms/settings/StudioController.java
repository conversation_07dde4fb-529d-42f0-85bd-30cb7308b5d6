package com.laive.cms.settings;

import java.text.SimpleDateFormat;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import com.laive.cms.common.Menu;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.laive.core.base.BaseController;
import com.laive.core.base.ResponseVO;
import com.laive.core.domain.calculation.Studio;
import com.laive.core.service.StudioManager;
import com.laive.core.util.DateUtil;
import com.laive.core.vo.StudioListVO;

@Slf4j
@Controller
@RequestMapping("/settings")
public class StudioController extends BaseController {

  @InitBinder
  public void initBinder(WebDataBinder binder) {
    SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.getDatePattern(LocaleContextHolder.getLocale()));
    binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
  }

  @Autowired
  private StudioManager studioManager;

  @PreAuthorize("hasPermission('Settings', 'READ')")
  @Menu({ 10, 8 })
  @RequestMapping(value = "/studio")
  public String list(StudioListVO listVO, Model model, HttpServletRequest request) {
    request.getSession().setAttribute("contractSearchQuery", request.getQueryString());
    //    listVO.setPlatformId(getCurrentAdmin().getPlatform().getId());

    listVO = studioManager.getListVO(listVO);

    model.addAttribute("listVO", listVO);
    return "settings/studio-list";
  }

  @PreAuthorize("hasPermission('Settings', 'WRITE')")
  @RequestMapping(value = "/studio_create")
  @ResponseBody
  public ResponseVO create(Studio studio) {
    studioManager.merge(studio);
    return ResponseVO.ok();
  }

  @PreAuthorize("hasPermission('Settings', 'WRITE')")
  @RequestMapping("/studio_delete")
  @ResponseBody
  public ResponseVO delete(String[] ids) {
    for (String id : ids) {
      studioManager.remove(id);
    }
    return ResponseVO.ok();
  }

}
