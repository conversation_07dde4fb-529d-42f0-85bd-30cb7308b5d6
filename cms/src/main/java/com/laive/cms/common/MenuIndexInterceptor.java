package com.laive.cms.common;

import com.laive.core.constants.Constants;
import com.laive.core.service.MenuManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.WebContentInterceptor;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public class MenuIndexInterceptor extends WebContentInterceptor {

  @Autowired
  private MenuManager menuManager;

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws ServletException {
    Map<String, String> mainMenuRequestUriMap = menuManager.getRequestUriMenuMap();
    String requestUri = request.getRequestURI();
    if (mainMenuRequestUriMap.containsKey(requestUri)) {
      request.getSession().setAttribute(Constants.SELECTED_MENU_ID, mainMenuRequestUriMap.get(requestUri));
    }
    return super.preHandle(request, response, handler);
  }

}
