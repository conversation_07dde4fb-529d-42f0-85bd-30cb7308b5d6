package com.laive.cms.batch;

import java.util.Date;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.laive.core.domain.Badge;
import com.laive.core.domain.Platform;
import com.laive.core.domain.Vod;
import com.laive.core.service.BadgeManager;
import com.laive.core.service.PlatformManager;
import com.laive.core.service.VodManager;
import com.laive.core.service.WatchVodStatsManager;
import com.laive.core.util.ConfigUtil;
import com.laive.core.util.ServicePropertiesUtil;

@Component
@Slf4j
public class VodBadgeAssignBatch {

  @Autowired
  private ServicePropertiesUtil servicePropertiesUtil;

  @Autowired
  private PlatformManager platformManager;

  @Autowired
  private VodManager vodManager;

  @Autowired
  private BadgeManager badgeManager;

  @Autowired
  private WatchVodStatsManager watchVodStatsManager;

  @Autowired
  private ConfigUtil configUtil;

  @Scheduled(cron = "${vodBatchAssignScheduler.cron}")
  public void execute() {
    execute(new Date());
  }

  public void execute(Date date) {

    if (!servicePropertiesUtil.isVodBadgeAssignBatchSchedulerEnabled()) {
      if (log.isInfoEnabled())
        log.info("VodBadgeAssignBatch.enabled option not activated...");
      return;
    }

    List<Platform> platforms = platformManager.getAll();
    platforms.stream().forEach(platform -> {
      vodManager.clearBadges(platform.getId());
      assignNewBadge(platform.getId(), date);
      assignHotBadge(platform.getId(), date);
    });

  }

  private void assignNewBadge(String platformId, Date date) {
    Badge newBadge = badgeManager.getBadgeByType(Badge.BadgeType.NEW);
    List<Vod> newVods = vodManager.getTopNByPlatformOrderByApprovedDtDesc(platformId, date, configUtil.getNewBadgeVodCount());
    newVods.stream().forEach(nv -> {
      nv.setBadge(newBadge);
      vodManager.merge(nv);
    });

  }

  private void assignHotBadge(String platformId, Date date) {
    Badge hotBadge = badgeManager.getBadgeByType(Badge.BadgeType.HOT);
    List<Vod> hotVods = watchVodStatsManager.getTopNByPlatformOrderByWatchCount(platformId, date, configUtil.getHotBadgeVodCount());
    hotVods.stream().forEach(hv -> {
      hv.setBadge(hotBadge);
      vodManager.merge(hv);
    });
  }

}
