package com.laive.cms.batch;

import com.laive.core.service.WatchChannelLogManager;
import com.laive.core.util.CollectionUtils;
import com.laive.core.util.DateUtil;
import com.laive.core.util.ServicePropertiesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class WatchChannelLogDeleteBatch {

  @Autowired
  private ServicePropertiesUtil servicePropertiesUtil;

  @Autowired
  private WatchChannelLogManager watchChannelLogManager;


//  @Scheduled(cron = "${watchChannelLogDeleteBatch.cron}")
  // change to executeDelete()
  public void execute() {
    int beforeMonth = servicePropertiesUtil.getWatchChannelLogDeleteBeforeMonth();
    execute(DateUtil.addMonths(new Date(), -1 * beforeMonth));
  }

  public void execute(Date beforeDt) {
    if (!servicePropertiesUtil.isWatchChannelLogDeleteBatchEnabled())
      return;

    log.info(String.format("WatchChannelLogDeleteBatch start:%s", DateUtil.getDateString(new Date())));
    StopWatch stopWatch = new StopWatch();
    stopWatch.start();
    int deleteJobSize = servicePropertiesUtil.getWatchChannelLogDeleteJobSize();
    int deleteStepSize = servicePropertiesUtil.getWatchChannelLogDeleteStepSize();

    long deleteCount = deleteJobSize / deleteStepSize;
    if (deleteJobSize % deleteStepSize > 0 ) {
      deleteCount++;
    }

    for (int i = 0; i < deleteCount; i++) {
      List<String> watchChannelLogIds = watchChannelLogManager.getLogIdsByBeforeDtAndSize(beforeDt, deleteStepSize);
      if (CollectionUtils.isEmpty(watchChannelLogIds)) {
        return;
      }

      long deleteSuccessCount = watchChannelLogManager.deleteLogsByIds(watchChannelLogIds);
      log.info(String.format("WatchChannelLogDeleteBatch No : %d, delete Count : %d", i + 1, deleteSuccessCount));
    }
    stopWatch.stop();
    log.info(String.format("WatchChannelLogDeleteBatch end:%s", stopWatch.shortSummary()));

  }

  // added by rakie
  @Scheduled(cron = "${watchChannelLogDeleteBatch.cron}")
  public void executeDelete() {
    log.info("Watch Channel Log Delete Job start.....");

    // 1. 저장된 데이터중 가장 오래된 날짜를 구한다.
    Date oldestRawDate = watchChannelLogManager.getOldestRawDateFromWatchChannelLog();
    if (oldestRawDate == null) {
      log.info("Cannot get oldestRawDate");
      return;
    }

    // 2. 유지해야할 마지막 날짜를 만든다.
    int keepDayCount = servicePropertiesUtil.getDeleteKeepDayCount();
    Calendar keepingDayCal = Calendar.getInstance();
    keepingDayCal.add(Calendar.DATE, (-1) * keepDayCount);
    Date keepingDay = DateUtil.getZeroTime(keepingDayCal.getTime());

    if (keepingDay.before(oldestRawDate) || keepingDay.getTime() == oldestRawDate.getTime()) {
      log.info("There is no deleting data...");
      return;
    }

    // 3. 지울 날짜 계산.
    int jobDayCount = servicePropertiesUtil.getDeleteJobDayCount();
    Calendar jobEndDayCal = Calendar.getInstance();
    jobEndDayCal.setTime(oldestRawDate);
    jobEndDayCal.add(Calendar.DATE, jobDayCount);

    if (jobEndDayCal.getTime().after(keepingDay)) {
      log.info("Change end date to keeping date");
      jobEndDayCal.setTime(keepingDay);
    }

    Date jobEndDay = jobEndDayCal.getTime();

    log.info("Keeping date : " + keepingDay);
    log.info("Delete start date : " + oldestRawDate);
    log.info("Delete end date : " + jobEndDay);

    int delCount = servicePropertiesUtil.getDeleteCountOnce();
    int maxSqlTry = servicePropertiesUtil.getDeleteMaxSqlTryCount();
    executeDelete(oldestRawDate, jobEndDay, delCount, maxSqlTry);
    log.info("Watch Channel Log Delete Job end.....");

  }

  public void executeDelete(Date startDt, Date endDate, int delCount, int maxSqlTry) {
    Long deletedCount = 0L;
    Long totalDelCount = 0L;
    int jobCount = 0;
    do {
      deletedCount = watchChannelLogManager.removeRawData(startDt, endDate, delCount);
      totalDelCount += deletedCount;
      jobCount += 1;
      log.info("Delete old date : " + (delCount * jobCount));
      if (jobCount > maxSqlTry) {  // 3백만....
        log.info("The sql job count is " + jobCount + " so end deleting job");
        break;
      }

      try {
        Thread.sleep(100);
      }
      catch(Exception e) {}
    } while (deletedCount != null && deletedCount > 0);
    log.info("Total Delete old date : " + totalDelCount);
  }
}
