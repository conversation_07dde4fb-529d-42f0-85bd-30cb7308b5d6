package com.laive.cms.analytics;

import com.laive.cms.common.Menu;
import com.laive.core.base.BaseController;
import com.laive.core.constants.CommonCodeGroups;
import com.laive.core.domain.Channel;
import com.laive.core.service.*;
import com.laive.core.util.DateUtil;
import com.laive.core.util.ExcelUtil;
import com.laive.core.util.JsonUtils;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.ChannelRankingListVO;
import com.laive.core.vo.ChannelWatchStatsVO;
import com.laive.core.vo.WatchChannelStatsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Controller
@RequestMapping("/analytics")
public class AnalyticsChannelController extends BaseController {

    @Autowired
    private WatchChannelLogService watchChannelLogService;

    @Autowired
    private WatchChannelLogManager watchChannelLogManager;

    @Autowired
    private LookupManager lookupManager;

    @Autowired
    private ChannelManager channelManager;

    @Autowired
    private WatchChannelDailyStatManager watchChannelDailyStatManager;

    @Autowired
    private WatchChannelCountManager watchChannelCountManager;

    @Autowired
    private WatchChannelDurationManager watchChannelDurationManager;

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.getDatePattern(LocaleContextHolder.getLocale()));
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    @PreAuthorize("hasPermission('Analytics', 'READ')")
    @RequestMapping(value = "/channel", method = RequestMethod.GET)
    @Menu({5, 1})
    public String channel(WatchChannelStatsVO watchChannelStatsVO, Model model, HttpServletRequest request) {
        request.getSession().setAttribute("searchQuery", request.getQueryString());
        adjustSearchDate(watchChannelStatsVO);
        watchChannelStatsVO.getSearch().setTimezoneId(getCurrentAdmin().getTimezone());

        watchChannelStatsVO = watchChannelDailyStatManager.getStatsVO(watchChannelStatsVO);

        model.addAttribute("watchChannelStatsVO", watchChannelStatsVO);
        model.addAttribute("watchChannelStatsVOJsonString", JsonUtils.toString(watchChannelStatsVO));
        model.addAttribute("dataList", JsonUtils.toString(watchChannelStatsVO.getList()));

        model.addAttribute("channelRankingList", JsonUtils.toString(getChannelRankingListVO(watchChannelStatsVO)));

        List<ChannelWatchStatsVO.Data> channelDurationStatList =
                watchChannelDurationManager.getDurationStats(watchChannelStatsVO.getSearch());
        model.addAttribute("channelDurationStatList", channelDurationStatList);
        model.addAttribute("channelDurationTotalHour", getTotalHour(channelDurationStatList));

        if (StringUtils.isNotBlank(watchChannelStatsVO.getSearch().getPlatformId())) {
            List<Channel> channels = channelManager.getAllByPassChannelsByPlatformId(watchChannelStatsVO.getSearch().getPlatformId());
            model.addAttribute("channels", channels);
        }
        model.addAttribute("platforms", lookupManager.getPlatforms());
        model.addAttribute("deviceTypes", lookupManager.getCodes(CommonCodeGroups.AccessDeviceType));
        model.addAttribute("periodTypes", WatchChannelStatsVO.PeriodType.values());

        Date today = new Date();
        Date defaultStartDate = getDefaultStartDate(today);
        model.addAttribute("defaultStartDate", new SimpleDateFormat("yyyy/MM/dd").format(defaultStartDate));
        model.addAttribute("defaultEndDate", new SimpleDateFormat("yyyy/MM/dd").format(today));

        return "analytics/channel";
    }

    private Date getDefaultStartDate(Date today) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return calendar.getTime();
    }

    private double getTotalHour(List<ChannelWatchStatsVO.Data> channelDurationStatList) {
        double sumHour = 0d;
        for (ChannelWatchStatsVO.Data data : channelDurationStatList) {
            sumHour += data.getWatchHour();
        }
        return sumHour;
    }

    @PreAuthorize("hasPermission('Analytics', 'READ')")
    @RequestMapping("/channel-excel")
    public void excel(WatchChannelStatsVO watchChannelStatsVO, HttpServletRequest request, HttpServletResponse response) {
        watchChannelStatsVO.setPageSize(0);
        if (log.isDebugEnabled())
            log.debug(String.format("search::%s", watchChannelStatsVO.getSearch()));
        watchChannelStatsVO.getSearch().setTimezoneId(getCurrentAdmin().getTimezone());
        adjustSearchDate(watchChannelStatsVO);

        watchChannelStatsVO = watchChannelDailyStatManager.getStatsVO(watchChannelStatsVO);
        ExcelUtil.download(request, response, watchChannelStatsVO, "channel-log-excel");
    }


    private void adjustSearchDate(WatchChannelStatsVO vo) {
        vo.getSearch().setFromDate(DateUtil.adjustAndTrucate(vo.getSearch().getFromDate(), getCurrentAdmin().getTimezone()));
        vo.getSearch().setToDate(DateUtil.adjustAndTrucate(vo.getSearch().getToDate(), getCurrentAdmin().getTimezone()));

    }

    private List<ChannelRankingListVO.Data> getChannelRankingListVO(WatchChannelStatsVO watchChannelStatsVO) {
        ChannelRankingListVO listVO = new ChannelRankingListVO();
        BeanUtils.copyProperties(watchChannelStatsVO.getSearch(), listVO.getSearch(), new String[]{});
        listVO.setPageSize(10);

        listVO = watchChannelCountManager.getRankingListVO(listVO);
        return listVO.getList();
    }

    @PreAuthorize("hasPermission('Analytics', 'READ')")
    @RequestMapping(value = "/channel/excel", method = RequestMethod.GET)
    public void channelExcel(WatchChannelStatsVO watchChannelStatsVO, HttpServletRequest request, HttpServletResponse response) {
        adjustSearchDate(watchChannelStatsVO);
        watchChannelStatsVO = watchChannelLogService.getWatchChannelStatsVO(watchChannelStatsVO);
        ExcelUtil.download(request, response, watchChannelStatsVO, "channel-stats");
    }

    @PreAuthorize("hasPermission('Analytics', 'READ')")
    @RequestMapping(value = "/channelRanking/excel", method = RequestMethod.GET)
    public void channelRankingExcel(ChannelRankingListVO channelRankingListVO, HttpServletRequest request,
                                    HttpServletResponse response) {
        adjustSearchDate(channelRankingListVO);
        channelRankingListVO = watchChannelLogService.getChannelRankingListVO(channelRankingListVO);
        ExcelUtil.download(request, response, channelRankingListVO, "channel-ranking");
    }

    private void adjustSearchDate(ChannelRankingListVO listVO) {
        listVO.getSearch().setFromDate(DateUtil.adjustAndTrucate(listVO.getSearch().getFromDate(), getCurrentAdmin().getTimezone()));
        listVO.getSearch().setToDate(DateUtil.adjustAndTrucate(listVO.getSearch().getToDate(), getCurrentAdmin().getTimezone()));
    }

}
