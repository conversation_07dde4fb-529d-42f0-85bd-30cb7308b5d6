package com.laive.cms.notification;

import java.text.SimpleDateFormat;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.SessionAttributes;

import com.laive.cms.common.Menu;
import com.laive.core.base.BaseController;
import com.laive.core.domain.Feedback;
import com.laive.core.service.FeedbackManager;
import com.laive.core.util.DateUtil;
import com.laive.core.vo.FeedbackListVO;

@Controller
@RequestMapping("/feedback")
@SessionAttributes("feedback")
public class FeedbackController extends BaseController {

  @Autowired
  private FeedbackManager feedbackManager;

  @InitBinder
  public void initBinder(WebDataBinder binder) {
    SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.getDatePattern());
    binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
  }

  @PreAuthorize("hasPermission('Notification', 'READ')")
  @RequestMapping("/list")
  @Menu({ 8, 2 })
  public String list(FeedbackListVO listVO, Model model, HttpServletRequest request) {
    request.getSession().setAttribute("feedbackSearchQuery", request.getQueryString());
    listVO = feedbackManager.getListVO(listVO);
    model.addAttribute("listVO", listVO);
    return "notification/feedback-list";
  }

  @PreAuthorize("hasPermission('Notification', 'READ')")
  @RequestMapping("/view")
  public String view(@RequestParam String id, Model model) {
    Feedback feedback = feedbackManager.get(id);
    model.addAttribute("feedback", feedback);
    return "notification/feedback-form";
  }

}
