package com.laive.cms.banner;

import java.beans.PropertyEditorSupport;
import java.io.File;
import java.util.List;

import com.laive.core.domain.Category;
import com.laive.core.service.CategoryManager;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.bind.support.SessionStatus;
import org.springframework.web.multipart.MultipartFile;

import com.laive.cms.common.Menu;
import com.laive.core.base.BaseController;
import com.laive.core.base.ResponseVO;
import com.laive.core.constants.ImageConstants;
import com.laive.core.domain.Banner;
import com.laive.core.domain.BannerImage;
import com.laive.core.service.BannerImageManager;
import com.laive.core.service.BannerManager;
import com.laive.core.util.FileUtil;
import com.laive.core.util.StringUtils;

@Slf4j
@Controller
@RequestMapping("/banner")
@SessionAttributes("mainDynamicBanner")
public class BannerDynamicMainController extends BaseController {

  @Autowired
  private BannerManager bannerManager;

  @Autowired
  private BannerImageManager bannerImageManager;

  @Autowired
  private BannerImageService bannerImageService;

  @Autowired
  private CategoryManager categoryManager;

  @InitBinder
  public void initBinder(WebDataBinder binder) throws Exception {
    binder.registerCustomEditor(MultipartFile.class, new PropertyEditorSupport() {

      @Override
      public void setAsText(String text) {
        setValue(null);
      }
    });
  }

  @PreAuthorize("hasPermission('Product', 'WRITE')")
  @RequestMapping(value = "/dynamic_main/form", method = RequestMethod.GET)
  @Menu({ 1, 3 })
  public String form(Model model) {

    Banner mainDynamicBanner = new Banner();
    mainDynamicBanner.setPlatform(getCurrentAdmin().getPlatform());
    mainDynamicBanner.setType(Banner.BannerType.DYNAMIC_MAIN);
    model.addAttribute("mainDynamicBanner", mainDynamicBanner);

    List<Category> categoryList = categoryManager.getLeafCategories(getCurrentAdmin().getPlatform().getId());
    model.addAttribute("categoryList", categoryList);
    return "banner/dynamic_main";
  }

  @PreAuthorize("hasPermission('Product', 'READ')")
  @RequestMapping(value = "/dynamic_main/view", method = RequestMethod.GET)
  @Menu({ 1, 3 })
  public String view(@RequestParam String bannerId, Model model) {

    Banner mainDynamicBanner = bannerManager.get(bannerId);
    model.addAttribute("mainDynamicBanner", mainDynamicBanner);

    List<Category> categoryList = categoryManager.getLeafCategories(getCurrentAdmin().getPlatform().getId());
    model.addAttribute("categoryList", categoryList);
    return "banner/dynamic_main";
  }

  @PreAuthorize("hasPermission('Product', 'WRITE')")
  @RequestMapping(value = "/dynamic_main", method = RequestMethod.POST)
  @ResponseBody
  public ResponseVO save(@ModelAttribute Banner mainDynamicBanner, BindingResult bindingResult, SessionStatus sessionStatus) {

    log.debug("main save ...");
    if (bindingResult.hasErrors()) {
      List<FieldError> errors = bindingResult.getFieldErrors();
      for (FieldError error : errors) {
        ResponseVO.BindingError bindingError = new ResponseVO.BindingError();
        bindingError.setField(error.getField());
        log.debug(error.getField());
      }
    }

    List<BannerImage> dynamicList = mainDynamicBanner.getFixedList();

    mainDynamicBanner.getBannerImages().clear();
    mainDynamicBanner.prePersist();
    mainDynamicBanner = bannerManager.merge(mainDynamicBanner);

    int i = 0;
    for (BannerImage bi : dynamicList) {
      if (bi == null || bi.getBanner() == null)
        continue;

      MultipartFile imageFile = bi.getImageFile();
      if (bi.getVod() != null && StringUtils.isBlank(bi.getVod().getId()))
        bi.setVod(null);

      bi.setBanner(mainDynamicBanner);
      bi.getId().setBannerId(mainDynamicBanner.getId());
      bi.getId().setSeq(i++);
      bi = bannerImageManager.merge(bi);
      if (imageFile != null && !imageFile.isEmpty()) {
        File uploadedFile = FileUtil.uploadWithUniqueName(imageFile, bi.getUploadPath());
        bannerImageService.processImageResize(bi.getUploadPath(), uploadedFile.getName(), ImageConstants.BANNER_MAIN_DYNAMIC_IMAGE_WIDTH,
            ImageConstants.BANNER_MAIN_DYNAMIC_IMAGE_HEIGHT);
        bi.setBannerImage(uploadedFile.getName());
      }
      bannerImageManager.merge(bi);
    }
    sessionStatus.setComplete();
    return ResponseVO.ok();
  }

}
