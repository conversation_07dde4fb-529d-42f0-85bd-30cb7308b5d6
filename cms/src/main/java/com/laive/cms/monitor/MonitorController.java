package com.laive.cms.monitor;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;

import com.laive.cms.common.Menu;
import com.laive.commons.core.util.StringUtils;
import com.laive.core.base.BaseController;
import com.laive.core.base.ResponseVO;
import com.laive.core.domain.Monitor;
import com.laive.core.service.MonitorManager;

@Controller
@RequestMapping("/monitor")
@SessionAttributes("monitor")
public class MonitorController extends BaseController {

  @Autowired
  private MonitorManager monitorManager;

  @PreAuthorize("hasPermission('Channel', 'READ')")
  @RequestMapping("/view")
  @Menu({ 3, 3 })
  public String view(@RequestParam(required = false) String id, Model model) {

    Monitor monitor = StringUtils.isBlank(id) ? monitorManager.getLatest() : monitorManager.get(id);
    if (monitor == null) {
      monitor = new Monitor();
      monitor.setPlatform(getCurrentAdmin().getPlatform());
    }

    model.addAttribute("monitor", monitor);
    model.addAttribute("monitorList", monitorManager.getList(getCurrentAdmin().getPlatform().getId()));
    return "monitor/view";
  }

  @PreAuthorize("hasPermission('Channel', 'READ')")
  @RequestMapping(value = "/list")
  @ResponseBody
  public List<Monitor> list() {
    List<Monitor> list = monitorManager.getList(getCurrentAdmin().getPlatform().getId());
    return list;
  }

  @PreAuthorize("hasPermission('Channel', 'WRITE')")
  @RequestMapping(value = "/save")
  @ResponseBody
  public ResponseVO save(Monitor monitor) {
    monitorManager.save(monitor);
    return ResponseVO.ok();
  }

  @PreAuthorize("hasPermission('Channel', 'WRITE')")
  @RequestMapping(value = "/create")
  @ResponseBody
  public ResponseVO create(@RequestParam String name) {
    Monitor monitor = new Monitor();
    monitor.setName(name);
    monitor.setPlatform(getCurrentAdmin().getPlatform());
    monitor = monitorManager.merge(monitor);
    return ResponseVO.okWithId(monitor.getId());
  }

  @PreAuthorize("hasPermission('Channel', 'WRITE')")
  @RequestMapping(value = "/delete")
  @ResponseBody
  public ResponseVO delete(@RequestParam String monitorId) {
    monitorManager.remove(monitorId);
    return ResponseVO.ok();
  }

  @PreAuthorize("hasPermission('Channel', 'WRITE')")
  @RequestMapping(value = "/channel_add")
  @ResponseBody
  public ResponseVO addChannel(@RequestParam String monitorId, @RequestParam String channelIds) {

    List<String> channelIdList = new ArrayList<String>();
    if (StringUtils.contains(channelIds, ",")) {
      String[] idArr = StringUtils.split(channelIds, ",");
      for (String id : idArr) {
        if (StringUtils.isBlank(id))
          continue;
        channelIdList.add(StringUtils.trim(id));
      }
    } else {
      channelIdList.add(channelIds);
    }

    monitorManager.addChannel(monitorId, channelIdList);
    return ResponseVO.ok();
  }

}
