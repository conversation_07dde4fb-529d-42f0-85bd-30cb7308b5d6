package com.laive.cms.library;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.laive.core.base.BaseController;
import com.laive.core.base.ResponseVO;
import com.laive.core.domain.LibraryGroup;
import com.laive.core.domain.calculation.Studio;
import com.laive.core.exception.BindingException;
import com.laive.core.service.LibraryGroupManager;
import com.laive.core.service.LibraryManager;
import com.laive.core.service.StudioManager;
import com.laive.core.util.ExcelUtil;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.LibraryExcelListVO;

@Slf4j
@Controller
@RequestMapping("/libraryExcel")
public class LibraryExcelController extends BaseController {

  @Autowired
  private LibraryManager libraryManager;

  @Autowired
  private LibraryGroupManager libraryGroupManager;

  @Autowired
  private StudioManager studioManager;

  @RequestMapping(value = "/download")
  public void downloadExcel(LibraryExcelListVO listVO, HttpServletRequest request, HttpServletResponse response) {
    listVO = libraryManager.getLibraryExcelListVO(listVO);
    String filename = "library-list";
    if (StringUtils.isNotBlank(listVO.getLibraryGroupId())) {
      LibraryGroup libraryGroup = libraryGroupManager.get(listVO.getLibraryGroupId());
      filename = String.format("%s-(%s)", filename, libraryGroup.getName());
    }
    ExcelUtil.download(request, response, listVO, filename);
  }

  @RequestMapping(value = "/upload", method = RequestMethod.POST)
  @ResponseBody
  public ResponseVO uploadExcel(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request, HttpServletResponse response) {
    log.debug("libraryExcel upload");


    try {
      Workbook workbook;
      if (multipartFile.getOriginalFilename().endsWith("xlsx")) {
        workbook = new XSSFWorkbook(multipartFile.getInputStream());
      } else if (multipartFile.getOriginalFilename().endsWith("xls")) {
        workbook = new HSSFWorkbook(multipartFile.getInputStream());
      } else {
        throw new IllegalArgumentException("The specified file is not Excel file");
      }

      Sheet sheet = workbook.getSheetAt(0);
      int totalRowCount = sheet.getPhysicalNumberOfRows();

      Map<String, Studio> studioMap = getStudioMap();

      for (int rowIndex = 1; rowIndex < totalRowCount; rowIndex++) {
        Row row = sheet.getRow(rowIndex);
        checkValidation(row);
        libraryManager.saveLibraryFromExcel(row, getCurrentAdmin(), studioMap);
        log.debug(String.format("[%d] finished.", rowIndex));
      }
      return ResponseVO.ok();
    } catch (BindingException be) {
      throw be;
    } catch (IOException ioex) {
      throw new RuntimeException(ioex);
    }
  }

  private void checkValidation(Row row) {
    // row.getCell(2) -> library title
    if (ExcelUtil.getStringCellValue(row.getCell(2)).length() > 200) {
      throw new BindingException("Library Name is Over 200 characters.");
    }
  }

  public Map<String, Studio> getStudioMap() {
    List<Studio> studioList = studioManager.getAll();
    return studioList.stream().collect(Collectors.toMap(Studio::getName, studio -> studio));
  }
}
