package com.laive.cms.library;

import com.itextpdf.text.Document;
import com.itextpdf.text.Element;
import com.itextpdf.text.Image;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.laive.commons.core.util.StringUtils;
import com.laive.core.domain.Library;
import com.laive.core.web.view.AbstractPdfView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public class LibraryPdfView extends AbstractPdfView {

  protected void buildPdfDocument(Map<String, Object> model, Document document, PdfWriter writer, HttpServletRequest req,
      HttpServletResponse resp) throws Exception {

    Library library = (Library) model.get("library");

    // Table
    PdfPTable table = new PdfPTable(2);
    table.setWidthPercentage(100);
    table.setWidths(new float[]{1f, 2f});

    // Image
    PdfPCell imageCell = null;
    String posterImagePath = library.getPosterPortraitPath();
    if (StringUtils.isNotEmpty(posterImagePath)) {
      Image posterImage = Image.getInstance(posterImagePath);
      imageCell = new PdfPCell(posterImage, true);

    } else {
      imageCell = new PdfPCell(new Paragraph("no image"));
    }
    imageCell.setBorder(PdfPCell.NO_BORDER);
    table.addCell(imageCell);

    // Description
    PdfPTable descTable = new PdfPTable(2);
    descTable.setWidthPercentage(100);
    descTable.setWidths(new float[]{3f, 7f});
    PdfPCell cellLabel = new PdfPCell();
    cellLabel.setBorder(PdfPCell.NO_BORDER);
    cellLabel.setHorizontalAlignment(Element.ALIGN_RIGHT);
    cellLabel.setLeading(15f, 0f);
    PdfPCell cellValue = new PdfPCell();
    cellValue.setBorder(PdfPCell.NO_BORDER);
    cellValue.setLeading(15f, 0f);
    cellValue.setHorizontalAlignment(Element.ALIGN_LEFT);

    addCell(descTable, cellLabel, cellValue, "Title", library.getName());
    addCell(descTable, cellLabel, cellValue, "Studio", library.getStudioName());
    addCell(descTable, cellLabel, cellValue, "Release Year", String.valueOf(library.getReleaseYear()));
    addCell(descTable, cellLabel, cellValue, "Run time", library.getFormattedDuration());
//    addCell(descTable, cellLabel, cellValue, "Start Date", library.getFormattedLicenseStartDate());
//    addCell(descTable, cellLabel, cellValue, "End Date", library.getFormattedLicenseEndDate());
    addCell(descTable, cellLabel, cellValue, "Genre", getGenre(library));
    addCell(descTable, cellLabel, cellValue, "Director", getDirector(library));
    addCell(descTable, cellLabel, cellValue, "Cast", getCast(library));
    addCell(descTable, cellLabel, cellValue, "Short synopsis", library.getShortSynopsis());
    addCell(descTable, cellLabel, cellValue, "Long synopsis", library.getLongSynopsis());

    PdfPCell descCell = new PdfPCell(descTable);
    descCell.setBorder(PdfPCell.NO_BORDER);
    table.addCell(descCell);

    document.add(table);
    document.close();
  }

  private String getGenre(Library library) {
    return StringUtils.join(library.getGenreNames(), ",");
  }

  private String getCast(Library library) {
    return StringUtils.join(library.getLibraryCastNames(), ",");
  }

  private String getDirector(Library library) {
    return StringUtils.join(library.getLibraryCrewNames(), ",");
  }

  private void addCell(PdfPTable descTable, PdfPCell cellLabel, PdfPCell cellValue, String label, String value) {
    cellLabel.setPhrase(new Phrase(label));
    descTable.addCell(cellLabel);
    cellValue.setPhrase(new Phrase(value));
    descTable.addCell(cellValue);
  }

  protected Document createDocument() {
    Rectangle rectangle = new Rectangle(842,595);
    return new Document(rectangle);
  }

}