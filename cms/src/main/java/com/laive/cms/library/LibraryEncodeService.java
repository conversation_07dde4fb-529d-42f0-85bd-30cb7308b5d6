package com.laive.cms.library;

import com.laive.core.domain.Admin;
import com.laive.core.domain.Library;

public interface LibraryEncodeService {

  void encode(Admin admin, LibraryEncodeRequestVO requestVO);

  GetEncoderResponseVO.Encoder getEncoder();

  void encode(String libraryId, String encoderHost, Integer encoderPort, String encoderId, String key, String trailer,
      Integer encryption, Integer beginOffset, Integer endOffset, Integer previewOffset, String languagePipeDelimedString, Integer fairplay,
      Integer volume, String soundlanguageDelimedString, String sdar, String srcBucket, String targetBucket);

  Library createLibrary(String key, Admin admin, String encoderUrl, String encoderId, String encoderType, String libraryType);

  void packaging(String libraryId, String encoderHost, Integer encoderPort, Integer encryption, Integer fairplay);
}
