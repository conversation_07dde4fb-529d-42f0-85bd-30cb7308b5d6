package com.laive.cms.instance;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;

import com.laive.cms.common.Menu;
import com.laive.core.base.BaseController;
import com.laive.core.domain.InstanceType;
import com.laive.core.service.InstanceManager;
import com.laive.core.util.GmmanagerUtil;
import com.laive.core.vo.GmmanagerUsageVO;
import com.laive.core.vo.InstanceUsageVO;
import com.laive.core.vo.InstanceUsageVO.Period;
import com.laive.core.vo.InstanceUsageVO.StartPoint;

@Controller
@RequestMapping("/instance")
@SessionAttributes("instance")
public class InstanceUsageController extends BaseController {

  @Autowired
  private InstanceManager instanceManager;

  @Autowired
  private GmmanagerUtil gmmanagerUtil;

  @PreAuthorize("hasPermission('Instance', 'READ')")
  @RequestMapping("/usage")
  @Menu({ 6, 1 })
  public String encoderUsage(Model model, HttpServletRequest request) {

    model.addAttribute("instanceList", instanceManager.getListByType(InstanceType.values()));
    model.addAttribute("durationList", StartPoint.values());
    model.addAttribute("intervalList", Period.values());
    return "instance/usage";
  }

  @PreAuthorize("hasPermission('Instance', 'READ')")
  @RequestMapping(value = "/getUsages")
  @ResponseBody
  public InstanceUsageVO getUsages(InstanceUsageVO listVO) {

    GmmanagerUsageVO gmmanagerUsageVO = gmmanagerUtil.getInstanceUsage(listVO.getInstanceId(), listVO.getMetricName(), listVO.getPeriod(),
        listVO.getStartPoint(), listVO.getUnit());

    InstanceUsageVO instanceUsageVO = getInstanceUsageVO(gmmanagerUsageVO);
    return instanceUsageVO;
  }

  private InstanceUsageVO getInstanceUsageVO(GmmanagerUsageVO gmmanagerUsageVO) {

    InstanceUsageVO instanceUsageVO = new InstanceUsageVO();
    instanceUsageVO.setElement(String.format("%s-chart", gmmanagerUsageVO.getLabel()));
    instanceUsageVO.setXkey("datetime");
    instanceUsageVO.setYkeys(new String[] { "average", "maximum" });
    instanceUsageVO.setLabels(new String[] { "average", "maximum" });

    for (GmmanagerUsageVO.UsageData usageData : gmmanagerUsageVO.getDatapoints()) {
      Map<String, Object> data = new HashMap<String, Object>();
      data.put("datetime", usageData.getTimestamp());
      data.put("average", usageData.getAverage());
      data.put("maximum", usageData.getMaximum());
      instanceUsageVO.getList().add(data);
    }
    return instanceUsageVO;
  }

}
