package com.laive.cms.config;

import com.laive.cms.common.MenuIndexInterceptor;
import com.laive.core.util.ServicePropertiesUtil;
import lombok.extern.apachecommons.CommonsLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.orm.jpa.support.OpenEntityManagerInViewInterceptor;
import org.springframework.util.Assert;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import org.springframework.web.servlet.handler.SimpleUrlHandlerMapping;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;
import org.springframework.web.servlet.mvc.SimpleControllerHandlerAdapter;
import org.springframework.web.servlet.mvc.UrlFilenameViewController;
import org.springframework.web.servlet.view.BeanNameViewResolver;
import org.springframework.web.servlet.view.InternalResourceViewResolver;

import javax.inject.Inject;
import javax.persistence.EntityManagerFactory;
import java.util.Properties;

@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableWebMvc
@Import({ ViewConfig.class })
@Slf4j
public class WebMvcConfig extends WebMvcConfigurerAdapter {

  @Autowired
  private ServicePropertiesUtil servicePropertiesUtil;

  @Inject
  private EntityManagerFactory entityManagerFactory;

  @Autowired
  private Environment env;

  private static final Long MAX_UPLOAD_SIZE_DEFAULT = 1048576000L;

  @Override
  public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
    configurer.enable();
  }

  @Override
  public void addResourceHandlers(ResourceHandlerRegistry registry) {
    String storagePath = env.getProperty("storage.path");
    String uploadUri = env.getProperty("upload.uri");
    if (log.isTraceEnabled()) {
      log.trace(String.format("storage.path:%s", storagePath));
    }
    log.debug(String.format("storagePath:%s, uploadUri:%s", storagePath, uploadUri));
    Assert.hasText(storagePath, "storage.path(service.properties) is empty...");
    registry.addResourceHandler("/resources/**").addResourceLocations("/resources/");
    registry.addResourceHandler(uploadUri + "/**").addResourceLocations("file:" + storagePath + uploadUri + "/");
    registry.addResourceHandler("/storage/**").addResourceLocations("file:" + storagePath + "/");
    registry.addResourceHandler("/snap/**").addResourceLocations("file:" + storagePath + "/snap/");
    registry.addResourceHandler("/catchup/**").addResourceLocations("file:" + storagePath + "/catchup/");
  }

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(menuIndexInterceptor()).addPathPatterns("/**");
    registry.addWebRequestInterceptor(openEntityManagerInViewInterceptor());
  }

  @Bean
  public MenuIndexInterceptor menuIndexInterceptor() {
    return new MenuIndexInterceptor();
  }

  @Bean
  public OpenEntityManagerInViewInterceptor openEntityManagerInViewInterceptor() {
    OpenEntityManagerInViewInterceptor openEntityManagerInViewInterceptor = new OpenEntityManagerInViewInterceptor();
    openEntityManagerInViewInterceptor.setEntityManagerFactory(this.entityManagerFactory);
    Properties jpaProperties = new Properties();
    jpaProperties.setProperty("singleSession", "true");
    openEntityManagerInViewInterceptor.setJpaProperties(jpaProperties);
    return openEntityManagerInViewInterceptor;
  }

  @Bean
  public BeanNameViewResolver beanNameViewResolver() {
    BeanNameViewResolver beanNameViewResolver = new BeanNameViewResolver();
    beanNameViewResolver.setOrder(1);
    return beanNameViewResolver;
  }

  @Bean
  public ViewResolver viewResolver() {
    InternalResourceViewResolver viewResolver = new InternalResourceViewResolver();
    viewResolver.setPrefix("/WEB-INF/views/");
    viewResolver.setSuffix(".jsp");
    return viewResolver;
  }

  @Bean
  public LocaleResolver localeResolver() {
    return new SessionLocaleResolver();
  }

  @Bean
  public UrlFilenameViewController urlFilenameViewController() {
    return new UrlFilenameViewController();
  }

  @Bean
  public SimpleControllerHandlerAdapter simpleControllerHandlerAdapter() {
    return new SimpleControllerHandlerAdapter();
  }

  @Bean
  public SimpleUrlHandlerMapping urlMapping() {
    SimpleUrlHandlerMapping urlMapping = new SimpleUrlHandlerMapping();
    Properties mappings = new Properties();
    mappings.setProperty("/**/*.html", "urlFilenameViewController");
    urlMapping.setMappings(mappings);
    urlMapping.setOrder(1);
    return urlMapping;
  }

  @Bean
  public CommonsMultipartResolver multipartResolver() {
    CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
    multipartResolver.setMaxUploadSize(MAX_UPLOAD_SIZE_DEFAULT);
    return multipartResolver;
  }

}
