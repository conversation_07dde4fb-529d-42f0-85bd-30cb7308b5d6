package com.laive.cms.config;

import java.util.EnumSet;

import javax.servlet.DispatcherType;
import javax.servlet.Filter;
import javax.servlet.FilterRegistration.Dynamic;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;

import org.springframework.web.context.request.RequestContextListener;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.filter.DelegatingFilterProxy;
import org.springframework.web.filter.HiddenHttpMethodFilter;
import org.springframework.web.servlet.support.AbstractAnnotationConfigDispatcherServletInitializer;

import com.laive.commons.web.HttpServletRequestFilter;
import com.laive.core.web.filter.LocaleFilter;
import com.laive.core.web.filter.XSSFilter;
import com.laive.core.web.listener.CommonCodeCategoriesLoaderListener;
import com.laive.core.web.listener.StartupListener;
import com.opensymphony.sitemesh.webapp.SiteMeshFilter;

public class WebAppInitializer extends AbstractAnnotationConfigDispatcherServletInitializer {

  private static final String SPRING_SECURITY_FILTER_NAME = "springSecurityFilterChain";

  @Override
  public void onStartup(ServletContext servletContext) throws ServletException {
    super.onStartup(servletContext);
    addListener(servletContext);
    addSpringSecurityFilter(servletContext);
  }

  private void addListener(ServletContext servletContext) {
    servletContext.addListener(new RequestContextListener());
    servletContext.addListener(new StartupListener());
    servletContext.addListener(new CommonCodeCategoriesLoaderListener());
  }

  private void addSpringSecurityFilter(ServletContext servletContext) {
    String filterName = SPRING_SECURITY_FILTER_NAME;
    DelegatingFilterProxy filter = new DelegatingFilterProxy(SPRING_SECURITY_FILTER_NAME);

    Dynamic registration = servletContext.addFilter(filterName, filter);
    if (registration == null) {
      throw new IllegalStateException("Duplicate Filter registration for '" + filterName + "'. Check to ensure the Filter is only configured once.");
    }
    registration.setAsyncSupported(true);
    EnumSet<DispatcherType> dispatcherTypes = EnumSet.of(DispatcherType.REQUEST, DispatcherType.ERROR, DispatcherType.ASYNC);
    registration.addMappingForUrlPatterns(dispatcherTypes, false, "/*");
  }

  @Override
  protected Class<?>[] getRootConfigClasses() {
    return new Class[] { MainConfig.class, SecurityConfig.class, MethodSecurityConfig.class, WebMvcConfig.class, ValidatorConfig.class };
  }

  @Override
  protected Class<?>[] getServletConfigClasses() {
    return new Class[] {};
  }

  @Override
  protected String[] getServletMappings() {
    return new String[] { "/" };
  }

  @Override
  protected Filter[] getServletFilters() {

    return new Filter[] { characterEncodingFilter(), siteMeshFilter(), httpServletRequestFilter(), localeFilter(), new HiddenHttpMethodFilter(),
        xssFilter() };
  }

  private Filter localeFilter() {
    return new LocaleFilter();
  }

  private Filter reconnectDelegateFilter() {
    return new DelegatingFilterProxy("apiExceptionHandler");
  }

  private CharacterEncodingFilter characterEncodingFilter() {
    CharacterEncodingFilter characterEncodingFilter = new CharacterEncodingFilter();
    characterEncodingFilter.setEncoding("UTF-8");
    characterEncodingFilter.setForceEncoding(true);
    return characterEncodingFilter;
  }

  private SiteMeshFilter siteMeshFilter() {
    return new SiteMeshFilter();
  }

  private Filter httpServletRequestFilter() {
    return new HttpServletRequestFilter();
  }

  private XSSFilter xssFilter() {
    return new XSSFilter();
  }

}
