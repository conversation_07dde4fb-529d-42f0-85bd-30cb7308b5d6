$(function() {
  'use strict';
  $('#modal-contact-form').on('show.bs.modal', function() {
  });

  $('#modal-contact-form').on('hidden.bs.modal', function(e) {
    document.location.reload();
  });

  var options = {
    url: 'save',
    type: 'post',
    dataType: 'json',
    clearForm: false,
    beforeSubmit: beforeSubmitForm,
    success: function(data) {
      console.log('success');
      if (data.resultCode == '100') {
        alert('Saved');
        $('#modal-contact-form').modal('hide');
        document.location.reload();
      } else {
        alert('Save Fails..:' + data.message);
      }
    }

  };
  $('#contactForm').ajaxForm(options);

  function beforeSubmitForm() {
    return true;
  }

  $('#btn-save').on('click', function() {
    $('#contactForm').submit();
  });

  $('#btn-searchOperator').on('click', function() {
    searchOperator();
  });

  $('#searchString').keypress(function(event) {
    var keycode = (event.keyCode ? event.keyCode : event.which);
    if (keycode == '13') {
      searchOperator();
      event.preventDefault();
      return false;
    }
  });

  function searchOperator() {
    $('#user-tbody').html('');
    var searchWord = $('#searchString').val();
    console.log('searchWord:', searchWord);
    if (!searchWord) {
      alert('Input Search Word!');
      return false;
    } else {
      $.ajax({
        url: '/lookup/operatorSearch',
        type: 'POST',
        data: {'search.searchword': searchWord, 'search.serviceSite': 'SMS'},
        dataType: 'json'
      }).done(function(data) {
        if (data.list.length > 0) {
          $.each(data.list, function(i, admin) {
            var templateRow = $('#user-row-template tbody').html();
            templateRow = templateRow.replace(/%adminId%/g, admin.id);
            templateRow = templateRow.replace(/%username%/g, admin.username);
            templateRow = templateRow.replace(/%name%/g, admin.fullName);
            $('#user-tbody').append(templateRow);
          });
        }
      });
    }

  }

});

