$(function() {

  function listSubmit() {
    $('#listVO').submit();
  }

  $('#pageSize').on('change', function() {
    listSubmit();
  });

  $('#searchword').keypress(function(event) {
    var keycode = (event.keyCode ? event.keyCode : event.which);
    if (keycode == '13') {
      searchUser();
      event.preventDefault();
      return false;
    }
  });

  $('#search-btn').on('click', function() {
    searchUser();
  });

  function openSearchUserForm() {
    if ($('#searchword').val() == '') {
      alert('Input SearchText!');
    } else {
      $('#modal-search-user-form #searchWord').val($('#searchword').val());
      $('#modal-search-user-form').modal('show');
    }
  }

  function searchUser() {
    $('#user-tbody').html('');
    var searchWord = $('#searchword').val();
    console.log('searchWord:', searchWord);
    if (!searchWord) {
      alert('Input Search Word!');
      return false;
    } else {
      $.ajax({
        url: '/lookup/userSearch',
        type: 'POST',
        data: {'keyword': searchWord},
        dataType: 'json'
      }).done(function(data) {
        if (data.list.length > 1) {
          $.each(data.list, function(i, item) {
            var templateRow = $('#user-row-template tbody').html();
            templateRow = templateRow.replace(/%userId%/g, item.userId);
            templateRow = templateRow.replace(/%email%/g, item.email);
            templateRow = templateRow.replace(/%name%/g, item.name ? item.name : '');
            templateRow = templateRow.replace(/%mobile%/g, item.mobile ? item.mobile : '');
            console.log('item.subscriber_id:', item.subscriber_id);
            templateRow = templateRow.replace(/%subId%/g, item.subscriber_id != '' ? item.subscriber_id : '');
            $('#user-tbody').append(templateRow);
          });
          openSearchUserForm();
        } else if (data.list.length == 1) {
          $('#userId').val(data.list[0].userId);
          listSubmit();
        } else {
          $('#userId').val('');
          listSubmit();
        }

      });
    }

  }

  $('#excelExportLink').on('click', function(e) {
    var params = $('#listVO').serialize();
    document.location.href = 'export?' + params;
  });

});
