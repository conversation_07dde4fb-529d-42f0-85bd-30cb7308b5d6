$(function() {

  function listSubmit() {
    $('#listVO').submit();
  }

  $('#pageSize, #search-cardgroup-list, #search-status-list').on('change',
      function() {
        listSubmit();
      });

  $('#search-btn').on('click', function() {
    listSubmit();
  });


  $('#excelExportLink').on('click', function(e) {
    var params = $('#listVO').serialize();
    document.location.href = 'export?' + params;
  });

  $('#checkAll').click(function() {
    $(':checkbox[name="prepaidCardGroupIds"]').prop('checked', this.checked);
  });

  $('#new-create-form').on('click', function(e) {
    event.preventDefault();
    $.getJSON('form', function(data) {
    });
    $('#modal-card-group-form').modal('show');
  });

  $('a.cardgroup-id').on('click', function(event) {
    event.preventDefault();
    var cardgroupid = $(this).data('cardgroupid');
    $.getJSON('get/' + cardgroupid, function(cardGroup) {
      $('#cardGroupForm input[name=id]').val(cardGroup.id);
      $('#cardGroupForm input[name=name]').val(cardGroup.name);
      $('#cardGroupForm input[name=price]').val(cardGroup.price);
      $('#cardGroupForm select[name=enabled]').val(cardGroup.enabled + '');
    });

    $('#modal-card-group-form').modal('show');
  });

  $('#cardgroup-delete-btn').on('click', function() {
    if ($('.checkbox').is(':checked')) {
      $.post('delete', $('#listVO').serialize()).done(function(data) {
        if (data.resultCode == '100') {
          document.location.reload();
          return true;
        } else if (data.resultCode == '991') {
          alert('Type can not be deleted! Maybe referenced by Prepaid Card!');
        } else {
          alert('Delete Fails..:' + data.message);
        }
      });

    } else {
      alert('Select Card Types To Delete!');
    }
  });

});
