$(function() {

  function listSubmit() {
    $('#listVO').submit();
  }

  $('#pageSize, #search-agent-list, #search-solve-list').on('change', function() {
    listSubmit();
  });

  $('#search-btn').on('click', function() {
    listSubmit();
  });

  $('#checkAll').click(function() {
    $(':checkbox[name="csIds"]').prop('checked', this.checked);
  });

  $('#solved-btn').on('click', function() {
    if ($('.checkbox').is(':checked')) {
      $.post('solve', $('#listVO').serialize()).done(function(data) {
        if (data.resultCode == '100') {
          document.location.reload();
          return true;
        } else {
          alert('Solve Fails..:' + data.message);
        }
      });

    } else {
      alert('Select A/S To Solve!');
    }
  });

  $('a.cs-id').on('click', function(event) {
    event.preventDefault();
    var csId = $(this).data('csid');
    $.getJSON('get/' + csId, function(cs) {
      $('#csForm #csIds').val(cs.id);
      $('#csForm #userName').text(cs.user.name);
      $('#csForm #mobile').text(cs.user.mobile);
      $('#csForm #email').text(cs.user.email);
      $('#csForm #subId').text(cs.user.subId);
      $('#csForm #address').val(cs.user.address);

      if (cs.solved == true) {
        $('#csForm input[name=solved]:radio[value="true"]').attr("checked", true);
      } else {
        $('#csForm input[name=solved]:radio[value="false"]').attr("checked", true);
      }

      if (cs.attachedFileUrl) {
        $('#cs-attached').attr('src', cs.attachedFileUrl);
      }

      $('#csForm #content').val(cs.content);
      if (cs.user.ownedBoxLabels.length > 0) {
        var deviceLabel = '';
        $.each(cs.user.ownedBoxLabels, function(i, item) {
          deviceLabel += item + '\n' ;
        });
        $('#device').val(deviceLabel);
      }
      $('#csForm textarea').attr('readonly', 'readonly').prop('disabled', true);
    });

    $('#modal-cs-form').modal('show');
  });



});
