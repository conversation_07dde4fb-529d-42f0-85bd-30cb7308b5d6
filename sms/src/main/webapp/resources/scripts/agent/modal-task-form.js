var taskForm = (function($, win) {
  'use strict';
  $('#modal-task-form').on('show.bs.modal', function() {
  });

  $('#modal-task-form').on('hidden.bs.modal', function(e) {
    document.location.reload();
  });

  var options = {
    url: 'save',
    type: 'post',
    dataType: 'json',
    clearForm: false,
    beforeSubmit: beforeSubmitForm,
    success: function(data) {
      if (data.resultCode == '100') {
        alert('Saved');
        $('#modal-task-form').modal('hide');
        ;
        document.location.reload();
      } else {
        alert('Create Fails..:' + data.message);
      }

    },
    errorClass: 'form-validation-error',
    highlight: function(element, errorClass, validClass) {
      $(element).parent().addClass('has-error');
    },
    unhighlight: function(element, errorClass, validClass) {
      $(element).parent().removeClass('has-error');
    }
  };

  $('#taskForm').ajaxForm(options);

  function beforeSubmitForm() {
    return true;
  }

  $('#save-btn').on('click', function(){
    $('#taskForm').submit();
  })

}(jQuery, this));
