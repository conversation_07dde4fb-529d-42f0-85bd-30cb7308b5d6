$(function() {

  function listSubmit() {
    $('#userListVO').submit();
  }

  $('#pageSize, #search-agent-list, #search-status-list').on('change',
      function() {
        listSubmit();
      });

  $('#search-btn').on('click', function() {
    listSubmit();
  });

  $('table').first().find('th.sortable').on('click', function() {
    console.log($(this).index());

    if ($(this).hasClass('click-disabled')) {
      return;
    }

    if ($(this).hasClass('name-sort')) {
      $('#sortColumn').val('NAME');
    } else if ($(this).hasClass('updated-dt-sort')) {
      $('#sortColumn').val('UPDATED_DT');
    }

    var order = '';
    if ($(this).hasClass('sorting')) {
      order = 'DESC';
    } else if ($(this).hasClass('sorting_asc')) {
      order = 'DESC';
    } else if ($(this).hasClass('sorting_desc')) {
      order = 'ASC';
    }
    $('#ascendingOrder').val(order);
    listSubmit();

  });

  $('#checkAll').click(function() {
    $(':checkbox[name="userIds"]').prop('checked', this.checked);
  });

  $('#new-create-form').on('click', function(e) {
    event.preventDefault();
    $.getJSON('form', function(data) {
      $('#userForm #tvsSubId').val(data.tvsSubId);
      $('#modal-user-form').modal('show');
    }).fail(function () {
      console.log('failed.');
    });

  })

  $('#checkAll').on('click', function() {
    $('.checkbox').prop('checked', this.checked);
  });

  $('a.user-id').on(
      'click',
      function(event) {
        event.preventDefault();
        var userId = $(this).data('userid');
        $.getJSON('../user/get/' + userId, function(user) {
          $('#userForm #name').val(user.name);
          $('#userForm #id').val(user.id);
          $('#userForm #subId').val(user.subId);
          $('#userForm #tvsSubId').val(user.tvsSubId);
          $('#userForm #mobile').val(user.mobile);
          $('#userForm #email').val(user.email).attr('readonly', true);
          $('#userForm #address').val(user.address);
          if (user.agent) {
            $('#userForm #agentId').val(user.agent.id);
            // $('#userForm #agentId').prop('disabled', true);
          }
          $('#userForm #csStatus').val(user.csStatus);
          $('#userForm #enabled').val(user.enabled + '');
          $('#userForm #unlimitedDeviceRegister').val(user.unlimitedDeviceRegister + '');
          if (user.ownedBoxLabels.length > 0) {
            $.each(user.ownedBoxLabels, function(i, item) {
              var template = '<input type="text" class="form-control input-sm width300 top5" value="%value%" readonly' +
                  ' />';
              template = template.replace(/%value%/g, item);
              $('#deviceList').append(template);
            });
          }

          if (user.deviceList.length > 0) {
            $.each(user.deviceList, function(i, device) {
              if (device && device.id) {
                var template = '<div class="form-inline top5"><input type="text" class="form-control input-sm' +
                    ' width300"' +
                    ' value="%value%"' +
                    ' readonly />' +
                    ' <button' +
                    ' type="button" class="btn btn-default btn-xs btn-device-delete" data-deviceid="%deviceId%"' +
                    ' data-userid="%userId%">X</button></div>';
                template = template.replace(/%value%/g, device.deviceModel);
                template = template.replace(/%deviceId%/g, device.id);
                template = template.replace(/%userId%/g, user.id);
                $('#deviceList').append(template);
              }

            });
          }

          if (user.userBalance) {
            $('#userForm #userBalance').val(
                numberWithCommas(user.userBalance.balance));
          }
          //
          if (user.id != '' && user.id != undefined) {
            $('#userForm #id').attr('readonly', true);
          }

          $('#userForm #city').val(user.city);
          $('#userForm #country').val(user.country);
          $('#userForm #state').val(user.state);
          $('#userForm #zipCode').val(user.zipCode);
          $('#userForm #tel').val(user.tel);
        });

        $('#modal-user-form').modal('show');
      });

  $('#user-status-change-btn').on('click', function() {
    if ($('.checkbox').is(':checked')) {
      if (!confirm('Are you sure to change Activation?'))
        return;

      $.post('activation', $('#userListVO').serialize()).done(function(data) {
        if (data.resultCode == '100') {
          alert('Activation change Success!');
          document.location.reload();
          return true;
        } else {
          alert('Activation change Fails..:' + data.message);
        }
      });

    } else {
      alert('Select Users To change Activation!');
    }
  });

  $('#task-create-btn').on('click', function() {
    if ($('.checkbox').is(':checked')) {
      if ($('select[name=agentTask]').val() == '') {
        alert('Select Task Type!');
        $('select[name=agentTask]').focus();
      } else {
        if (!confirm('Are you sure to create Task?'))
          return;

        $.post('createTask', $('#userListVO').serialize()).done(function(data) {
          if (data.resultCode == '100') {
            alert('Task creation Success!');
            document.location.reload();
            return true;
          } else {
            alert('Task creation Fails..:' + data.message);
          }
        });
      }
    } else {
      alert('Select Users To create Task!');
    }
  });

  $('#user-assign-agent-btn').on('click', function() {
    var agentId = $('#assign-agent-list').val();
    if (agentId) {
      if ($('.checkbox').is(':checked')) {
        $.post('agentAssign', $('#userListVO').serialize()).done(function(data) {
          if (data.resultCode == '100') {
            document.location.reload();
            return true;
          } else {
            alert('Allocate Fails..:' + data.message);
          }
        });

      } else {
        alert('Select Device To Assign!');
      }
    } else {
      alert('Select Agent!');
    }
  });
  $('#user-unassign-agent-btn').on('click', function() {
    if ($('.checkbox').is(':checked')) {
      $.post('removeAgentAssign', $('#userListVO').serialize()).done(function(data) {
        if (data.resultCode == '100') {
          document.location.reload();
          return true;
        } else {
          alert('DeAllocate Fails..:' + data.message);
        }
      });

    } else {
      alert('Select Device To DeAllocate!');
    }
  });

  $('#excelExportLink').on('click', function(e) {
    var params = $('#userListVO').serialize();
    document.location.href = 'export?' + params;
  });


  $(document).on('click', 'button.btn-device-delete', function() {
    var userId = $(this).data('userid');
    var deviceId = $(this).data('deviceid');
    if (confirm('Are you sure to delete this device?')) {
      $.ajax({
        async: false,
        url: '/user/device_delete?userId=' + userId + '&deviceId=' + deviceId,
        type: 'GET',
        dataType: 'json'
      }).done(function(data) {
        if (data.resultCode === '100') {
          alert('Delete Complete!');
          document.location.reload();
        } else {
          alert('Failed to delete!');
        }
      });
    }

  });


});
