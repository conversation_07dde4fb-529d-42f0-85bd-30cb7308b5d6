$(function (){


  $('#btn-create').on('click', function (){
    event.preventDefault();
    $('#modal-studio-form').modal('show');
  });

  $('#btn-delete').on('click', function (){
    if ($(".checkbox").is(':checked')) {

      if (!confirm('Are you sure to delete selected Studios?'))
        return;

      $.ajax({
        url: 'delete',
        traditional: true,
        type: 'POST',
        data: $('#listVO input[name="ids"]').serialize(),
        dataType: 'json'
      }).done(function (data){
        if (data.resultCode != '100') {
          alert(data.message);
          return;
        }
        document.location.reload();
      });
    } else {
      alert('Select Studio To Delete...');
    }
  });

  $('#checkAll').on('click', function (){
    $('.checkbox').prop('checked', this.checked);
  });


});