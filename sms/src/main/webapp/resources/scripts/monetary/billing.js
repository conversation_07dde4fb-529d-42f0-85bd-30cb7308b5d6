$(function() {
  $('#pageSize, #purchaseType').on('change', function() {
    listSubmit();
  });

  $('#search-btn').on('click', function() {
    listSubmit();
  });

  $('table').first().find('th').on('click', function() {
    console.log($(this).index());

    if ($(this).hasClass('click-disabled')) {
      return;
    }

    if ($(this).hasClass('user-email-sort')) {
      $('#sortColumn').val('USER_EMAIL');
    } else if ($(this).hasClass('buy-dt-sort')) {
      $('#sortColumn').val('BUY_DT');
    }

    var order = '';
    if ($(this).hasClass('sorting')) {
      order = 'DESC';
    } else if ($(this).hasClass('sorting_asc')) {
      order = 'DESC';
    } else if ($(this).hasClass('sorting_desc')) {
      order = 'ASC';
    }
    $('#ascendingOrder').val(order);
    listSubmit();
  });

  function listSubmit() {
    $('#listVO').submit();
  }

});
