$(function() {

  function listSubmit() {
    $('#listVO').submit();
  }

  $('#pageSize').on('change', function() {
    listSubmit();
  });

  $('#search-btn').on('click', function() {
    listSubmit();
  });


  $('#checkAll').click(function() {
    $(':checkbox[name="boxModelIds"]').prop('checked', this.checked);
  });

  $('#create-btn').on('click', function(e) {
    event.preventDefault();
    $.getJSON('form', function(data) {
    });
    $('#modal-boxmodel-form').modal('show');
  });

  $('a.box-model-id').on('click', function(event) {
    event.preventDefault();
    var modelId = $(this).data('boxmodelid');
    $.getJSON('get/' + modelId, function(box) {
      $('#boxModelForm input[name=id]').val(box.id);
      $('#boxModelForm select[name=enabled]').val(box.enabled + '');
    });

    $('#boxModelForm input[name=id]').prop('disabled', true);
    $('#modal-boxmodel-form').modal('show');
  });

  $('#delete-btn').on('click', function() {
    if ($('.checkbox').is(':checked')) {
      $.post('delete', $('#listVO').serialize()).done(function(data) {
        if (data.resultCode == '100') {
          document.location.reload();
          return true;
        } else if (data.resultCode == '309') {
          alert('Models Assigned To Box can not be Deleted!');
        } else {
          alert('Delete Fails..:' + data.message);
        }
      });

    } else {
      alert('Select Models To Delete!');
    }
  });

});
