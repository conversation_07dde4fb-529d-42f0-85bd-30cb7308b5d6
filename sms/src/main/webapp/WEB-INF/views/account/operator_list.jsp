<!DOCTYPE html>
<%@ include file="/common/common.jsp" %>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8" %>

<head>
  <title>Operator</title>
</head>
<div class="row">
  <div class="col-md-12">
    <h3 class="page-header">Account - Operator</h3>
  </div>
</div>
<form:form modelAttribute="listVO" method="get" action="list">
  <div class="row">
    <div class="col-md-4">
      <div class="form-group input-group">
        <form:input path="search.searchword" class="form-control input-sm" placeholder="ID or Name"/>
        <span class="input-group-btn">
          <button class="btn btn-default btn-sm" type="button" id="search-btn">
            <i class="fa fa-search"></i>
          </button>
        </span>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-inline">
        <form:select path="search.agentId" class="form-control input-sm" id="search-agent-list">
          <form:option value="">-- Agent --</form:option>
          <form:options items="${agentList}" itemLabel="name" itemValue="id" />
        </form:select>
      </div>
    </div>
    <div class="col-md-3">
    </div>
    <div class="col-md-1">
      <div class="form-inline pull-right">
        <form:select path="pageSize" cssClass="form-control input-sm">
          <form:options items="${listVO.pageSizes }" itemValue="value" itemLabel="label"/>
        </form:select>
      </div>
    </div>

    <!-- list -->
    <div class="col-md-12">
      <div class="table-responsive">
        <table class="table table-bordered table-hover table-striped">
          <thead>
          <tr>
            <th class="text-center click-disabled" style="width: 60px;">
              <input type="checkbox" id="checkAll" >
            </th>
            <th class="text-center ">Name</th>
            <th class="text-center ">ID</th>
            <th class="text-center click-disabled">Role</th>
            <th class="text-center click-disabled">Agent</th>
            <th class="text-center click-disabled">Timezone</th>
            <th class="text-center click-disabled">Enabled</th>
            <th class="text-center ">Updated</th>
            <th class="text-center ">Updated By</th>
          </tr>
          </thead>
          <tbody>
          <c:forEach var="admin" items="${listVO.list }" varStatus="status">
            <tr class="odd gradeX">
              <td align="center" class="vertical-middle">
                <input type="checkbox" class="checkbox" name="adminIds" value="${admin.id }" />
              </td>
              <td class="text-center">${admin.fullName }</td>
              <td class="text-center"><a href="#" class="admin-id" data-adminid="${admin.id}">${admin.username }</a></td>
              <td class="text-center">${admin.role.name}</td>
              <td class="text-center">
                <c:if test="${admin.agent ne null}">${admin.agent.name}</c:if>
              </td>
              <td class="text-center">
                ${admin.timezone}
              </td>
              <td class="text-center">
                <c:set var="enabledLabel" value="${admin.enabled ? 'primary' : 'danger'}"/>
                <span class="label label-${enabledLabel}">${admin.enabled}</span>
              </td>
              <td class="text-center"><fmt:formatDate value="${admin.updatedDt }" pattern="${dateTimePattern }" timeZone="${sessionTimezone }"/></td>
              <td class="text-center"><ui:adminUsername userId="${admin.updatedBy}" /></td>
            </tr>
          </c:forEach>
          </tbody>
        </table>
      </div>

    </div>

    <div class="col-md-12 text-center">
      <ui:pagination name="${listVO}"/>
    </div>

  </div>

  <!-- /.col-lg-12 -->
  <div class="row">
    <div class="col-md-4">
    </div>
    <div class="col-md-8">
      <div class="pull-right">
        <a class="btn btn-custom-default btn-sm" id="create-btn">New</a>
        <a class="btn btn-custom-default btn-sm" id="delete-btn">Delete</a>
      </div>
    </div>
  </div>
</form:form>

<!-- /.row -->
<!-- Page-Level Demo Scripts - Tables - Use for reference -->

<script>

</script>
<jsp:include page="modal-admin-form.jsp" />
<script src="${RESOURCE_SERVER_URL}/resources/scripts/account/operator_list.js?vs=${timestamp}"></script>




