<!DOCTYPE html>
<%@ include file="/common/common.jsp" %>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8" %>
<link href="${RESOURCE_SERVER_URL}/resources/bootstrap/css/plugins/tag/bootstrap-tag-cloud.css?vs=${timestamp}"
      rel="stylesheet">

<div class="modal fade" id="modal-stb-pairing" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true" style="display: none;">
    <div class="modal-dialog" style="width:800px">
        <form id="rollingTextForm">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="myModalLabel">STB Pairing</h4>
                </div>
                <div class="modal-body">
                    <p>Paired STBs</p>
                    <div class="tableFixHead">
                        <table class="table table-bordered table-hover table-striped" id="modal-table">
                            <thead>
                            <tr>
                                <th class="text-center click-disabled" style="width: 60px;">
                                    <input type="checkbox" id="checkAll">
                                </th>
                                <th class="text-center click-disabled">Serial</th>
                                <th class="text-center click-disabled">MAC</th>
                                <th class="text-center click-disabled"></th>
                            </tr>
                            </thead>
                            <tbody class="table-body">

                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" id="unpair-device-btn" class="btn btn-danger">Unpair Other Devices</button>
                    <button type="button" id="unpair-btn" class="btn btn-danger">Unpair Selected STB</button>
                    <button type="button" id="pair-btn" class="btn btn-info" data-dismiss="modal">Pair New STB</button>
                    <button type="button" id="multiple-pair-btn" class="btn btn-info" data-dismiss="modal">Pair Multiple STB</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <%--                    <input  type="submit" class="btn btn-primary" value="Save"/>--%>
                </div>
                <input type="hidden" id="hiddenUserId" value="">
                <input type="hidden" id="hiddenEmail" value="">
            </div>
            <!-- /.modal-content -->
        </form>
    </div>
</div>