<!DOCTYPE html>
<%@ include file="/common/common.jsp"%>
<%@ page pageEncoding="UTF-8" contentType="text/html;charset=utf-8"%>

<head>
<title>CS - List</title>
</head>
<div class="row">
  <div class="col-md-12">
    <h3 class="page-header">CS - List</h3>
  </div>
</div>

<form:form modelAttribute="listVO" method="get" action="list">
  <div class="row">
    <div class="col-md-6">
      <div class="form-group input-group">
        <form:input path="search.searchword" class="form-control input-sm" placeholder="Input email, name, mobile"/>
        <span class="input-group-btn">
          <button class="btn btn-default btn-sm" type="submit">
            <i class="fa fa-search"></i>
          </button>
        </span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="form-inline pull-right">
        Show :
        <form:select path="pageSize" cssClass="form-control input-sm">
          <form:options items="${listVO.pageSizes }" itemValue="value" itemLabel="label"/>
        </form:select>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12">
      <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
          <thead>
            <tr>
              <th class="text-center" style="width: 60px;"><input type="checkbox" class="checkbox" value="" id="checkAll" style="margin-left: 35%;"></th>
              <th class="text-center">CS Title</th>
              <th class="text-center">Agent</th>
              <th class="text-center">User Name</th>
              <th class="text-center">User Mobile</th>
              <th class="text-center">Type</th>
              <th class="text-center">Status</th>
              <th class="text-center">Operator</th>
              <th class="text-center">Received</th>
              <th class="text-center">Solved</th>
            </tr>
          </thead>
          <tbody>
            <c:forEach var="cs" items="${listVO.list }" varStatus="status">
            <tr class="odd gradeX">
              <td align="center" class="vertical-middle"><input type="checkbox" class="checkbox" name="csIds" value="${cs.id }"></td>
              <td class="text-center"><a href="#" class="cs-id" data-csid="${cs.id}">${cs.title} </a>&nbsp;
                <c:if test="${cs.attachedFileName ne null && cs.attachedFileName ne ''}">
                  <span class="glyphicon glyphicon-paperclip"></span>
                </c:if>
              </td>
              <td class="text-center">${cs.agentName}</td>
              <td class="text-center">${cs.user.name}</td>
              <td class="text-center">${cs.user.mobile}</td>
              <td class="text-center"><ui:code code="${cs.csType}" codeCategory="${CODECATEGORY_CsType}" /></td>
              <td class="text-center">
                <c:choose>
                  <c:when test="${cs.solved}"><span class="label label-primary">Solved</span></c:when>
                  <c:otherwise><span class="label label-default">Assigned</span></c:otherwise>
                </c:choose>
              </td>
              <td class="text-center">${cs.operator.username}</td>
              <td class="text-center"><fmt:formatDate value="${cs.receivedDt }" pattern="${dateTimePattern }"  timeZone="${sessionTimezone }"/></td>
              <td class="text-center"><fmt:formatDate value="${cs.solvedDt }" pattern="${dateTimePattern }"  timeZone="${sessionTimezone }"/></td>
            </tr>
            </c:forEach>
          </tbody>
        </table>
      </div>

    </div>
    <div class="col-md-12">
      <div class="row">
        <div class="col-md-2">
        </div>
        <div class="col-md-6">
        </div>
        <div class="col-md-4">
          <div class="pull-right">
            <a class="btn btn-custom-default btn-sm" id="solved-btn">Solve</a>
            <a class="btn btn-custom-default btn-sm" id="create-btn">Create</a>
            <a class="btn btn-custom-default btn-sm" id="delete-btn">Delete</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</form:form>
<jsp:include page="modal-cs-form.jsp"/>
<script src="${RESOURCE_SERVER_URL}/resources/scripts/cs/cs-list.js?vs=${timestamp}"></script>
