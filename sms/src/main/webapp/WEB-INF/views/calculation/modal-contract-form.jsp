<!DOCTYPE html>
<%@ include file="/common/common.jsp" %>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8" %>
<link href="${RESOURCE_SERVER_URL}/resources/bootstrap/css/plugins/tag/bootstrap-tag-cloud.css?vs=${timestamp}" rel="stylesheet">
<div class="modal fade" id="modal-contract-form" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true" style="display: none;">
  <div class="modal-dialog">
    <form id="contractForm">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
          <h4 class="modal-title" id="myModalLabel">Contract</h4>
        </div>
        <div class="modal-body">

          <div class="row" style="padding-bottom: 10px;">
            <div class="col-md-3 text-right"><label>Studio</label></div>
            <div class="col-md-9">
              <select id="studio.id" name="studio.id" class="form-control input-sm">
                <c:forEach var="data" items="${studioList }">
                  <option value="${data.id }">${data.name }</option>
                </c:forEach>
              </select>
            </div>
          </div>

          <div class="row">
            <div class="col-md-3 text-right"><label>Type</label></div>
            <div class="col-md-9">
              <select id="libraryLicenseType" name="libraryLicenseType" class="form-control input-sm">
                <c:forEach var="data" items="${libraryLicenseTypes }">
                  <option value="${data }">${data }</option>
                </c:forEach>
              </select>
            </div>
          </div>

          <div class="row top10">
            <div class="col-md-3 text-right"><label>MG</label></div>
            <div class="col-md-9">
              <div class="input-group">
                <input id="mg" name="mg" class="form-control input-sm width150"/>
                <span class="input-group-addon" id="basic-addon2">USD</span>
              </div>
            </div>
          </div>

          <div class="row top10 svod-only-form">
            <div class="col-md-3 text-right"><label>PPM</label></div>
            <div class="col-md-9">
              <div class="input-group">
                <input id="ppm" name="ppm" class="form-control input-sm width100"/>
                <span class="input-group-addon" id="basic-addon2">USD</span>
              </div>
            </div>
          </div>

          <div class="row tvod-only-form top10">
            <div class="col-md-3 text-right"><label>Per Title MG</label></div>
            <div class="col-md-9">
              <div class="form-inline">
                <input type="checkbox" id="perTitleMg" name="perTitleMg" class="checkbox"/>
                <label style="color:#a2a2a2; font-weight: normal;" for="perTitleMg">Check this if this contract is per title MG based.</label>
              </div>
            </div>
          </div>

          <div class="row tvod-only-form">
            <div class="col-md-3 text-right"><label>RS</label></div>
            <div class="col-md-9">
              <div class="form-inline">
                <ui:codelist codeCategory="${CODECATEGORY_TvodLicenseType }" cssClass="form-control" name="select-rs" id="select-rs"
                             prompt="==select=="/>
                <input type="text" id="rsAmount" name="rsAmount" class="form-control input-sm width100" placeholder="Amount" value=""/>
                <button type="button" class="btn btn-sm btn-default btn-add-rs">Add</button>
              </div>
              <ul id="contract-rs-tag" class="tag-cloud-info"></ul>
            </div>
          </div>

          <div class="row tvod-only-form">
            <div class="col-md-3 text-right"><label>Deemed Price</label></div>
            <div class="col-md-9">
              <div class="form-inline">
                <ui:codelist codeCategory="${CODECATEGORY_TvodLicenseType }" cssClass="form-control" name="select-dp" id="select-dp"
                             prompt="==select=="/>
                <input type="text" id="dpAmount" name="dpAmount" class="form-control input-sm width100" placeholder="Amount" value=""/>
                <button type="button" class="btn btn-sm btn-default btn-add-dp">Add</button>
              </div>
              <div class="form-group">
                <ul id="contract-dp-tag" class="tag-cloud-info">
                </ul>
              </div>
            </div>
          </div>

          <div class="row top10">
            <div class="col-md-3 text-right"><label>Duration</label></div>
            <div class="col-md-9">
              <div class="form-inline">
                <input id="start" name="start" class="form-control input-sm datepicker width150" placeholder="${datePattern }"
                       data-format="${datePattern }"/> -
                <input id="end" name="end" class="form-control input-sm datepicker width150" placeholder="${datePattern }"
                       data-format="${datePattern }"/>
              </div>
            </div>
          </div>

          <div class="row top10">
            <div class="col-md-3 text-right"><label>Payment Due</label></div>
            <div class="col-md-9">
              <input id="paymentDue" name="paymentDue" class="form-control input-sm datepicker width150" placeholder="${datePattern }"
                     data-format="${datePattern }"/>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <input type="submit" class="btn btn-primary" value="Save"/>
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
      <!-- /.modal-content -->
    </form>
  </div>
</div>
<script src="${RESOURCE_SERVER_URL}/resources/scripts/calculation/modal-contract-form.js?vs=${timestamp}"></script>



