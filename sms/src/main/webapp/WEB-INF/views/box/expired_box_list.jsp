<!DOCTYPE html>
<%@ include file="/common/common.jsp" %>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8" %>

<head>
    <title>User</title>
    <style>
        table.sorting-table thead .sorting {
            background: url('/resources/images/bootstrap/sort_both.png') no-repeat center right;
        }

        table.sorting-table thead .sorting_asc {
            background: url('/resources/images/bootstrap/sort_asc.png') no-repeat center right;
        }

        table.sorting-table thead .sorting_desc {
            background: url('/resources/images/bootstrap/sort_desc.png') no-repeat center right;
        }

    </style>
</head>
<div class="row">
    <div class="col-md-12">
        <h3 class="page-header">NOT SUBSCRIBED USER LIST</h3>
    </div>
</div>

<div class="modal fade" id="sync-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true" style="display: none;">
    <div class="modal-dialog" style="display: flex;align-items: center;min-height: calc(100% - (0.5rem * 2));">
        <div class="modal-content" style="width: 100%;">
            <div class="modal-body" style="text-align: center;">
                <div class="form-inline" style="margin: 0px 90px">

                    <h4 id="sync-msg" style="display: inline;" id="loader"><i class="fa fa-spin fa-spinner"></i> ... Loading SUS data</h4>
                </div>
            </div>
        </div>
    </div>
</div>

<form:form modelAttribute="listVO" method="get" action="expiredActiveBox">
    <div class="row">
            <%--        <div class="col-md-4">--%>
            <%--            <div class="form-inline">--%>
            <%--                <label>Select Date Range</label>--%>
            <%--                <fmt:formatDate value="${listVO.search.searchDtFrom }" pattern="yyyy/MM/dd"--%>
            <%--                                timeZone="${sessionTimezone }"--%>
            <%--                                var="formattedCreatedDtFrom"/>--%>
            <%--                <fmt:formatDate value="${listVO.search.searchDtTo }" pattern="yyyy/MM/dd" timeZone="${sessionTimezone }"--%>
            <%--                                var="formattedCreatedDtTo"/>--%>
            <%--                <div class="form-group">--%>
            <%--                    <input type="text" class="form-control form-inline input-sm datepicker width100"--%>
            <%--                           name="search.searchDtFrom" data-date-end-date="0d"--%>
            <%--                           placeholder="Date From" readonly="readonly" value="${formattedCreatedDtFrom }">--%>
            <%--                </div>--%>
            <%--                <div class="form-group">~</div>--%>
            <%--                <div class="form-group">--%>
            <%--                    <input type="text" class="form-control form-inline input-sm datepicker width100"--%>
            <%--                           name="search.searchDtTo" data-date-end-date="0d"--%>
            <%--                           placeholder="Date To" readonly="readonly" value="${formattedCreatedDtTo }">--%>
            <%--                </div>--%>
            <%--            </div>--%>

            <%--        </div>--%>
        <div class="col-md-10">
            <div class="form-inline">
                <form:input path="search.schText" class="form-control input-sm width250"
                            placeholder="Input email, name, mobile, mac, serial"/>

                <form:select path="search.numberOfDaya" class="form-control input-sm">
                    <form:option value="0">-Activated Within-</form:option>
                    <form:option value="-3">3 Days</form:option>
                    <form:option value="-7">7 Days</form:option>
                    <form:option value="-30">30 Days</form:option>
                </form:select>

                <form:select path="search.platformId" class="form-control input-sm">
                    <form:option value="">-- Platform --</form:option>
                    <form:options items="${platforms}" itemLabel="name" itemValue="id"/>
                </form:select>

                <button class="btn btn-default btn-sm" type="button" id="search-btn">
                    <i class="fa fa-search"></i>
                </button>
<%--                <img src="/resources/images/loading/loading.gif" alt="loading..."--%>
<%--                     style="display: none; width: 20px; margin: 0px 15px;" id="sync-loader">--%>
<%--                <h4 id="sync-msg" style="display: none;" id="loader">Syncing data with tvsus</h4>--%>
            </div>
        </div>
            <%--        <div class="col-md-2"></div>--%>
        <div class="col-md-1">
            <a id="excelExportLink" href="#" class="btn btn-default btn-sm">Export to Excel</a>
        </div>
        <div class="col-md-1">
            <div class="form-inline pull-right">
                <form:select path="pageSize" cssClass="form-control input-sm">
                    <form:options items="${listVO.pageSizes }" itemValue="value" itemLabel="label"/>
                </form:select>
            </div>
        </div>

        <div class="col-md-12 top15">
            <div class="table-responsive">
                <table class="table table-bordered table-hover table-striped">
                    <thead>
                    <tr>
                        <th class="text-center click-disabled">Name</th>
                        <th class="text-center click-disabled">City</th>
                        <th class="text-center click-disabled">State</th>
                        <th class="text-center click-disabled">Country</th>
                        <th class="text-center click-disabled">Email</th>
                        <th class="text-center click-disabled">Mobile</th>
                        <th class="text-center click-disabled">Serial</th>
                        <th class="text-center click-disabled">MAC</th>
                        <th class="text-center click-disabled">Subscriber ID</th>
                        <th class="text-center click-disabled">Customer ID</th>
                        <th class="text-center click-disabled">Expired From</th>
                        <th class="text-center click-disabled">Agent</th>
                        <th class="text-center click-disabled">Activated</th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:forEach var="list" items="${listVO.list}" varStatus="status">
                        <tr class="odd gradeX">
                            <td class="text-center">${list.userName}
                                    <%--                                <c:if test="${list.box.user ne null}">${list.box.user.username}</c:if>--%>
                            </td>
                            <td class="text-center">${list.city}</td>
                            <td class="text-center">${list.state}</td>
                            <td class="text-center">${list.country}</td>
                            <td class="text-center">${list.email}</td>
                            <td class="text-center">${list.mobile}</td>
                            <td class="text-center">${list.serial}</td>
                            <td class="text-center">${list.macAddress}</td>
                            <td class="text-center">${list.subId}</td>
                            <td class="text-center">${list.cusId}</td>
                            <td class="text-center">
                                <c:choose>
                                    <c:when test="${list.expiredDate ne null}">
                                        <fmt:formatDate value="${list.expiredDate}"
                                                        pattern="${dateTimePattern }"
                                                        timeZone="${sessionTimezone }"/>
                                    </c:when>
                                    <c:otherwise>Not Found</c:otherwise>
                                </c:choose>


                            </td>
                            <td class="text-center">${list.agent}</td>
                            <td class="text-center">${list.activated}</td>
                        </tr>
                    </c:forEach>
                    </tbody>
                </table>
            </div>

        </div>

        <div class="col-md-12 text-center">
            <ui:pagination name="${listVO}"/>
        </div>

    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="form-inline pull-left">
                <a class="btn btn-custom-default btn-sm" id="reload-sus-data-btn">Reload SUS Data</a>
            </div>
        </div>
    </div>

</form:form>
<script>


</script>
<script src="${RESOURCE_SERVER_URL}/resources/scripts/box/expired_box_list.js?vs=${timestamp}"></script>





