<!DOCTYPE html>
<%@ include file="/common/common.jsp"%>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8"%>

<head>
<title>Toggle Free User</title>
<style>
</style>
</head>

<div class="row">
  <div class="col-md-12">
    <h4 class="page-header">Toggle Free User</h4>
  </div>
</div>
<form:form modelAttribute="userListVO" method="get" action="free_user">
  <div class="row">
    <div class="col-md-9">
      <div class="form-group input-group">
        <form:input path="searchword" class="form-control input-sm" placeholder="search email, name, mobile" />
        <span class="input-group-btn">
          <button class="btn btn-default btn-sm" type="button" id="search-btn">
            <i class="fa fa-search"></i>
          </button>
        </span>
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-inline pull-right">
        <form:select path="pageSize" cssClass="form-control input-sm">
          <form:options items="${userListVO.pageSizes }" itemValue="value" itemLabel="label" />
        </form:select>
      </div>
    </div>

    <!-- list -->
    <div class="col-md-12">
      <div class="table-responsive">
        <form:hidden path="sortColumn" />
        <form:hidden path="ascendingOrder" />

        <c:set var="emailSortClass" value="sorting" />
        <c:set var="firstNameSortClass" value="sorting" />
        <c:set var="lastNameSortClass" value="sorting" />
        <c:set var="createdDtSortClass" value="sorting" />

        <c:if test="${userListVO.sortColumn eq 'EMAIL' }">
          <c:choose>
            <c:when test="${userListVO.ascendingOrder eq 'DESC' }">
              <c:set var="emailSortClass" value="sorting_desc" />
            </c:when>
            <c:otherwise>
              <c:set var="emailSortClass" value="sorting_asc" />
            </c:otherwise>
          </c:choose>
        </c:if>
        <c:if test="${userListVO.sortColumn eq 'FIRST_NAME' }">
          <c:choose>
            <c:when test="${userListVO.ascendingOrder eq 'DESC' }">
              <c:set var="firstNameSortClass" value="sorting_desc" />
            </c:when>
            <c:otherwise>
              <c:set var="firstNameSortClass" value="sorting_asc" />
            </c:otherwise>
          </c:choose>
        </c:if>
        <c:if test="${userListVO.sortColumn eq 'LAST_NAME' }">
          <c:choose>
            <c:when test="${userListVO.ascendingOrder eq 'DESC' }">
              <c:set var="lastNameSortClass" value="sorting_desc" />
            </c:when>
            <c:otherwise>
              <c:set var="lastNameSortClass" value="sorting_asc" />
            </c:otherwise>
          </c:choose>
        </c:if>
        <c:if test="${userListVO.sortColumn eq 'CREATED_DT' }">
          <c:choose>
            <c:when test="${userListVO.ascendingOrder eq 'DESC' }">
              <c:set var="createdDtSortClass" value="sorting_desc" />
            </c:when>
            <c:otherwise>
              <c:set var="createdDtSortClass" value="sorting_asc" />
            </c:otherwise>
          </c:choose>
        </c:if>
        <table class="table table-bordered table-hover sorting-table">
          <thead>
            <tr>
              <th class="text-center click-disabled" style="width: 60px;"><input type="checkbox" class="checkbox" value=""
                id="checkAll" style="margin-left: 35%;"></th>
              <th class="text-center ${emailSortClass } email-sort sortable">Email</th>
              <th class="text-center ${firstNameSortClass } first-name-sort sortable col-sm-2">First Name</th>
              <th class="text-center ${lastNameSortClass } last-name-sort sortable col-sm-2">Last Name</th>
              <th class="text-center col-sm-2 click-disabled">Mobile</th>
              <th class="text-center col-sm-2 ${createdDtSortClass } created-dt-sort sortable">Created Date</th>
            </tr>
          </thead>
          <tbody>
            <c:forEach var="user" items="${userListVO.list }" varStatus="status">
              <tr class="odd gradeX">
                <td align="center" class="vertical-middle"><input type="checkbox" class="checkbox" name="userIds"
                  value="${user.id }"></td>
                <td class="text-center">
                  ${user.email }
                  <c:if test="${user.freeUser }">
                    <span class="label label-danger">free</span>
                  </c:if>
                </td>
                <td class="text-center">${user.firstName}</td>
                <td class="text-center">${user.lastName}</td>
                <td class="text-center">${user.mobile}</td>
                <td class="text-center"><fmt:formatDate value="${user.createdDt }" pattern="${dateTimePattern }"
                    timeZone="${sessionTimezone }" /></td>
              </tr>
            </c:forEach>
          </tbody>
        </table>
      </div>

    </div>

    <div class="col-md-12 text-center">
      <ui:pagination name="${userListVO}"></ui:pagination>
    </div>

    <div class="col-md-12" style="margin-bottom: 50px;">
      <div class="pull-right">
        <button type="button" class="btn btn-custom-default btn-sm" id="save-btn">Toggle Free User</button>
      </div>
    </div>

  </div>
</form:form>
<!-- /.row -->
<!-- Page-Level Demo Scripts - Tables - Use for reference -->

<script>

</script>
<script src="${RESOURCE_SERVER_URL}/resources/scripts/monetary/free_user.js?vs=${timestamp}"></script>
<jsp:include page="left_menu.jsp" />




