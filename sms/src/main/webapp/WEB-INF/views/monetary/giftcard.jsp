<!DOCTYPE html>
<%@ include file="/common/common.jsp"%>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8"%>
<head>
<title>GiftCard Log</title>
</head>

<div class="row">
  <div class="col-md-12">
    <h3 class="page-header">GiftCard Log</h3>
  </div>
</div>
<form:form modelAttribute="listVO" method="get" action="giftcard">
  <div class="row">
    <div class="col-md-8">
      <div class="pull-left">
        <fmt:formatDate value="${listVO.searchStartDt }" pattern="yyyy/MM/dd" timeZone="${sessionTimezone }"
          var="formattedStartDt" />
        <fmt:formatDate value="${listVO.searchEndDt }" pattern="yyyy/MM/dd" timeZone="${sessionTimezone }" var="formattedEndDt" />
        <div class="row">
          <div class="form-inline col-xs-12">
            <div class="form-group">
              <input type="text" class="form-control form-inline input-sm datepicker" name="searchStartDt"
                placeholder="Start Date" style="width: 150px;" readonly="readonly" value="${formattedStartDt }">
            </div>
            <div class="form-group">~</div>
            <div class="form-group">
              <input type="text" class="form-control form-inline input-sm datepicker" name="searchEndDt" placeholder="End Date"
                style="width: 150px;" readonly="readonly" value="${formattedEndDt }">
            </div>
            <button class="btn btn-default btn-sm" type="submit" id="search-btn">
              <i class="fa fa-search"></i>
            </button>
            <a href="giftcard-excel?${userGiftCardSearchQuery }" class="btn btn-default btn-sm">Export To Excel</a>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-inline pull-right">
        Show :
        <form:select path="pageSize" class="form-control input-sm">
          <form:option value="10">10</form:option>
          <form:option value="15">15</form:option>
          <form:option value="20">20</form:option>
          <form:option value="50">50</form:option>
          <form:option value="100">100</form:option>
        </form:select>
      </div>
    </div>
  </div>

  <div class="top5"></div>
  <div class="row">
    <div class="col-md-12">
      <div class="table-responsive">
        <form:hidden path="sortColumn" />
        <form:hidden path="ascendingOrder" />

        <c:set var="userEmailSortClass" value="sorting" />
        <c:if test="${listVO.sortColumn eq 'USER_EMAIL' }">
          <c:set var="userEmailSortClass" value="${listVO.ascendingOrder eq 'DESC' ? 'sorting_desc' :'sorting_asc' }" />
        </c:if>
        <c:set var="typeSortClass" value="sorting" />
        <c:if test="${listVO.sortColumn eq 'TYPE' }">
          <c:set var="typeSortClass" value="${listVO.ascendingOrder eq 'DESC' ? 'sorting_desc' :'sorting_asc' }" />
        </c:if>

        <c:set var="registerDtSortClass" value="sorting" />
        <c:if test="${listVO.sortColumn eq 'REGISTER_DT' }">
          <c:set var="registerDtSortClass" value="${listVO.ascendingOrder eq 'DESC' ? 'sorting_desc' :'sorting_asc' }" />
        </c:if>

        <table class="table table-striped table-bordered table-hover sorting-table">
          <thead>
            <tr>
              <th class="text-center col-sm-1 click-disabled">#</th>
              <th class="text-center ${userEmailSortClass } user-email-sort">User Email</th>
              <th class="text-center ${typeSortClass } type-sort">Type</th>
              <th class="text-center click-disabled">Amount(<spring:eval expression="@environment['currency.unit']" />)
              </th>
              <th class="text-center click-disabled">Original(<spring:eval expression="@environment['currency.unit']" />)
              </th>
              <th class="text-center click-disabled">Promotion(<spring:eval expression="@environment['currency.unit']" />)
              </th>
              <th class="text-center click-disabled">Card Number</th>
              <th class="text-center ${registerDtSortClass } register-dt-sort">Date</th>
            </tr>
          </thead>
          <tbody>

            <c:forEach var="item" items="${listVO.list }" varStatus="status">
              <tr class="odd gradeX">
                <td class="text-center">${listVO.fullListSize - (listVO.firstRecordIndex + status.count - 1)}</td>
                <td class="text-center">${item.userEmail }</td>
                <td class="text-center">${item.giftCardName }</td>
                <td class="text-center"><fmt:formatNumber type="number" pattern="#,##0" value="${item.price }" /></td>
                <td class="text-center"><fmt:formatNumber type="number" pattern="#,##0" value="${item.originalPrice }" /></td>
                <td class="text-center"><fmt:formatNumber type="number" pattern="#,##0" value="${item.promotionPrice }" /></td>
                <td class="text-center">${item.cardNumber }</td>
                <td class="text-center"><fmt:formatDate value="${item.registerDt }" pattern="yyyy/MM/dd HH:mm:ss"
                    timeZone="${sessionTimezone }" /></td>
              </tr>
            </c:forEach>
          </tbody>
        </table>
      </div>
      <div class="text-center">
        <ui:pagination name="${listVO}"></ui:pagination>
      </div>

    </div>
  </div>

</form:form>

<jsp:include page="left_menu.jsp" />
<script src="${RESOURCE_SERVER_URL}/resources/scripts/monetary/giftcard.js?vs=${timestamp}"></script>



