<!DOCTYPE html>
<%@ include file="/common/common.jsp" %>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8" %>
<head>
  <title>Sale</title>
  <link href="${RESOURCE_SERVER_URL}/resources/bootstrap/css/plugins/morris/morris-0.4.3.min.css?vs=${timestamp}" rel="stylesheet">
</head>

<div class="row" style="padding-top: 30px;">
  <div class="col-md-12">
    <ul class="nav nav-tabs">
      <li class="active text-center text-bold"><a href="#" class="cursor_pointer width150">Sales</a></li>
      <li class="text-center"><a href="sales_by_title" class="width150">Title</a></li>
    </ul>
  </div>
</div>

<form:form modelAttribute="listVO" method="get" action="sales">
  <div class="row top10">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <!-- /.panel-heading -->
        <div class="panel-body">
          <div class="row">
            <div class="form-inline col-xs-12">
              <div class="form-group">
<%--                <form:select path="platformId" class="form-control input-sm">--%>
<%--                  <form:option value="">- Platform -</form:option>--%>
<%--                  <form:options items="${platforms }" itemLabel="label" itemValue="value"/>--%>
<%--                </form:select>--%>
              </div>
              <div class="form-group">
                <form:select path="searchCondition" cssClass="form-control input-sm width150">
                  <form:option value="">==Select==</form:option>
                  <form:option value="USERNAME">User Email</form:option>
                  <form:option value="VOD_NAME">ProductName</form:option>
                </form:select>
              </div>
              <div class="form-group">
                <form:input path="searchWord" cssClass="form-control input-sm width300" placeholder="Search Word"/>
              </div>
            </div>
          </div>
          <div class="top10"></div>
          <fmt:formatDate value="${listVO.searchStartDt }" pattern="yyyy/MM/dd" timeZone="${sessionTimezone }" var="formattedStartDt"/>
          <fmt:formatDate value="${listVO.searchEndDt }" pattern="yyyy/MM/dd" timeZone="${sessionTimezone }" var="formattedEndDt"/>
          <div class="row">
            <div class="form-inline col-xs-12">
              <div class="form-group">
                <input type="text" class="form-control form-inline input-sm datepicker" name="searchStartDt" placeholder="Start Date"
                       style="width: 150px;" readonly="readonly" value="${formattedStartDt }">
              </div>
              <div class="form-group">~</div>
              <div class="form-group">
                <input type="text" class="form-control form-inline input-sm datepicker" name="searchEndDt" placeholder="End Date"
                       style="width: 150px;" readonly="readonly" value="${formattedEndDt }">
              </div>
              <button class="btn btn-default btn-sm" type="submit">
                <i class="fa fa-search"></i>
              </button>
              <a href="sales-excel?${salesSearchQuery }" class="btn btn-default btn-sm">Export To Excel</a>
            </div>
          </div>
        </div>
        <!-- /.panel-body -->
      </div>
      <!-- /.panel -->
    </div>
  </div>
  <div class="row">
    <div class="col-md-12">
      <div class="panel panel-default">
        <div class="panel-heading float-right">Total Sales by Daily</div>
        <div class="panel-body">
          <div id="line-chart" style="position: relative; height: 200px;"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-6">
      <div class="pull-left top10">
        <label><spring:eval expression="@environment['currency.unit']"/> : ${listVO.sum }</label>
      </div>
    </div>
    <div class="col-md-6">
      <div class="form-inline pull-right">
        Show :
        <form:select path="pageSize" class="form-control input-sm">
          <form:option value="10">10</form:option>
          <form:option value="15">15</form:option>
          <form:option value="20">20</form:option>
          <form:option value="50">50</form:option>
          <form:option value="100">100</form:option>
        </form:select>
      </div>
    </div>
  </div>
</form:form>
<div class="top5"></div>
<div class="row">
  <div class="col-md-12">
    <div class="table-responsive">
      <table class="table table-striped table-bordered table-hover sorting-table">
        <thead>
        <tr>
          <th class="text-center col-sm-1">#</th>
          <th class="text-center col-sm-2">User</th>
          <th class="text-center">Product</th>
          <th class="text-center col-sm-1">Price</th>
          <th class="text-center col-sm-2">Payment</th>
          <th class="text-center col-sm-2">Sale Date</th>
        </tr>
        </thead>
        <tbody>

        <c:forEach var="item" items="${listVO.list }" varStatus="status">
          <tr class="odd gradeX">
            <td class="text-center">${status.index + 1 }</td>
            <td class="text-center">${item.username }</td>
            <td class="text-center">${item.vodName }</td>
            <td class="text-center">${item.price }</td>
            <td class="text-center">${item.paymentType }</td>
            <td class="text-center"><fmt:formatDate value="${item.buyDt }" pattern="yyyy/MM/dd HH:mm:ss" timeZone="${sessionTimezone }"/></td>
          </tr>
        </c:forEach>
        </tbody>
      </table>
    </div>
    <div class="text-center">
      <ui:pagination name="${listVO}"></ui:pagination>
    </div>

  </div>
</div>
<!-- Page-Level Plugin Scripts - Morris -->
<script src="${RESOURCE_SERVER_URL}/resources/bootstrap/js/plugins/morris/raphael-2.1.0.min.js?vs=${timestamp}"></script>
<script src="${RESOURCE_SERVER_URL}/resources/bootstrap/js/plugins/morris/morris.js?vs=${timestamp}"></script>
<script>
  var statsVO = JSON.parse('${statsVOJsonString}');
  console.log(statsVO.list);
  Morris.Line({
    element: 'line-chart',
    data: statsVO.list,
    xkey: 'id',
    ykeys: ['sum'],
    labels: ['Sales']
  });
</script>

<jsp:include page="left_menu.jsp"/>
<script src="${RESOURCE_SERVER_URL}/resources/scripts/monetary/sales.js?vs=${timestamp}"></script>



