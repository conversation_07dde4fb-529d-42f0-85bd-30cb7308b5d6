<!DOCTYPE html>
<%@ include file="/common/common.jsp" %>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8" %>

<head>
  <title>User</title>
  <style>
    table.sorting-table thead .sorting {
      background: url('/resources/images/bootstrap/sort_both.png') no-repeat center right;
    }

    table.sorting-table thead .sorting_asc {
      background: url('/resources/images/bootstrap/sort_asc.png') no-repeat center right;
    }

    table.sorting-table thead .sorting_desc {
      background: url('/resources/images/bootstrap/sort_desc.png') no-repeat center right;
    }

    .nav-tabs > li > a, .nav-tabs > li > a:hover, .nav-tabs > li > a:focus {
      color: #0365C0;
      font-size: 15px;
      text-align: center;
    }

    .nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
      color: #53585F;
      cursor: default;
      border: 1px solid #dddddd;
      border-bottom-color: transparent;
      font-size: 15px;
      font-weight: bold;
      text-align: center;
      font-weight: bold;
    }

    #thumbType {
      color: #53585F;
    }
  </style>
</head>
<div class="row">
  <div class="col-md-12">
    <h3 class="page-header">User List</h3>
  </div>
</div>
<form:form modelAttribute="userListVO" method="get" action="list">
  <div class="row">
    <div class="col-md-4">
      <div class="form-group input-group">
        <form:input path="search.searchword" class="form-control input-sm" placeholder="Input email, name, mobile"/>
        <span class="input-group-btn">
          <button class="btn btn-default btn-sm" type="button" id="search-btn">
            <i class="fa fa-search"></i>
          </button>
        </span>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-inline">
        <form:select path="search.agentId" class="form-control input-sm" id="search-agent-list">
          <form:option value="">-- Agent --</form:option>
          <form:options items="${agentList}" itemLabel="name" itemValue="id" />
        </form:select>
        <form:select path="search.enabled" class="form-control input-sm" id="search-status-list">
          <form:option value="">-- Activated --</form:option>
          <form:option value="true">Yes</form:option>
          <form:option value="false">No</form:option>
        </form:select>
      </div>
    </div>
    <div class="col-md-3">
      <a id="excelExportLink" href="#" class="btn btn-default btn-sm">Export Excel</a>
      <a href="#modal-excel-form" class="btn btn-default btn-sm" data-toggle="modal">Import Excel</a>
      <a href="#modal-import-user-agent" class="btn btn-default btn-sm" data-toggle="modal">Import Agent</a>
    </div>
    <div class="col-md-1">
      <div class="form-inline pull-right">
        <form:select path="pageSize" cssClass="form-control input-sm">
          <form:options items="${userListVO.pageSizes }" itemValue="value" itemLabel="label"/>
        </form:select>
      </div>
    </div>

    <!-- list -->
    <div class="col-md-12">
      <div class="table-responsive">
        <form:hidden path="search.sortColumn"/>
        <form:hidden path="ascendingOrder"/>

        <c:set var="nameSortClass" value="sorting"/>
        <c:set var="updatedDtSortClass" value="sorting"/>

        <c:if test="${userListVO.search.sortColumn eq 'NAME' }">
          <c:choose>
            <c:when test="${userListVO.ascendingOrder eq 'DESC' }">
              <c:set var="nameSortClass" value="sorting_desc"/>
            </c:when>
            <c:otherwise>
              <c:set var="nameSortClass" value="sorting_asc"/>
            </c:otherwise>
          </c:choose>
        </c:if>
        <c:if test="${userListVO.search.sortColumn eq 'UPDATED_DT' }">
          <c:choose>
            <c:when test="${userListVO.ascendingOrder eq 'DESC' }">
              <c:set var="updatedDtSortClass" value="sorting_desc"/>
            </c:when>
            <c:otherwise>
              <c:set var="updatedDtSortClass" value="sorting_asc"/>
            </c:otherwise>
          </c:choose>
        </c:if>
        <table class="table table-bordered table-hover table-striped sorting-table">
          <thead>
          <tr>
            <th class="text-center click-disabled" style="width: 60px;"><input type="checkbox" class="checkbox" value="" id="checkAll"
                                                                               style="margin-left: 35%;"></th>
            <th class="text-center ${nameSortClass } name-sort sortable">Name</th>
            <th class="text-center click-disabled">City</th>
            <th class="text-center click-disabled">State</th>
            <th class="text-center click-disabled">Country</th>
            <th class="text-center click-disabled">Email</th>
            <th class="text-center click-disabled">Mobile</th>
            <th class="text-center click-disabled">Subscriber ID</th>
            <th class="text-center click-disabled">Customer ID</th>
            <th class="text-center click-disabled">STB</th>
            <th class="text-center click-disabled">Others</th>
            <th class="text-center ${updatedDtSortClass } updated-dt-sort sortable">Updated</th>
            <th class="text-center click-disabled">Agent</th>
<%--            <th class="text-center click-disabled">CS</th>--%>
            <th class="text-center click-disabled">Activated</th>
          </tr>
          </thead>
          <tbody>
          <c:forEach var="user" items="${userListVO.list }" varStatus="status">
            <tr class="odd gradeX context-menu">
              <td align="center"
                  class="vertical-middle"><input type="checkbox" class="checkbox" name="userIds" value="${user.id }" data-name="${user.name}"
                                                 data-email="${user.email}" data-boxcount="${fn:length(user.boxes)}"></td>
              <td class="text-center"><a href="#" class="user-id" data-userid="${user.id}">${user.name }</a></td>
              <td class="text-center"> ${not empty user.deviceIP ? user.deviceIP.userCity : ''}</td>
              <td class="text-center"> ${not empty user.deviceIP ? user.deviceIP.userState : ''}</td>
              <td class="text-center"> ${not empty user.deviceIP ? user.deviceIP.userCountry : ''}</td>
              <td class="text-center">${user.email}</td>
              <td class="text-center">${user.mobile}</td>
              <td class="text-center">${user.subId}</td>
              <td class="text-center">${user.tvsSubId}</td>
              <td class="text-center">${fn:length(user.boxes)}</td>
              <td class="text-center">${fn:length(user.userDevices)}</td>
              <td class="text-center"><fmt:formatDate value="${user.updatedDt }" pattern="${dateTimePattern }" timeZone="${sessionTimezone }"/></td>
              <td class="text-center">${user.agent.name}</td>
<%--              <td class="text-center">--%>
<%--                <c:if test="${user.csAssignedCount > 0}">--%>
<%--                  <a href="/cs/list?search.userId=${user.id}" >${user.csSolvedCount}/${user.csAssignedCount}</a>--%>
<%--                </c:if>--%>
<%--              </td>--%>
              <td class="text-center">${user.enabled}</td>
            </tr>
          </c:forEach>
          </tbody>
        </table>
      </div>

    </div>

    <div class="col-md-12 text-center">
      <ui:pagination name="${userListVO}"/>
    </div>

  </div>

  <!-- /.col-lg-12 -->
  <div class="row">
    <div class="col-md-10">
      <div class="form-inline pull-left">
        <select name="agentId" id="assign-agent-list" class="form-control input-sm">
          <option value="">--Agent--</option>
          <c:forEach var="agent" items="${agentList}">
            <option value="${agent.id}">${agent.name}</option>
          </c:forEach>
        </select>
        <a class="btn btn-custom-default btn-sm" id="user-assign-agent-btn">Allocate</a>
        <a class="btn btn-custom-default btn-sm" id="user-unassign-agent-btn">DeAllocate</a>

        <select id="user-status-change-select" name="enabled" class="form-control input-sm">
          <option value="">--Activation--</option>
          <option value="true">Yes</option>
          <option value="false">No</option>
        </select>
        <a class="btn btn-custom-default btn-sm" id="user-status-change-btn">Apply</a>

        <select name="agentTask" class="form-control input-sm left10">
          <option value="">-- Task --</option>
          <option value="1">Installation</option>
          <option value="2">Removal</option>
        </select>
        <button type="button" id="task-create-btn" class="btn btn-custom-default btn-sm">Task Create</button>
      </div>
    </div>

    <div class="col-md-2">
      <div class="pull-right">
        <a class="btn btn-custom-default btn-sm" id="new-create-form">New</a>
        <%--<a class="btn btn-custom-default btn-sm delete">Delete</a>--%>
      </div>
    </div>
  </div>
</form:form>
<!-- /.row -->
<!-- Page-Level Demo Scripts - Tables - Use for reference -->
<script>

</script>
<jsp:include page="modal-user-form.jsp" />
<jsp:include page="modal-user-excel-form.jsp" />
<jsp:include page="modal-import-user-agent-form.jsp" />
<jsp:include page="modal_import_user_agent_result.jsp" />
<script src="${RESOURCE_SERVER_URL}/resources/scripts/user/user_list.js?vs=${timestamp}"></script>




