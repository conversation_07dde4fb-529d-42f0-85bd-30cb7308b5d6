<!DOCTYPE html>
<%@ include file="/common/common.jsp"%>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8"%>
<style>
  #excelForm a {
    text-decoration: underline;
  }
</style>
<div class="modal fade" id="modal-import-user-agent" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  aria-hidden="true" style="display: none;">
  <div class="modal-dialog">
    <form id="import-user-agent-form">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
          <h4 class="modal-title" id="myModalLabel">Import User Agent</h4>
        </div>
        <div class="modal-body">
          <div class="alert alert-success">
            Import User Agent.<br>
            Download import form <a href="/user/import-agent-form"><u><b>here</b></u></a>!<br>
          </div>
          <div class="top10"></div>
          <div class="form-group">
            <label>Upload</label> <input type="file" name="file" />
          </div>

        </div>
        <div class="modal-footer form-inline">
          <img class="loading" style="display:none;" src="/resources/images/loading/loading.gif" width=34 height=34> <span class="loading" style="display:none;" >processing...</span>
          <input type="button" id="import-user-agent-button" class="btn btn-primary save" value="Save" />
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
      <!-- /.modal-content -->
    </form>
  </div>
  <!-- /.modal-dialog -->
</div>


<script>
  $(document).ready(function() {

    const optionsExcelUserAgentImport = {
      url: '/user/import-agent',
      type: 'post',
      dataType: 'json',
      clearForm: false,
      beforeSubmit: function () {
        $('.loading').show();
      },
      success: function (data) {
        $('.loading').hide();
        if (data.resultCode === "100") {
          $('#modal_import_user_agent_result').modal({
            show: true,
            backdrop: 'static',
            keyboard: true
          });
          $('#modal_import_user_agent_result #message').html(data.message);
        } else {
          alert("Import Fails : " + data.message);
        }
        $('#modal-import-user-agent').modal('hide');
      },
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        alert("Status: " + textStatus);
        alert("Error: " + errorThrown);
        $('.loading').hide();
      }
    };
    $('#import-user-agent-form').ajaxForm(optionsExcelUserAgentImport);

    $('#import-user-agent-button').click(function () {
      $('#import-user-agent-form').submit();
    });
  });
</script>





