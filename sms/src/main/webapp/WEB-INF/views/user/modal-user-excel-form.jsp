<!DOCTYPE html>
<%@ include file="/common/common.jsp"%>
<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8"%>
<style>
  #excelForm a {
    text-decoration: underline;
  }
</style>
<div class="modal fade" id="modal-excel-form" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  aria-hidden="true" style="display: none;">
  <div class="modal-dialog">
    <form id="excelForm">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
          <h4 class="modal-title" id="myModalLabel">Import Excel</h4>
        </div>
        <div class="modal-body">
          <div class="alert alert-success">
            Import User.<br>
            Download import form <a href="importForm">here</a>!<br>
          </div>
          <div class="top10"></div>
          <div class="form-group">
            <label>Upload</label> <input type="file" name="file" />
          </div>

        </div>
        <div class="modal-footer form-inline">
          <img class="loading" style="display:none;" src="/resources/images/loading/loading.gif" width=34 height=34> <span class="loading" style="display:none;" >processing...</span>
          <input type="submit" class="btn btn-primary save" value="Save" />
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
      <!-- /.modal-content -->
    </form>
  </div>
  <!-- /.modal-dialog -->
</div>
<script>
  $(document).ready(function() {

    var options = {
      url : '/user/import',
      type : 'post',
      dataType : 'json',
      clearForm : false,
      beforeSubmit : function() {
        $('.loading').show();
      },
      success : function(data) {
        console.log(data.resultCode);
        if (data.resultCode == "100") {
          alert("Success!");
          $('.loading').hide();

        } else {
          alert("Import Fails : " + data.message);
          $('.loading').hide();
          return false;
        }
        document.location.reload();
      }
    };

    $('#excelForm').ajaxForm(options);
  });
</script>





