<%@ page language="java" pageEncoding="UTF-8" contentType="text/html;charset=utf-8" %>
<%@ include file="/common/taglibs.jsp" %>

<link href="${resourceServerUrl}/resources/css/menu.css?vs=${timestamp}" rel="stylesheet" type="text/css">

<div class="alert alert-danger custom-alert alert-dismissible" role="alert" style="display: none;" >
  <h4><strong>403 Access Denied!</strong></h4>
  You are not authorized for this action
</div>

<div id='cssmenu'>
  <div class="logo">
    <img class="navbar-logo" src="${resourceServerUrl}/resources/images/logo_tvstorm.png" style="max-height: 50px; margin-top: 10px;
      margin-right: 15px;">
  </div>
  <ul>
    <c:forEach var="menu" items="${topMenus}" varStatus="status">
      <c:set var="selected" value="${menu.id eq SELECTED_MENU_ID ? 'selected' : ''}"/>
      <li>
        <a class="${selected}" data-uri="${menu.uri}" data-menuid="${menu.id}"><span>${menu.name}</span></a>
        <c:if test="${fn:length(menu.childMenus) > 0}">
          <ul>
            <c:forEach var="subMenu" items="${menu.childMenus}">
              <li><a data-uri="${subMenu.uri}" data-menuid="${menu.id}"><span>${subMenu.name}</span></a></li>
            </c:forEach>
          </ul>
        </c:if>
      </li>
    </c:forEach>
  </ul>
  <ul class="pull-right" style="margin-right: 30px;">
    <li><a href="#"><i class="glyphicon glyphicon-cog"></i></a>
      <ul>
        <li><a href="/logout"><span>Logout</span></a></li>
      </ul>
    </li>
  </ul>
</div>

<script>
  $(function () {
    $('#cssmenu a').on('click', function () {
      var topmenuId = $(this).data('menuid');
      if (topmenuId) {
        var uri = $(this).data('uri');
        location.href = uri;
      } else {
        location.href = $(this).attr('href');
      }
    })
  });

</script>



