package com.laive.sms.config;

import com.laive.core.base.ResponseVO;
import com.laive.core.web.view.CustomMappingJacksonJsonView;
import com.laive.core.web.view.DownloadView;
import com.laive.core.web.view.MappingJacksonJsonpView;
import org.springframework.context.annotation.Bean;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.web.servlet.view.xml.MarshallingView;

public class ViewConfig {
  @Bean
  public CustomMappingJacksonJsonView jsonView() {
    return new CustomMappingJacksonJsonView();
  }

  @Bean
  public MappingJacksonJsonpView jsonpView() {
    return new MappingJacksonJsonpView();
  }

  @Bean
  public DownloadView downloadView() {
    return new DownloadView();
  }

  @Bean
  public MarshallingView xmlView() {
    MarshallingView xmlView = new MarshallingView();
    Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
    marshaller.setClassesToBeBound(ResponseVO.class);
    xmlView.setMarshaller(marshaller);
    return xmlView;
  }

}
