package com.laive.sms.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springmodules.validation.commons.DefaultValidatorFactory;

@Configuration
public class ValidatorConfig {
  
  @Bean
  public DefaultValidatorFactory validatorFactory() {
    DefaultValidatorFactory validatorFactory = new DefaultValidatorFactory();
    PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    List<Resource> resources = new ArrayList<Resource>();
    resources.add(resolver.getResource("classpath:validator-config.xml"));
    resources.add(resolver.getResource("classpath:validator-rules.xml"));
    resources.add(resolver.getResource("classpath:validator-rules-custom.xml"));
    validatorFactory.setValidationConfigLocations(resources.toArray(new Resource[0]));
    return validatorFactory;
  }

}
