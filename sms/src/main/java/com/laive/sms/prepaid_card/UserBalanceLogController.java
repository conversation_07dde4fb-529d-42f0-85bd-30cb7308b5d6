package com.laive.sms.prepaid_card;

import java.text.SimpleDateFormat;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.laive.core.base.BaseController;
import com.laive.core.repository.UserBalanceLogRepository;
import com.laive.core.service.UserManager;
import com.laive.core.util.DateUtil;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.UserBalanceLogListVO;
import com.laive.sms.common.Menu;

@Controller
@RequestMapping("/user_balance_log")
public class UserBalanceLogController extends BaseController {

  @Autowired
  private UserBalanceLogRepository userBalanceLogRepository;

  @Autowired
  private UserManager userManager;

  @InitBinder
  public void initBinder(WebDataBinder binder) {
    SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.getDatePattern(LocaleContextHolder.getLocale()));
    binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
  }

  @PreAuthorize("hasPermission('Prepaid', 'READ')")
  @RequestMapping(value = "/list", method = RequestMethod.GET)
  public String list(UserBalanceLogListVO listVO, Model model, HttpServletRequest request) {
    if (StringUtils.isNotBlank(listVO.getUserId())) {
      request.getSession().setAttribute("userBalanceLogSearchQuery", request.getQueryString());
      listVO = userBalanceLogRepository.getListVO(listVO);
      listVO.setUser(userManager.get(listVO.getUserId()));
    }
    model.addAttribute("listVO", listVO);
    return "prepaid_card/user_balance_log_list";
  }

}
