package com.laive.sms.billing;

import java.util.Date;

import lombok.extern.slf4j.Slf4j;

import org.junit.Ignore;
import org.junit.Test;
import org.omg.PortableServer.POA;
import org.springframework.beans.factory.annotation.Autowired;

import com.laive.core.util.DateUtil;
import com.laive.sms.base.BaseControllerTestCase;

@Slf4j
public class BillingControllerTest extends BaseControllerTestCase {

  @Autowired
  private BillingController controller;

  @Test
  public void testBillingAdjust() {
    Date now = new Date();
    Date buyDt = DateUtil.addDays(now, -10);
    Date expireDt = DateUtil.addDays(buyDt, 30);
    Double price = 5200d;

    Long leftDay = DateUtil.getDateDiffDay(expireDt, now);
    log.debug("leftDay:{}", leftDay);
    Long originalDay = DateUtil.getDateDiffDay(expireDt, buyDt);
    log.debug("originalDay:{}", originalDay);

    Double leftPrice = (price * leftDay) / originalDay;
    log.debug("leftPrice:{}", leftPrice);

    double discard = Double.parseDouble(String.format("%.0f",leftPrice));
    log.debug("leftPrice:{}", discard);
  }

  @Ignore
  @Test
  public void testAdjust() {
    String paymentId = "11";
    String priceId = "3";
    String paymentType = "CA";
    controller.adjustSave(paymentId, priceId, paymentType);
    flushAndClear();
  }

}